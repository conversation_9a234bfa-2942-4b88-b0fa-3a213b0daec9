@echo off
echo 🚀 STARTING COMPLETE LICENSE SYSTEM...
echo.

echo 📊 Starting MongoDB (if not running)...
start "MongoDB" cmd /k "mongod --dbpath C:\data\db"
timeout /t 3

echo 🔧 Starting License Server (Port 5002)...
start "License Server" cmd /k "cd license-server && npm start"
timeout /t 3

echo 🌐 Starting Website Frontend (Port 3000)...
start "Website" cmd /k "cd web-frontend && npm start"
timeout /t 3

echo.
echo ✅ SYSTEM STARTED SUCCESSFULLY!
echo.
echo 📋 SERVICES RUNNING:
echo    🗄️  MongoDB: mongodb://localhost:27017
echo    🔧 License Server: http://localhost:5002
echo    🌐 Website: http://localhost:3000
echo.
echo 🔑 TEST LICENSE KEY: IAT-MBIQQMOV-11F4DB70C4756BAC
echo 📧 TEST EMAIL: <EMAIL>
echo.
echo 💡 Open http://localhost:3000 to access the website
echo 💡 Download software from the website to test license system
echo.
pause
