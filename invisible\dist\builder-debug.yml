x64:
  firstOrDefaultFilePatterns:
    - '!build{,/**/*}'
    - '!dist{,/**/*}'
    - main.js
    - index.html
    - preload.js
    - package.json
    - '!**/node_modules/**'
    - node_modules/dotenv/**/*
    - screenshots/.gitkeep
    - temp/.gitkeep
    - cache/.gitkeep
    - '!screenshots/*.png'
    - '!screenshots/*.jpg'
    - '!screenshots/*.jpeg'
    - '!temp/*'
    - '!cache/*'
    - '!dist/**/*'
    - '!.git/**/*'
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,obj,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,pnpm-lock.yaml,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - main.js
    - index.html
    - preload.js
    - package.json
    - node_modules/dotenv/**/*
    - screenshots/.gitkeep
    - temp/.gitkeep
    - cache/.gitkeep
    - '!screenshots/*.png'
    - '!screenshots/*.jpg'
    - '!screenshots/*.jpeg'
    - '!temp/*'
    - '!cache/*'
    - '!dist/**/*'
    - '!.git/**/*'
