const express = require('express');
const { verifyToken } = require('../middleware/auth');
const License = require('../models/License');
const Payment = require('../models/Payment');
const User = require('../models/User');

const router = express.Router();

// 📊 GET DASHBOARD DATA
router.get('/dashboard', verifyToken, async (req, res) => {
    try {
        const user = await User.findById(req.userId);

        // Get user's licenses
        const licenses = await License.find({ userId: req.userId });
        const activeLicenses = licenses.filter(license => license.isValid());

        // Get recent payments
        const recentPayments = await Payment.find({ userId: req.userId })
            .sort({ createdAt: -1 })
            .limit(10);

        // Calculate stats
        const totalScreenshotsUsed = user.totalScreenshotsUsed || 0;
        const totalSpent = recentPayments
            .filter(payment => payment.status === 'completed')
            .reduce((sum, payment) => sum + payment.amount, 0);

        res.json({
            success: true,
            data: {
                totalScreenshotsUsed,
                activeLicenses: activeLicenses.map(license => license.getStatus()),
                recentPayments: recentPayments.map(payment => ({
                    id: payment._id,
                    amount: payment.amount,
                    currency: payment.currency,
                    status: payment.status,
                    tier: payment.tier,
                    paymentDate: payment.paymentDate
                })),
                totalSpent,
                userStats: user.getStats()
            }
        });

    } catch (error) {
        console.error('❌ Dashboard data error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch dashboard data'
        });
    }
});

// 📸 INCREMENT USAGE (NO AUTH REQUIRED - FOR CLIENT APP)
router.post('/increment', async (req, res) => {
    try {
        const { license_key, user_id, feature, timestamp } = req.body;

        if (!license_key) {
            return res.status(400).json({
                success: false,
                error: 'License key is required'
            });
        }

        console.log('📸 Usage increment request:', { license_key: license_key.substring(0, 8) + '...', feature });

        // Find license
        const license = await License.findOne({ licenseKey: license_key });

        if (!license) {
            return res.status(404).json({
                success: false,
                error: 'License not found'
            });
        }

        // Check if license is valid
        if (!license.isValid()) {
            return res.status(403).json({
                success: false,
                error: 'License is expired or invalid'
            });
        }

        // Check if screenshots available
        if (license.screenshotsUsed >= license.screenshotsLimit) {
            return res.status(429).json({
                success: false,
                error: 'Screenshot limit reached. Please renew your license.'
            });
        }

        // Increment usage
        license.screenshotsUsed += 1;
        license.lastUsed = new Date();
        await license.save();

        // Update user stats if user exists
        if (user_id) {
            try {
                const user = await User.findById(user_id);
                if (user) {
                    user.totalScreenshotsUsed = (user.totalScreenshotsUsed || 0) + 1;
                    await user.save();
                }
            } catch (userError) {
                console.warn('⚠️ Failed to update user stats:', userError);
                // Continue anyway - license usage is more important
            }
        }

        console.log('✅ Usage incremented successfully:', license.screenshotsUsed, '/', license.screenshotsLimit);

        res.json({
            success: true,
            usage: {
                screenshots_used: license.screenshotsUsed,
                screenshots_remaining: license.getRemainingScreenshots(),
                screenshots_limit: license.screenshotsLimit
            }
        });

    } catch (error) {
        console.error('❌ Usage increment error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to increment usage'
        });
    }
});

// 📊 GET USAGE BY LICENSE KEY (NO AUTH REQUIRED - FOR CLIENT APP)
router.get('/get/:license_key', async (req, res) => {
    try {
        const { license_key } = req.params;

        if (!license_key) {
            return res.status(400).json({
                success: false,
                error: 'License key is required'
            });
        }

        console.log('📊 Usage query for:', license_key.substring(0, 8) + '...');

        // Find license
        const license = await License.findOne({ licenseKey: license_key });

        if (!license) {
            return res.status(404).json({
                success: false,
                error: 'License not found'
            });
        }

        // Update last used
        license.lastUsed = new Date();
        await license.save();

        console.log('📊 Usage data:', license.screenshotsUsed, '/', license.screenshotsLimit);

        res.json({
            success: true,
            screenshots_used: license.screenshotsUsed,
            screenshots_limit: license.screenshotsLimit,
            screenshots_remaining: license.getRemainingScreenshots(),
            plan: license.tier === 1 ? 'basic' : 'premium',
            expiry_date: license.expiryDate.toISOString(),
            days_remaining: license.getDaysRemaining(),
            is_valid: license.isValid()
        });

    } catch (error) {
        console.error('❌ Usage query error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get usage data'
        });
    }
});

// Simple test route
router.get('/test', (req, res) => {
    res.json({
        success: true,
        message: 'Usage route working!'
    });
});

module.exports = router;
