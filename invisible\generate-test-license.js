// 🔐 TEST LICENSE KEY GENERATOR
// Generates valid license keys for testing the anti-piracy system

const crypto = require('crypto');

class TestLicenseGenerator {
  constructor() {
    this.prefix = 'IAT'; // Invisible Assessment Tool
    this.version = '2024';
  }

  // Generate a random license key
  generateLicenseKey() {
    // Create timestamp component
    const timestamp = Date.now().toString(36).toUpperCase();
    
    // Create random component
    const randomBytes = crypto.randomBytes(8).toString('hex').toUpperCase();
    
    // Create checksum component
    const checksumData = this.prefix + this.version + timestamp + randomBytes;
    const checksum = crypto.createHash('sha256').update(checksumData).digest('hex').substring(0, 8).toUpperCase();
    
    // Format: IAT-2024-TIMESTAMP-RANDOM-CHECKSUM
    const licenseKey = `${this.prefix}-${this.version}-${timestamp}-${randomBytes}-${checksum}`;
    
    return licenseKey;
  }

  // Generate multiple license keys
  generateMultipleLicenses(count = 5) {
    const licenses = [];
    for (let i = 0; i < count; i++) {
      licenses.push(this.generateLicenseKey());
    }
    return licenses;
  }

  // Validate license key format
  validateLicenseFormat(licenseKey) {
    if (!licenseKey || typeof licenseKey !== 'string') {
      return false;
    }

    // Should be at least 32 characters
    if (licenseKey.length < 32) {
      return false;
    }

    // Should contain alphanumeric characters and dashes
    const licensePattern = /^[A-Za-z0-9\-]+$/;
    return licensePattern.test(licenseKey);
  }

  // Generate license with custom data
  generateCustomLicense(customerData = {}) {
    const {
      customerName = 'TEST_USER',
      purchaseDate = new Date().toISOString().split('T')[0],
      licenseType = 'STANDARD'
    } = customerData;

    // Create customer hash
    const customerHash = crypto.createHash('md5')
      .update(customerName + purchaseDate + licenseType)
      .digest('hex')
      .substring(0, 8)
      .toUpperCase();

    // Create timestamp
    const timestamp = Date.now().toString(36).toUpperCase();

    // Create random component
    const randomBytes = crypto.randomBytes(6).toString('hex').toUpperCase();

    // Create final checksum
    const checksumData = this.prefix + customerHash + timestamp + randomBytes;
    const checksum = crypto.createHash('sha256')
      .update(checksumData)
      .digest('hex')
      .substring(0, 6)
      .toUpperCase();

    // Format: IAT-CUSTOMERHASH-TIMESTAMP-RANDOM-CHECKSUM
    const licenseKey = `${this.prefix}-${customerHash}-${timestamp}-${randomBytes}-${checksum}`;

    return {
      licenseKey,
      customerData: {
        customerName,
        purchaseDate,
        licenseType,
        generatedAt: new Date().toISOString()
      }
    };
  }
}

// Main execution
function main() {
  console.log('🔐 TEST LICENSE KEY GENERATOR');
  console.log('============================');
  
  const generator = new TestLicenseGenerator();
  
  // Generate simple test licenses
  console.log('\n🎯 SIMPLE TEST LICENSES:');
  const simpleLicenses = generator.generateMultipleLicenses(3);
  simpleLicenses.forEach((license, index) => {
    console.log(`${index + 1}. ${license}`);
    console.log(`   Length: ${license.length} characters`);
    console.log(`   Valid: ${generator.validateLicenseFormat(license) ? '✅' : '❌'}`);
    console.log('');
  });

  // Generate custom licenses
  console.log('\n🎯 CUSTOM TEST LICENSES:');
  const customLicenses = [
    generator.generateCustomLicense({
      customerName: 'JOHN_DOE',
      licenseType: 'PREMIUM'
    }),
    generator.generateCustomLicense({
      customerName: 'JANE_SMITH',
      licenseType: 'STANDARD'
    }),
    generator.generateCustomLicense({
      customerName: 'TEST_USER',
      licenseType: 'TRIAL'
    })
  ];

  customLicenses.forEach((licenseData, index) => {
    console.log(`${index + 1}. LICENSE: ${licenseData.licenseKey}`);
    console.log(`   Customer: ${licenseData.customerData.customerName}`);
    console.log(`   Type: ${licenseData.customerData.licenseType}`);
    console.log(`   Generated: ${licenseData.customerData.generatedAt}`);
    console.log(`   Length: ${licenseData.licenseKey.length} characters`);
    console.log(`   Valid: ${generator.validateLicenseFormat(licenseData.licenseKey) ? '✅' : '❌'}`);
    console.log('');
  });

  // Generate a special test license for development
  console.log('\n🔧 DEVELOPMENT TEST LICENSE:');
  const devLicense = generator.generateCustomLicense({
    customerName: 'DEVELOPER',
    licenseType: 'DEV_TEST'
  });
  
  console.log(`LICENSE KEY: ${devLicense.licenseKey}`);
  console.log(`Length: ${devLicense.licenseKey.length} characters`);
  console.log(`Valid: ${generator.validateLicenseFormat(devLicense.licenseKey) ? '✅' : '❌'}`);
  
  console.log('\n💡 USAGE INSTRUCTIONS:');
  console.log('1. Copy any license key above');
  console.log('2. Run the installer');
  console.log('3. Paste the license key when prompted');
  console.log('4. Complete the installation');
  console.log('5. The anti-piracy system will bind the license to your hardware');
  
  console.log('\n⚠️ NOTE:');
  console.log('These are test licenses for development purposes only.');
  console.log('In production, implement proper license generation with your payment system.');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = TestLicenseGenerator;
