{"name": "expect", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/expect"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./build/utils": "./build/utils.js", "./build/matchers": "./build/matchers.js"}, "dependencies": {"@jest/types": "^27.5.1", "jest-get-type": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1"}, "devDependencies": {"@jest/test-utils": "^27.5.1", "@tsd/typescript": "~4.1.5", "chalk": "^4.0.0", "fast-check": "^2.0.0", "immutable": "^4.0.0", "tsd-lite": "^0.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}