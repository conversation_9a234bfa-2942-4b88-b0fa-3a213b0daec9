// 🛡️ ANTI-PIRACY MANAGER
// Main controller for all anti-piracy features

const LicenseValidator = require('./license-validator');
const RegistryProtection = require('./registry-protection');
const HardwareFingerprint = require('./hardware-fingerprint');
const fs = require('fs');
const path = require('path');

class AntiPiracyManager {
  constructor() {
    this.licenseValidator = new LicenseValidator();
    this.registryProtection = new RegistryProtection();
    this.hardwareFingerprint = new HardwareFingerprint();
    this.isInitialized = false;
    this.validationInterval = null;
  }

  // Initialize anti-piracy system
  async initialize() {
    try {
      console.log('🛡️ Initializing Anti-Piracy System...');

      // Check if license exists
      const licenseKey = this.licenseValidator.loadLicenseKey();
      if (!licenseKey) {
        console.log('❌ No license key found - requesting license');
        return { success: false, reason: 'NO_LICENSE' };
      }

      // Validate license
      const validation = await this.licenseValidator.validateLicense();
      if (!validation.valid) {
        console.log('❌ License validation failed:', validation.reason);
        return { success: false, reason: validation.reason };
      }

      // Verify registry protection
      const fingerprint = await this.hardwareFingerprint.generateFingerprint();
      if (!this.registryProtection.verifyRegistryIntegrity(licenseKey, fingerprint)) {
        console.log('❌ Registry integrity check failed');
        return { success: false, reason: 'REGISTRY_TAMPERED' };
      }

      // Update validation count
      this.registryProtection.updateValidationCount();

      // Start periodic validation
      this.startPeriodicValidation();

      this.isInitialized = true;
      console.log('✅ Anti-Piracy System initialized successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Anti-piracy initialization error:', error);
      return { success: false, reason: 'INITIALIZATION_ERROR' };
    }
  }

  // Setup license (first-time installation)
  async setupLicense(licenseKey) {
    try {
      console.log('🔐 Setting up license...');

      // Initialize license with hardware binding
      const result = await this.licenseValidator.initializeLicense(licenseKey);
      if (!result.success) {
        console.log('❌ License setup failed:', result.reason);
        return result;
      }

      // Generate hardware fingerprint
      const fingerprint = await this.hardwareFingerprint.generateFingerprint();

      // Store in registry
      if (!this.registryProtection.storeLicenseData(licenseKey, fingerprint)) {
        console.log('❌ Failed to store license in registry');
        return { success: false, reason: 'REGISTRY_STORAGE_FAILED' };
      }

      // Store anti-tampering data
      this.registryProtection.storeAntiTamperingData();

      console.log('🎉 License setup completed successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ License setup error:', error);
      return { success: false, reason: 'SETUP_ERROR' };
    }
  }

  // Validate current license status
  async validateCurrentLicense() {
    try {
      // Check if already initialized
      if (!this.isInitialized) {
        const initResult = await this.initialize();
        if (!initResult.success) {
          return initResult;
        }
      }

      // Perform comprehensive validation
      const licenseKey = this.licenseValidator.loadLicenseKey();
      const fingerprint = await this.hardwareFingerprint.generateFingerprint();

      // Check registry integrity
      if (!this.registryProtection.verifyRegistryIntegrity(licenseKey, fingerprint)) {
        return { valid: false, reason: 'REGISTRY_TAMPERED' };
      }

      // Check hardware binding
      const hardwareValid = await this.licenseValidator.verifyHardwareBinding();
      if (!hardwareValid) {
        return { valid: false, reason: 'HARDWARE_MISMATCH' };
      }

      return { valid: true };
    } catch (error) {
      console.error('❌ License validation error:', error);
      return { valid: false, reason: 'VALIDATION_ERROR' };
    }
  }

  // Start periodic validation (every 30 minutes)
  startPeriodicValidation() {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
    }

    this.validationInterval = setInterval(async () => {
      console.log('🔄 Performing periodic license validation...');
      const result = await this.validateCurrentLicense();
      
      if (!result.valid) {
        console.log('❌ Periodic validation failed:', result.reason);
        this.handleValidationFailure(result.reason);
      } else {
        console.log('✅ Periodic validation successful');
      }
    }, 30 * 60 * 1000); // 30 minutes
  }

  // Handle validation failure
  handleValidationFailure(reason) {
    console.log('🚨 ANTI-PIRACY VIOLATION DETECTED:', reason);
    
    // Log the violation
    this.logViolation(reason);
    
    // Show warning message
    const { dialog } = require('electron');
    dialog.showErrorBox(
      'License Violation Detected',
      `The software license validation failed.\n\nReason: ${reason}\n\nThe application will now close.\n\nPlease contact support if you believe this is an error.`
    );
    
    // Exit application
    setTimeout(() => {
      process.exit(1);
    }, 3000);
  }

  // Log violation attempt
  logViolation(reason) {
    try {
      const violationLog = {
        timestamp: new Date().toISOString(),
        reason: reason,
        hardwareFingerprint: this.hardwareFingerprint.getCurrentFingerprint(),
        processPath: process.execPath,
        workingDirectory: process.cwd()
      };

      const logFile = path.join(process.cwd(), 'violation.log');
      fs.appendFileSync(logFile, JSON.stringify(violationLog) + '\n');
    } catch (error) {
      console.error('❌ Error logging violation:', error);
    }
  }

  // Get license information
  getLicenseInfo() {
    try {
      const licenseInfo = this.licenseValidator.getLicenseInfo();
      const registryData = this.registryProtection.retrieveLicenseData();
      const installTime = this.registryProtection.getInstallationTime();

      return {
        ...licenseInfo,
        registryData: registryData,
        installationTime: installTime,
        isRegistered: this.registryProtection.isLicenseRegistered()
      };
    } catch (error) {
      console.error('❌ Error getting license info:', error);
      return null;
    }
  }

  // Check if license is properly set up
  isLicenseSetup() {
    try {
      const hasLicenseFile = this.licenseValidator.loadLicenseKey() !== null;
      const hasRegistryData = this.registryProtection.isLicenseRegistered();
      return hasLicenseFile && hasRegistryData;
    } catch (error) {
      return false;
    }
  }

  // Cleanup (for uninstall)
  cleanup() {
    try {
      // Stop periodic validation
      if (this.validationInterval) {
        clearInterval(this.validationInterval);
      }

      // Clean registry data
      this.registryProtection.cleanRegistryData();

      console.log('🧹 Anti-piracy system cleaned up');
      return true;
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
      return false;
    }
  }

  // Force validation (for testing)
  async forceValidation() {
    return await this.validateCurrentLicense();
  }

  // Get system status
  getSystemStatus() {
    return {
      isInitialized: this.isInitialized,
      hasLicense: this.licenseValidator.loadLicenseKey() !== null,
      isRegistered: this.registryProtection.isLicenseRegistered(),
      validationRunning: this.validationInterval !== null
    };
  }
}

module.exports = AntiPiracyManager;
