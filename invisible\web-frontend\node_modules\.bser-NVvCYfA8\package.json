{"name": "bser", "version": "2.1.1", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "files": ["index.js"], "repository": {"type": "git", "url": "https://github.com/facebook/watchman"}, "keywords": ["bser", "binary", "protocol"], "author": "<PERSON><PERSON> Furlong <<EMAIL>> (http://wezfurlong.org)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}}