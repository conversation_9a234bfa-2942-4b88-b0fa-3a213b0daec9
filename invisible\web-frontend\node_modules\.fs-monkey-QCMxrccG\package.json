{"name": "fs-monkey", "version": "1.0.6", "description": "Monkey patches for file system related things.", "main": "lib/index.js", "license": "Unlicense", "keywords": ["fs", "file", "file system", "monkey", "fsmo<PERSON>ey", "monkeyfs", "monkeypatch", "patch"], "files": ["lib", "!lib/__tests__", "docs"], "directories": {"doc": "docs"}, "repository": {"type": "git", "url": "https://github.com/streamich/fs-monkey.git"}, "scripts": {"build": "babel src --out-dir lib", "test": "jest"}, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.18.6", "@babel/core": "^7.18.6", "@babel/preset-env": "^7.18.6", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^9.0.1", "@types/jest": "^29.0.0", "@types/node": "^8.10.66", "babel-jest": "^29.0.0", "jest": "^29.0.0", "semantic-release": "^19.0.3", "source-map-support": "^0.5.21"}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"]}, "jest": {"collectCoverageFrom": ["src/**/*.js"], "transform": {"^.+\\.jsx?$": "babel-jest"}, "testRegex": ".*(__tests__/|/test/unit/).*(test|spec)\\.(t|j)sx?$"}}