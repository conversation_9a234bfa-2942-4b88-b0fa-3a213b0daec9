@echo off
echo 🛡️ BUILDING ANTI-PIRACY PROTECTED INSTALLER
echo ==========================================

:: Check if Inno Setup is installed
if not exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo ❌ Inno Setup 6 not found!
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

:: Create output directory
if not exist "installer-output" mkdir installer-output

:: Clean previous builds
echo 🧹 Cleaning previous builds...
del /q "installer-output\*.exe" 2>nul

:: Ensure anti-piracy directory exists
if not exist "anti-piracy" (
    echo ❌ Anti-piracy directory not found!
    echo Please ensure the anti-piracy system files are in place.
    pause
    exit /b 1
)

:: Check if portable app exists
if not exist "portable-app\InvisibleAssessmentTool.exe" (
    echo ❌ Portable app not found!
    echo Please build the Electron app first using: npm run build
    pause
    exit /b 1
)

:: Build the installer
echo 🔨 Building anti-piracy protected installer...
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" "installer-basic.iss"

if %ERRORLEVEL% EQU 0 (
    echo ✅ INSTALLER BUILD SUCCESSFUL!
    echo 📦 Output: installer-output\InvisibleAssessmentTool-v2.0-HARDWARE-LOCKED-ANTI-PIRACY.exe
    echo.
    echo 🛡️ ANTI-PIRACY FEATURES INCLUDED:
    echo ✅ Hardware fingerprinting
    echo ✅ License validation during installation
    echo ✅ Hardware binding system
    echo ✅ Registry protection
    echo ✅ Anti-tampering mechanisms
    echo.
    echo 🚀 Ready for distribution!
    
    :: Open output directory
    explorer "installer-output"
) else (
    echo ❌ INSTALLER BUILD FAILED!
    echo Check the error messages above.
)

pause
