/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import type { MatchersObject, RawMatcherFn } from './types';
export declare const createMatcher: (matcherName: string, fromPromise?: boolean | undefined) => RawMatcherFn;
declare const matchers: MatchersObject;
export default matchers;
