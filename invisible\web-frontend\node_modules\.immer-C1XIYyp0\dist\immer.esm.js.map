{"version": 3, "file": "immer.esm.js", "sources": ["../src/utils/errors.ts", "../src/utils/common.ts", "../src/utils/plugins.ts", "../src/core/scope.ts", "../src/core/finalize.ts", "../src/core/proxy.ts", "../src/core/immerClass.ts", "../src/core/current.ts", "../src/plugins/es5.ts", "../src/plugins/patches.ts", "../src/plugins/mapset.ts", "../src/plugins/all.ts", "../src/immer.ts", "../src/utils/env.ts"], "sourcesContent": ["const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n"], "names": ["die", "error", "args", "e", "errors", "msg", "apply", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "isPlainObject", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "original", "base_", "each", "obj", "iter", "enumerableOnly", "getArchtype", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "type_", "has", "prop", "prototype", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "getPlugin", "pluginKey", "plugin", "plugins", "loadPlugin", "implementation", "getCurrentScope", "process", "currentScope", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "undefined", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "NOTHING", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "res", "assigned_", "concat", "autoFreeze_", "peek", "getDescriptorFromProto", "source", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "proxyMap_", "proxySet_", "isManual_", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createProxyProxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "proxyProperty", "this", "assertUnrevoked", "mark<PERSON><PERSON>esSweep", "drafts", "hasArrayChanges", "hasObjectChanges", "baseValue", "baseIsDraft", "descriptor", "JSON", "stringify", "defineProperty", "createES5Draft", "mark<PERSON>hangesRecursively", "object", "min", "Math", "enablePatches", "deepClonePatchValue", "entries", "cloned", "immerable", "clonePatchValueIfNeeded", "ADD", "applyPatches_", "patches", "patch", "op", "parentType", "p", "type", "splice", "basePath", "inversePatches", "assignedValue", "origValue", "generatePatchesFromAssigned", "generateArrayPatches", "unshift", "generateSetPatches", "replacement", "enableMapSet", "__extends", "d", "b", "__", "extendStatics", "prepareMapCopy", "prepareSetCopy", "setPrototypeOf", "__proto__", "DraftMap", "size", "cb", "thisArg", "_value", "_this", "values", "iterator", "iteratorSymbol", "_this2", "next", "r", "done", "_this3", "DraftSet", "enableAllPlugins", "castDraft", "castImmutable", "hasSymbol", "Symbol", "hasProxies", "Reflect", "for", "data", "getOwnPropertySymbols", "getOwnPropertyNames", "_desc$get", "readPropFromProto", "currentState", "Number", "isNaN", "deleteProperty", "owner", "fn", "arguments", "parseInt", "Immer", "config", "recipe", "defaultBase", "self", "produce", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "then", "ip", "produceWithPatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "applyPatchesImpl", "bind"], "mappings": "SA4CgBA,EAAIC,8BAA+BC,+BAAAA,2DACrC,KACNC,EAAIC,EAAOH,GACXI,EAAOF,EAEG,mBAANA,EACPA,EAAEG,MAAM,KAAMJ,GACdC,EAHA,qBAAuBF,QAIhBM,iBAAiBF,SAElBE,oCACqBN,GAC7BC,EAAKM,OAAS,IAAMN,EAAKO,KAAI,SAAAC,aAASA,SAAMC,KAAK,KAAO,iECvC3CC,EAAQC,WACdA,KAAWA,EAAMC,YAKXC,EAAYF,iBACtBA,aAawBA,OACxBA,GAA0B,iBAAVA,EAAoB,OAAO,MAC1CG,EAAQC,OAAOC,eAAeL,MACtB,OAAVG,SACI,MAEFG,EACLF,OAAOG,eAAeC,KAAKL,EAAO,gBAAkBA,EAAMM,mBAEvDH,IAASF,QAGG,mBAARE,GACPI,SAASC,SAASH,KAAKF,KAAUM,EAxBjCC,CAAcb,IACdc,MAAMC,QAAQf,MACZA,EAAMgB,iBACNhB,EAAMS,gCAANQ,EAAoBD,KACtBE,EAAMlB,IACNmB,EAAMnB,aA0BQoB,EAASpB,UACnBD,EAAQC,IAAQb,EAAI,GAAIa,GACtBA,EAAMC,GAAaoB,EA8B3B,SAAgBC,EAAKC,EAAUC,EAAWC,YAAAA,IAAAA,GAAiB,OACtDC,EAAYH,IACbE,EAAiBrB,OAAOuB,KAAOC,IAASL,GAAKM,SAAQ,SAAAC,GACjDL,GAAiC,iBAARK,GAAkBN,EAAKM,EAAKP,EAAIO,GAAMP,MAGrEA,EAAIM,SAAQ,SAACE,EAAYC,UAAeR,EAAKQ,EAAOD,EAAOR,eAK7CG,EAAYO,OAErBC,EAAgCD,EAAMhC,UACrCiC,EACJA,EAAMC,EAAQ,EACbD,EAAMC,EAAQ,EACbD,EAAMC,EACRrB,MAAMC,QAAQkB,KAEdf,EAAMe,KAENd,EAAMc,gBAMMG,EAAIH,EAAYI,cACxBX,EAAYO,GAChBA,EAAMG,IAAIC,GACVjC,OAAOkC,UAAU/B,eAAeC,KAAKyB,EAAOI,YAIhCE,EAAIN,EAA2BI,cAEvCX,EAAYO,GAA0BA,EAAMM,IAAIF,GAAQJ,EAAMI,GAItE,SAAgBG,EAAIP,EAAYQ,EAA6BzC,OACtD0C,EAAIhB,EAAYO,OAClBS,EAAoBT,EAAMO,IAAIC,EAAgBzC,OACzC0C,EACRT,EAAMU,IAAI3C,GACJiC,EAAMQ,GAAkBzC,WAIhB4C,EAAGC,EAAQC,UAEtBD,IAAMC,EACI,IAAND,GAAW,EAAIA,GAAM,EAAIC,EAEzBD,GAAMA,GAAKC,GAAMA,WAKV5B,EAAM6B,UACdC,GAAUD,aAAkBE,aAIpB9B,EAAM4B,UACdG,GAAUH,aAAkBI,aAGpBC,EAAOlB,UACfA,EAAMmB,GAASnB,EAAMb,WAIbiC,EAAYC,MACvBzC,MAAMC,QAAQwC,GAAO,OAAOzC,MAAMwB,UAAUkB,MAAMhD,KAAK+C,OACrDE,EAAcC,GAA0BH,UACvCE,EAAYxD,WACf0B,EAAOC,GAAQ6B,GACVE,EAAI,EAAGA,EAAIhC,EAAKhC,OAAQgE,IAAK,KAC/B7B,EAAWH,EAAKgC,GAChBC,EAAOH,EAAY3B,IACH,IAAlB8B,EAAKC,WACRD,EAAKC,UAAW,EAChBD,EAAKE,cAAe,IAKjBF,EAAKrB,KAAOqB,EAAKpB,OACpBiB,EAAY3B,GAAO,CAClBgC,cAAc,EACdD,UAAU,EACVE,WAAYH,EAAKG,WACjB/D,MAAOuD,EAAKzB,YAGR1B,OAAO4D,OAAO5D,OAAOC,eAAekD,GAAOE,YAWnCQ,EAAU1C,EAAU2C,mBAAAA,IAAAA,GAAgB,GAC/CC,EAAS5C,IAAQxB,EAAQwB,KAASrB,EAAYqB,KAC9CG,EAAYH,GAAO,IACtBA,EAAIiB,IAAMjB,EAAIoB,IAAMpB,EAAI6C,MAAQ7C,EAAI8C,OAASC,GAE9ClE,OAAO6D,OAAO1C,GACV2C,GAAM5C,EAAKC,GAAK,SAACO,EAAK9B,UAAUiE,EAAOjE,GAAO,MAAO,IALMuB,EAShE,SAAS+C,IACRnF,EAAI,YAGWgF,EAAS5C,UACb,MAAPA,GAA8B,iBAARA,GAEnBnB,OAAO+D,SAAS5C,YCxKRgD,EACfC,OAEMC,EAASC,GAAQF,UAClBC,GACJtF,EAAI,GAAIqF,GAGFC,WAGQE,EACfH,EACAI,GAEKF,GAAQF,KAAYE,GAAQF,GAAaI,GClC/C,SAAgBC,yBACXC,sBAAYC,GAAc5F,EAAI,GAC3B4F,WAkBQC,EACfC,EACAC,GAEIA,IACHX,EAAU,WACVU,EAAME,EAAW,GACjBF,EAAMG,EAAkB,GACxBH,EAAMI,EAAiBH,YAITI,EAAYL,GAC3BM,EAAWN,GACXA,EAAMO,EAAQ3D,QAAQ4D,GAEtBR,EAAMO,EAAU,cAGDD,EAAWN,GACtBA,IAAUF,IACbA,EAAeE,EAAMS,YAIPC,EAAWC,UAClBb,EArCD,CACNS,EAAS,GACTE,EAmCkCX,EAlClCc,EAkCgDD,EA/BhDE,GAAgB,EAChBC,EAAoB,GAiCtB,SAASN,EAAYO,OACd9D,EAAoB8D,EAAM/F,OAE/BiC,EAAMC,OACND,EAAMC,EAEND,EAAM+D,IACF/D,EAAMgE,GAAW,WC9DPC,EAAcC,EAAanB,GAC1CA,EAAMc,EAAqBd,EAAMO,EAAQ7F,WACnC0G,EAAYpB,EAAMO,EAAS,GAC3Bc,OAAwBC,IAAXH,GAAwBA,IAAWC,SACjDpB,EAAMY,EAAOW,GACjBjC,EAAU,OAAOkC,EAAiBxB,EAAOmB,EAAQE,GAC9CA,GACCD,EAAUpG,GAAayG,IAC1BpB,EAAYL,GACZ9F,EAAI,IAEDe,EAAYkG,KAEfA,EAASO,EAAS1B,EAAOmB,GACpBnB,EAAMS,GAASkB,EAAY3B,EAAOmB,IAEpCnB,EAAME,GACTZ,EAAU,WAAWsC,EACpBR,EAAUpG,GAAaoB,EACvB+E,EACAnB,EAAME,EACNF,EAAMG,IAKRgB,EAASO,EAAS1B,EAAOoB,EAAW,IAErCf,EAAYL,GACRA,EAAME,GACTF,EAAMI,EAAgBJ,EAAME,EAAUF,EAAMG,GAEtCgB,IAAWU,EAAUV,OAASG,EAGtC,SAASI,EAASI,EAAuB/G,EAAYgH,MAEhD7C,EAASnE,GAAQ,OAAOA,MAEtBkC,EAAoBlC,EAAMC,OAE3BiC,SACJZ,EACCtB,GACA,SAAC8B,EAAKmF,UACLC,EAAiBH,EAAW7E,EAAOlC,EAAO8B,EAAKmF,EAAYD,MAC5D,GAEMhH,KAGJkC,EAAMiF,IAAWJ,EAAW,OAAO/G,MAElCkC,EAAMwE,SACVE,EAAYG,EAAW7E,EAAMb,GAAO,GAC7Ba,EAAMb,MAGTa,EAAMkF,EAAY,CACtBlF,EAAMkF,GAAa,EACnBlF,EAAMiF,EAAOpB,QACPK,MAELlE,EAAMC,OAAiCD,EAAMC,EACzCD,EAAMmB,EAAQC,EAAYpB,EAAMmF,GACjCnF,EAAMmB,EAKNiE,EAAalB,EACbjF,GAAQ,MACRe,EAAMC,IACTmF,EAAa,IAAInE,IAAIiD,GACrBA,EAAOhC,QACPjD,GAAQ,GAETG,EAAKgG,GAAY,SAACxF,EAAKmF,UACtBC,EAAiBH,EAAW7E,EAAOkE,EAAQtE,EAAKmF,EAAYD,EAAM7F,MAGnEyF,EAAYG,EAAWX,GAAQ,GAE3BY,GAAQD,EAAU5B,GACrBZ,EAAU,WAAWgD,EACpBrF,EACA8E,EACAD,EAAU5B,EACV4B,EAAU3B,UAINlD,EAAMmB,EAGd,SAAS6D,EACRH,EACAS,EACAC,EACApF,EACA4E,EACAS,EACAC,qBAEI7C,sBAAWmC,IAAeQ,GAActI,EAAI,GAC5CY,EAAQkH,GAAa,KASlBW,EAAMjB,EAASI,EAAWE,EAP/BS,GACAF,OACAA,EAAarF,IACZC,EAAKoF,EAA8CK,EAAYxF,GAC7DqF,EAAUI,OAAOzF,QACjBkE,MAGJ/D,EAAIiF,EAAcpF,EAAMuF,IAGpB7H,EAAQ6H,GAEL,OADNb,EAAUjB,GAAiB,OAElB6B,GACVF,EAAa9E,IAAIsE,MAGd/G,EAAY+G,KAAgB9C,EAAS8C,GAAa,KAChDF,EAAUlB,EAAOkC,GAAehB,EAAUhB,EAAqB,SAQpEY,EAASI,EAAWE,GAEfO,GAAgBA,EAAYL,EAAOzB,GACvCkB,EAAYG,EAAWE,IAI1B,SAASL,EAAY3B,EAAmBjF,EAAYkE,YAAAA,IAAAA,GAAO,IAErDe,EAAMS,GAAWT,EAAMY,EAAOkC,GAAe9C,EAAMa,GACvD7B,EAAOjE,EAAOkE,GCqEhB,SAAS8D,EAAKhC,EAAgB3D,OACvBH,EAAQ8D,EAAM/F,UACLiC,EAAQkB,EAAOlB,GAAS8D,GACzB3D,GAcf,SAAS4F,EACRC,EACA7F,MAGMA,KAAQ6F,UACV/H,EAAQC,OAAOC,eAAe6H,GAC3B/H,GAAO,KACPyD,EAAOxD,OAAO+H,yBAAyBhI,EAAOkC,MAChDuB,EAAM,OAAOA,EACjBzD,EAAQC,OAAOC,eAAeF,aAKhBiI,EAAYlG,GACtBA,EAAMwE,IACVxE,EAAMwE,GAAY,EACdxE,EAAMwD,GACT0C,EAAYlG,EAAMwD,aAKL2C,EAAYnG,GACtBA,EAAMmB,IACVnB,EAAMmB,EAAQC,EAAYpB,EAAMb,ICtDlC,SAAgBiH,EACf1C,EACA5F,EACAuI,OAGMvC,EAAiB9E,EAAMlB,GAC1BuE,EAAU,UAAUiE,EAAUxI,EAAOuI,GACrCpH,EAAMnB,GACNuE,EAAU,UAAUkE,EAAUzI,EAAOuI,GACrC3C,EAAMY,WDvLTjD,EACAgF,OAEMxH,EAAUD,MAAMC,QAAQwC,GACxBrB,EAAoB,CACzBC,EAAOpB,IAAkC,EAEzCoG,EAAQoB,EAASA,EAAOpB,EAAStC,IAEjC6B,GAAW,EAEXU,GAAY,EAEZS,EAAW,GAEXnC,EAAS6C,EAETlH,EAAOkC,EAEP8D,EAAQ,KAERhE,EAAO,KAEP4C,EAAS,KACTyC,GAAW,GASR3F,EAAYb,EACZyG,EAA2CC,GAC3C7H,IACHgC,EAAS,CAACb,GACVyG,EAAQE,UAGeC,MAAMC,UAAUhG,EAAQ4F,GAAzCK,IAAAA,OAAQC,IAAAA,aACf/G,EAAMmF,EAAS4B,EACf/G,EAAM+D,EAAU+C,EACTC,EC6IJC,CAAiBlJ,EAAOuI,GACxBhE,EAAU,OAAO4E,EAAgBnJ,EAAOuI,UAE7BA,EAASA,EAAOpB,EAAStC,KACjCW,EAAQ4D,KAAKpD,GACZA,WC9NQqD,EAAQrJ,UAClBD,EAAQC,IAAQb,EAAI,GAAIa,GAI9B,SAASsJ,EAAYtJ,OACfE,EAAYF,GAAQ,OAAOA,MAE5BuJ,EADErH,EAAgClC,EAAMC,GAEtCuJ,EAAW9H,EAAY1B,MACzBkC,EAAO,KAERA,EAAMwE,IACNxE,EAAMC,EAAQ,IAAMoC,EAAU,OAAOkF,EAAYvH,IAElD,OAAOA,EAAMb,EAEda,EAAMkF,GAAa,EACnBmC,EAAOG,EAAW1J,EAAOwJ,GACzBtH,EAAMkF,GAAa,OAEnBmC,EAAOG,EAAW1J,EAAOwJ,UAG1BlI,EAAKiI,GAAM,SAACzH,EAAKmF,GACZ/E,GAASK,EAAIL,EAAMb,EAAOS,KAASmF,GACvCzE,EAAI+G,EAAMzH,EAAKwH,EAAYrC,WAGrBuC,EAA4B,IAAIrG,IAAIoG,GAAQA,EA3B5CD,CAAYtJ,GA8BpB,SAAS0J,EAAW1J,EAAYwJ,UAEvBA,iBAEC,IAAIvG,IAAIjD,iBAGRc,MAAM6I,KAAK3J,UAEbsD,EAAYtD,YClCJ4J,aA8ENC,EACRxH,EACA0B,OAEIH,EAAOH,EAAYpB,UACnBuB,EACHA,EAAKG,WAAaA,EAElBN,EAAYpB,GAAQuB,EAAO,CAC1BE,cAAc,EACdC,WAAAA,EACAxB,mBACOL,EAAQ4H,KAAK7J,8CACN8J,EAAgB7H,GAEtB0G,GAAYrG,IAAIL,EAAOG,IAE/BG,aAAexC,OACRkC,EAAQ4H,KAAK7J,wCACN8J,EAAgB7H,GAE7B0G,GAAYpG,IAAIN,EAAOG,EAAMrC,KAIzB4D,WAICoG,EAAiBC,OAKpB,IAAItG,EAAIsG,EAAOtK,OAAS,EAAGgE,GAAK,EAAGA,IAAK,KACtCzB,EAAkB+H,EAAOtG,GAAG1D,OAC7BiC,EAAMwE,SACFxE,EAAMC,UAER+H,EAAgBhI,IAAQkG,EAAYlG,gBAGpCiI,EAAiBjI,IAAQkG,EAAYlG,cA6DrCiI,EAAiBjI,WAClBb,EAAiBa,EAAjBb,EAAOgG,EAAUnF,EAAVmF,EAIR1F,EAAOC,GAAQyF,GACZ1D,EAAIhC,EAAKhC,OAAS,EAAGgE,GAAK,EAAGA,IAAK,KACpC7B,EAAWH,EAAKgC,MAClB7B,IAAQ7B,OACNmK,EAAY/I,EAAMS,WAENyE,IAAd6D,IAA4BhI,EAAIf,EAAOS,UACnC,MAKD9B,EAAQqH,EAAOvF,GACfI,EAAoBlC,GAASA,EAAMC,MACrCiC,EAAQA,EAAMb,IAAU+I,GAAaxH,EAAG5C,EAAOoK,UAC3C,OAOJC,IAAgBhJ,EAAMpB,UACrB0B,EAAKhC,SAAWiC,GAAQP,GAAO1B,QAAU0K,EAAc,EAAI,YAG1DH,EAAgBhI,OACjBmF,EAAUnF,EAAVmF,KACHA,EAAO1H,SAAWuC,EAAMb,EAAM1B,OAAQ,OAAO,MAS3C2K,EAAalK,OAAO+H,yBACzBd,EACAA,EAAO1H,OAAS,MAGb2K,IAAeA,EAAW/H,IAAK,OAAO,MAErC,IAAIoB,EAAI,EAAGA,EAAI0D,EAAO1H,OAAQgE,QAC7B0D,EAAO9G,eAAeoD,GAAI,OAAO,SAGhC,WASCoG,EAAgB7H,GACpBA,EAAMgE,GAAU/G,EAAI,EAAGoL,KAAKC,UAAUpH,EAAOlB,SAxK5CuB,EAAoD,GA2K1DkB,EAAW,MAAO,CACjBwE,WA5MA5F,EACAgF,OAEMxH,EAAUD,MAAMC,QAAQwC,GACxByC,WA1BiBjF,EAAkBwC,MACrCxC,EAAS,SACNiF,EAAYlF,MAAMyC,EAAK5D,QACpBgE,EAAI,EAAGA,EAAIJ,EAAK5D,OAAQgE,IAChCvD,OAAOqK,eAAezE,EAAO,GAAKrC,EAAGkG,EAAclG,GAAG,WAChDqC,MAEDvC,EAAcC,GAA0BH,UACvCE,EAAYxD,WACb0B,EAAOC,GAAQ6B,GACZE,EAAI,EAAGA,EAAIhC,EAAKhC,OAAQgE,IAAK,KAC/B7B,EAAWH,EAAKgC,GACtBF,EAAY3B,GAAO+H,EAClB/H,EACAf,KAAa0C,EAAY3B,GAAKiC,mBAGzB3D,OAAO4D,OAAO5D,OAAOC,eAAekD,GAAOE,GASrCiH,CAAe3J,EAASwC,GAEhCrB,EAAwC,CAC7CC,EAAOpB,IAAgC,EACvCoG,EAAQoB,EAASA,EAAOpB,EAAStC,IACjC6B,GAAW,EACXU,GAAY,EACZS,EAAW,GACXnC,EAAS6C,EAETlH,EAAOkC,EAEP8D,EAAQrB,EACR3C,EAAO,KACP6C,GAAU,EACVwC,GAAW,UAGZtI,OAAOqK,eAAezE,EAAO/F,EAAa,CACzCD,MAAOkC,EAEP2B,UAAU,IAEJmC,GAkLPS,WAvPAxB,EACAmB,EACAE,GAEKA,EASJvG,EAAQqG,IACPA,EAAOnG,GAA0BkH,IAAWlC,GAE7C+E,EAAiB/E,EAAMO,IAXnBP,EAAME,YAwHHwF,EAAuBC,MAC1BA,GAA4B,iBAAXA,OAChB1I,EAA8B0I,EAAO3K,MACtCiC,OACEb,EAAmCa,EAAnCb,EAAOgG,EAA4BnF,EAA5BmF,EAAQQ,EAAoB3F,EAApB2F,EAAW1F,EAASD,EAATC,SAC7BA,EAKHb,EAAK+F,GAAQ,SAAAvF,GACPA,IAAgB7B,SAEOsG,IAAvBlF,EAAcS,IAAuBM,EAAIf,EAAOS,GAGzC+F,EAAU/F,IAErB6I,EAAuBtD,EAAOvF,KAJ9B+F,EAAU/F,IAAO,EACjBsG,EAAYlG,QAOdZ,EAAKD,GAAO,SAAAS,QAESyE,IAAhBc,EAAOvF,IAAuBM,EAAIiF,EAAQvF,KAC7C+F,EAAU/F,IAAO,EACjBsG,EAAYlG,YAGR,OAAIC,EAA8B,IACpC+H,EAAgBhI,KACnBkG,EAAYlG,GACZ2F,EAAUlI,QAAS,GAGhB0H,EAAO1H,OAAS0B,EAAM1B,WACpB,IAAIgE,EAAI0D,EAAO1H,OAAQgE,EAAItC,EAAM1B,OAAQgE,IAAKkE,EAAUlE,IAAK,WAE7D,IAAIA,EAAItC,EAAM1B,OAAQgE,EAAI0D,EAAO1H,OAAQgE,IAAKkE,EAAUlE,IAAK,UAI7DkH,EAAMC,KAAKD,IAAIxD,EAAO1H,OAAQ0B,EAAM1B,QAEjCgE,EAAI,EAAGA,EAAIkH,EAAKlH,IAEnB0D,EAAO9G,eAAeoD,KAC1BkE,EAAUlE,IAAK,QAEK4C,IAAjBsB,EAAUlE,IAAkBgH,EAAuBtD,EAAO1D,OAxK9DgH,CAAuB1F,EAAMO,EAAS,IAGvCwE,EAAiB/E,EAAMO,KA+OxBiE,WAboBvH,cACbA,EAAMC,EACVgI,EAAiBjI,GACjBgI,EAAgBhI,eC9OL6I,aA6PNC,EAAoBzJ,OACvBrB,EAAYqB,GAAM,OAAOA,KAC1BT,MAAMC,QAAQQ,GAAM,OAAOA,EAAI3B,IAAIoL,MACnC9J,EAAMK,GACT,OAAO,IAAI0B,IACVnC,MAAM6I,KAAKpI,EAAI0J,WAAWrL,KAAI,kBAAY,MAAIoL,gBAE5C7J,EAAMI,GAAM,OAAO,IAAI4B,IAAIrC,MAAM6I,KAAKpI,GAAK3B,IAAIoL,QAC7CE,EAAS9K,OAAO4D,OAAO5D,OAAOC,eAAekB,QAC9C,IAAMO,KAAOP,EAAK2J,EAAOpJ,GAAOkJ,EAAoBzJ,EAAIO,WACzDM,EAAIb,EAAK4J,KAAYD,EAAOC,GAAa5J,EAAI4J,IAC1CD,WAGCE,EAA2B7J,UAC/BxB,EAAQwB,GACJyJ,EAAoBzJ,GACdA,MA5QT8J,EAAM,MA+QZ1G,EAAW,UAAW,CACrB2G,WAlGyBtF,EAAUuF,UACnCA,EAAQ1J,SAAQ,SAAA2J,WACRxE,EAAYwE,EAAZxE,KAAMyE,EAAMD,EAANC,GAETlI,EAAYyC,EACPrC,EAAI,EAAGA,EAAIqD,EAAKrH,OAAS,EAAGgE,IAAK,KACnC+H,EAAahK,EAAY6B,GAC3BoI,EAAI3E,EAAKrD,GACI,iBAANgI,GAA+B,iBAANA,IACnCA,EAAI,GAAKA,OAKRD,OAAkCA,GAC5B,cAANC,GAA2B,gBAANA,GAEtBxM,EAAI,IACe,mBAAToE,GAA6B,cAANoI,GAAmBxM,EAAI,IAErC,iBADpBoE,EAAOhB,EAAIgB,EAAMoI,KACaxM,EAAI,GAAI6H,EAAKlH,KAAK,UAG3C8L,EAAOlK,EAAY6B,GACnBvD,EAAQgL,EAAoBQ,EAAMxL,OAClC8B,EAAMkF,EAAKA,EAAKrH,OAAS,UACvB8L,OAzMM,iBA2MJG,iBAECrI,EAAKf,IAAIV,EAAK9B,UAGrBb,EAAI,mBAMIoE,EAAKzB,GAAO9B,OAElBqL,SACIO,gBAES,MAAR9J,EACJyB,EAAK6F,KAAKpJ,GACVuD,EAAKsI,OAAO/J,EAAY,EAAG9B,iBAEvBuD,EAAKf,IAAIV,EAAK9B,iBAEduD,EAAKZ,IAAI3C,kBAERuD,EAAKzB,GAAO9B,MAjOX,gBAoOH4L,iBAECrI,EAAKsI,OAAO/J,EAAY,iBAExByB,EAAKc,OAAOvC,iBAEZyB,EAAKc,OAAOmH,EAAMxL,6BAEXuD,EAAKzB,WAGrB3C,EAAI,GAAIsM,OAIJzF,GA6BPuB,WA7QArF,EACA4J,EACAP,EACAQ,UAEQ7J,EAAMC,wCAgFdD,EACA4J,EACAP,EACAQ,OAEO1K,EAAgBa,EAAhBb,EAAOgC,EAASnB,EAATmB,EACd/B,EAAKY,EAAM2F,GAAY,SAAC/F,EAAKkK,OACtBC,EAAY1J,EAAIlB,EAAOS,GACvB9B,EAAQuC,EAAIc,EAAQvB,GACpB2J,EAAMO,EAAyB5J,EAAIf,EAAOS,GAnGlC,UAmGmDuJ,EAjGpD,YAkGTY,IAAcjM,GApGJ,YAoGayL,OACrBzE,EAAO8E,EAAShE,OAAOhG,GAC7ByJ,EAAQnC,KApGK,WAoGAqC,EAAgB,CAACA,GAAAA,EAAIzE,KAAAA,GAAQ,CAACyE,GAAAA,EAAIzE,KAAAA,EAAMhH,MAAAA,IACrD+L,EAAe3C,KACdqC,IAAOJ,EACJ,CAACI,GAvGQ,SAuGIzE,KAAAA,GAvGJ,WAwGTyE,EACA,CAACA,GAAIJ,EAAKrE,KAAAA,EAAMhH,MAAOoL,EAAwBa,IAC/C,CAACR,GA5GS,UA4GIzE,KAAAA,EAAMhH,MAAOoL,EAAwBa,SA9F/CC,CACNhK,EACA4J,EACAP,EACAQ,iCAgBH7J,EACA4J,EACAP,EACAQ,OAEK1K,EAAoBa,EAApBb,EAAOwG,EAAa3F,EAAb2F,EACRxE,EAAQnB,EAAMmB,KAGdA,EAAM1D,OAAS0B,EAAM1B,OAAQ,OAEd,CAAC0D,EAAOhC,GAAxBA,OAAOgC,aACoB,CAAC0I,EAAgBR,GAA5CA,OAASQ,WAIP,IAAIpI,EAAI,EAAGA,EAAItC,EAAM1B,OAAQgE,OAC7BkE,EAAUlE,IAAMN,EAAMM,KAAOtC,EAAMsC,GAAI,KACpCqD,EAAO8E,EAAShE,OAAO,CAACnE,IAC9B4H,EAAQnC,KAAK,CACZqC,GAtDY,UAuDZzE,KAAAA,EAGAhH,MAAOoL,EAAwB/H,EAAMM,MAEtCoI,EAAe3C,KAAK,CACnBqC,GA7DY,UA8DZzE,KAAAA,EACAhH,MAAOoL,EAAwB/J,EAAMsC,UAMnC,IAAIA,EAAItC,EAAM1B,OAAQgE,EAAIN,EAAM1D,OAAQgE,IAAK,KAC3CqD,EAAO8E,EAAShE,OAAO,CAACnE,IAC9B4H,EAAQnC,KAAK,CACZqC,GAAIJ,EACJrE,KAAAA,EAGAhH,MAAOoL,EAAwB/H,EAAMM,MAGnCtC,EAAM1B,OAAS0D,EAAM1D,QACxBoM,EAAe3C,KAAK,CACnBqC,GAjFa,UAkFbzE,KAAM8E,EAAShE,OAAO,CAAC,WACvB9H,MAAOqB,EAAM1B,SA7DNwM,CAAqBjK,EAAO4J,EAAUP,EAASQ,0BA4FxD7J,EACA4J,EACAP,EACAQ,OAEK1K,EAAgBa,EAAhBb,EAAOgC,EAASnB,EAATmB,EAERM,EAAI,EACRtC,EAAMQ,SAAQ,SAAC7B,OACTqD,EAAOjB,IAAIpC,GAAQ,KACjBgH,EAAO8E,EAAShE,OAAO,CAACnE,IAC9B4H,EAAQnC,KAAK,CACZqC,GA5HW,SA6HXzE,KAAAA,EACAhH,MAAAA,IAED+L,EAAeK,QAAQ,CACtBX,GAAIJ,EACJrE,KAAAA,EACAhH,MAAAA,IAGF2D,OAEDA,EAAI,EACJN,EAAOxB,SAAQ,SAAC7B,OACVqB,EAAMe,IAAIpC,GAAQ,KAChBgH,EAAO8E,EAAShE,OAAO,CAACnE,IAC9B4H,EAAQnC,KAAK,CACZqC,GAAIJ,EACJrE,KAAAA,EACAhH,MAAAA,IAED+L,EAAeK,QAAQ,CACtBX,GAlJW,SAmJXzE,KAAAA,EACAhH,MAAAA,IAGF2D,OAjIQ0I,CACLnK,EACD4J,EACAP,EACAQ,KAuPHlF,WArHAuD,EACAkC,EACAf,EACAQ,GAEAR,EAAQnC,KAAK,CACZqC,GApKc,UAqKdzE,KAAM,GACNhH,MAAOsM,IAAgBxF,OAAUP,EAAY+F,IAE9CP,EAAe3C,KAAK,CACnBqC,GAzKc,UA0KdzE,KAAM,GACNhH,MAAOoK,OCrMV,SAmBgBmC,aAgBNC,EAAUC,EAAQC,YAEjBC,SACHlM,YAAcgM,EAFpBG,EAAcH,EAAGC,GAIjBD,EAAEnK,WAECqK,EAAGrK,UAAYoK,EAAEpK,UAAY,IAAIqK,YA8J5BE,EAAe3K,GAClBA,EAAMmB,IACVnB,EAAM2F,EAAY,IAAI5E,IACtBf,EAAMmB,EAAQ,IAAIJ,IAAIf,EAAMb,aA0HrByL,EAAe5K,GAClBA,EAAMmB,IAEVnB,EAAMmB,EAAQ,IAAIF,IAClBjB,EAAMb,EAAMQ,SAAQ,SAAA7B,MACfE,EAAYF,GAAQ,KACjBgG,EAAQsC,EAAYpG,EAAMiF,EAAOtB,EAAQ7F,EAAOkC,GACtDA,EAAMsD,EAAQhD,IAAIxC,EAAOgG,GACzB9D,EAAMmB,EAAOV,IAAIqD,QAEjB9D,EAAMmB,EAAOV,IAAI3C,gBAMZ+J,EAAgB7H,GACpBA,EAAMgE,GAAU/G,EAAI,EAAGoL,KAAKC,UAAUpH,EAAOlB,SAjU9C0K,EAAgB,SAASH,EAAQC,UACpCE,EACCxM,OAAO2M,gBACN,CAACC,UAAW,cAAelM,OAC3B,SAAS2L,EAAGC,GACXD,EAAEO,UAAYN,IAEhB,SAASD,EAAGC,OACN,IAAIf,KAAKe,EAAOA,EAAEnM,eAAeoL,KAAIc,EAAEd,GAAKe,EAAEf,MAEhCc,EAAGC,IAcnBO,EAAY,oBAGRA,EAAoBlK,EAAgBwF,eACvCtI,GAAe,CACnBkC,IACAuD,EAAS6C,EACTpB,EAAQoB,EAASA,EAAOpB,EAAStC,IACjC6B,GAAW,EACXU,GAAY,EACZ/D,OAAOkD,EACPsB,OAAWtB,EACXlF,EAAO0B,EACPsE,EAAQyC,KACRpB,GAAW,EACXxC,GAAU,GAEJ4D,KAhBR0C,EAAUS,EAmJRhK,SAjII0I,EAAIsB,EAAS3K,iBAEnBlC,OAAOqK,eAAekB,EAAG,OAAQ,CAChCpJ,IAAK,kBACGa,EAAO0G,KAAK7J,IAAciN,QAMnCvB,EAAEvJ,IAAM,SAASN,UACTsB,EAAO0G,KAAK7J,IAAcmC,IAAIN,IAGtC6J,EAAEnJ,IAAM,SAASV,EAAU9B,OACpBkC,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GACXkB,EAAOlB,GAAOE,IAAIN,IAAQsB,EAAOlB,GAAOK,IAAIT,KAAS9B,IACzD6M,EAAe3K,GACfkG,EAAYlG,GACZA,EAAM2F,EAAWrF,IAAIV,GAAK,GAC1BI,EAAMmB,EAAOb,IAAIV,EAAK9B,GACtBkC,EAAM2F,EAAWrF,IAAIV,GAAK,IAEpBgI,MAGR6B,EAAEtH,OAAS,SAASvC,OACdgI,KAAK1H,IAAIN,UACN,MAGFI,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GAChB2K,EAAe3K,GACfkG,EAAYlG,GACRA,EAAMb,EAAMe,IAAIN,GACnBI,EAAM2F,EAAWrF,IAAIV,GAAK,GAE1BI,EAAM2F,EAAWxD,OAAOvC,GAEzBI,EAAMmB,EAAOgB,OAAOvC,IACb,GAGR6J,EAAEvH,MAAQ,eACHlC,EAAkB4H,KAAK7J,GAC7B8J,EAAgB7H,GACZkB,EAAOlB,GAAOgL,OACjBL,EAAe3K,GACfkG,EAAYlG,GACZA,EAAM2F,EAAY,IAAI5E,IACtB3B,EAAKY,EAAMb,GAAO,SAAAS,GACjBI,EAAM2F,EAAWrF,IAAIV,GAAK,MAE3BI,EAAMmB,EAAOe,UAIfuH,EAAE9J,QAAU,SACXsL,EACAC,cAGAhK,EADwB0G,KAAK7J,IACf4B,SAAQ,SAACwL,EAAavL,GACnCqL,EAAG3M,KAAK4M,EAASE,EAAK/K,IAAIT,GAAMA,EAAKwL,OAIvC3B,EAAEpJ,IAAM,SAAST,OACVI,EAAkB4H,KAAK7J,GAC7B8J,EAAgB7H,OACVlC,EAAQoD,EAAOlB,GAAOK,IAAIT,MAC5BI,EAAMkF,IAAelH,EAAYF,UAC7BA,KAEJA,IAAUkC,EAAMb,EAAMkB,IAAIT,UACtB9B,MAGFgG,EAAQsC,EAAYpG,EAAMiF,EAAOtB,EAAQ7F,EAAOkC,UACtD2K,EAAe3K,GACfA,EAAMmB,EAAOb,IAAIV,EAAKkE,GACfA,GAGR2F,EAAEhK,KAAO,kBACDyB,EAAO0G,KAAK7J,IAAc0B,QAGlCgK,EAAE4B,OAAS,wBACJC,EAAW1D,KAAKnI,oBAEpB8L,GAAiB,kBAAMC,EAAKH,YAC7BI,KAAM,eACCC,EAAIJ,EAASG,cAEfC,EAAEC,KAAaD,EAEZ,CACNC,MAAM,EACN7N,MAHa0N,EAAKnL,IAAIqL,EAAE5N,YAS5B2L,EAAEV,QAAU,wBACLuC,EAAW1D,KAAKnI,oBAEpB8L,GAAiB,kBAAMK,EAAK7C,aAC7B0C,KAAM,eACCC,EAAIJ,EAASG,UAEfC,EAAEC,KAAM,OAAOD,MACb5N,EAAQ8N,EAAKvL,IAAIqL,EAAE5N,aAClB,CACN6N,MAAM,EACN7N,MAAO,CAAC4N,EAAE5N,MAAOA,QAMrB2L,EAAE8B,GAAkB,kBACZ3D,KAAKmB,WAGNgC,EAnJU,GAkKZc,EAAY,oBAGRA,EAAoBhL,EAAgBwF,eACvCtI,GAAe,CACnBkC,IACAuD,EAAS6C,EACTpB,EAAQoB,EAASA,EAAOpB,EAAStC,IACjC6B,GAAW,EACXU,GAAY,EACZ/D,OAAOkD,EACPlF,EAAO0B,EACPsE,EAAQyC,KACRtE,EAAS,IAAIvC,IACbiD,GAAU,EACVwC,GAAW,GAELoB,KAhBR0C,EAAUuB,EA8GR5K,SA5FIwI,EAAIoC,EAASzL,iBAEnBlC,OAAOqK,eAAekB,EAAG,OAAQ,CAChCpJ,IAAK,kBACGa,EAAO0G,KAAK7J,IAAciN,QAKnCvB,EAAEvJ,IAAM,SAASpC,OACVkC,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GAEXA,EAAMmB,IAGPnB,EAAMmB,EAAMjB,IAAIpC,OAChBkC,EAAMsD,EAAQpD,IAAIpC,KAAUkC,EAAMmB,EAAMjB,IAAIF,EAAMsD,EAAQjD,IAAIvC,KAH1DkC,EAAMb,EAAMe,IAAIpC,IAQzB2L,EAAEhJ,IAAM,SAAS3C,OACVkC,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GACX4H,KAAK1H,IAAIpC,KACb8M,EAAe5K,GACfkG,EAAYlG,GACZA,EAAMmB,EAAOV,IAAI3C,IAEX8J,MAGR6B,EAAEtH,OAAS,SAASrE,OACd8J,KAAK1H,IAAIpC,UACN,MAGFkC,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GAChB4K,EAAe5K,GACfkG,EAAYlG,GAEXA,EAAMmB,EAAOgB,OAAOrE,MACnBkC,EAAMsD,EAAQpD,IAAIpC,IAChBkC,EAAMmB,EAAOgB,OAAOnC,EAAMsD,EAAQjD,IAAIvC,KAK3C2L,EAAEvH,MAAQ,eACHlC,EAAkB4H,KAAK7J,GAC7B8J,EAAgB7H,GACZkB,EAAOlB,GAAOgL,OACjBJ,EAAe5K,GACfkG,EAAYlG,GACZA,EAAMmB,EAAOe,UAIfuH,EAAE4B,OAAS,eACJrL,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GAChB4K,EAAe5K,GACRA,EAAMmB,EAAOkK,UAGrB5B,EAAEV,QAAU,eACL/I,EAAkB4H,KAAK7J,UAC7B8J,EAAgB7H,GAChB4K,EAAe5K,GACRA,EAAMmB,EAAO4H,WAGrBU,EAAEhK,KAAO,kBACDmI,KAAKyD,UAGb5B,EAAE8B,GAAkB,kBACZ3D,KAAKyD,UAGb5B,EAAE9J,QAAU,SAAiBsL,EAASC,WAC/BI,EAAW1D,KAAKyD,SAClBnH,EAASoH,EAASG,QACdvH,EAAOyH,MACdV,EAAG3M,KAAK4M,EAAShH,EAAOpG,MAAOoG,EAAOpG,MAAO8J,MAC7C1D,EAASoH,EAASG,QAIbI,EA9GU,GA0IlBpJ,EAAW,SAAU,CAAC6D,WAtJezF,EAAWwF,UAExC,IAAI0E,EAASlK,EAAQwF,IAoJIE,WAzBI1F,EAAWwF,UAExC,IAAIwF,EAAShL,EAAQwF,eC/TdyF,IACfpE,IACA2C,IACAxB,aC2FekD,EAAajO,UACrBA,WAQQkO,EAAiBlO,UACzBA,QTnFJ+E,EUpBEoJ,EACa,oBAAXC,QAAiD,iBAAhBA,OAAO,KACnCpL,EAAwB,oBAARC,IAChBC,EAAwB,oBAARC,IAChBkL,EACK,oBAAVvF,YACoB,IAApBA,MAAMC,WACM,oBAAZuF,QAKKxH,EAAmBqH,EAC7BC,OAAOG,IAAI,yBACR,kBAAkB,KAUXvN,EAA2BmN,EACrCC,OAAOG,IAAI,mBACV,qBAEStO,EAA6BkO,EACvCC,OAAOG,IAAI,eACV,iBAGSd,EACM,oBAAVW,QAAyBA,OAAOZ,UAAc,abvCjDjO,EAAS,GACX,kBACA,iDACA,mEACDiP,SAEA,uHACAA,KAGC,sHACA,sCACA,iEACA,oEACA,6FACA,+EACC,0CACA,8DACA,8DACA,gDACA,kFACDxH,SACK,6CAA+CA,MAEnD,kDACDyE,SACK,gCAAkCA,eAEvChH,4BACwBA,oFAAyFA,gDAEhH,wFACDxC,+JAC2JA,mBAE3JA,4CACwCA,eAExCA,6CACyCA,MAExC,yFCNCrB,EAAmBR,GAAAA,OAAOkC,UAAU7B,YA4B7BmB,GACO,oBAAZ0M,SAA2BA,QAAQ1M,QACvC0M,QAAQ1M,aACgC,IAAjCxB,OAAOqO,sBACd,SAAAlN,UACAnB,OAAOsO,oBAAoBnN,GAAKuG,OAC/B1H,OAAOqO,sBAAsBlN,KAEHnB,OAAOsO,oBAEzBhL,GACZtD,OAAOsD,2BACP,SAAmCX,OAE5B6E,EAAW,UACjBhG,GAAQmB,GAAQlB,SAAQ,SAAAC,GACvB8F,EAAI9F,GAAO1B,OAAO+H,yBAAyBpF,EAAQjB,MAE7C8F,GCnEHlD,GA4BF,GGyDSkE,GAAwC,CACpDrG,aAAIL,EAAOG,MACNA,IAASpC,EAAa,OAAOiC,MAE3BgG,EAAS9E,EAAOlB,OACjBE,EAAI8F,EAAQ7F,UAwInB,SAA2BH,EAAmBgG,EAAa7F,SACpDuB,EAAOqE,EAAuBC,EAAQ7F,UACrCuB,EACJ,UAAWA,EACVA,EAAK5D,gBAGL4D,EAAKrB,wBAALoM,EAAUnO,KAAK0B,EAAMmF,QACtBd,EA9IMqI,CAAkB1M,EAAOgG,EAAQ7F,OAEnCrC,EAAQkI,EAAO7F,UACjBH,EAAMkF,IAAelH,EAAYF,GAC7BA,EAIJA,IAAUgI,EAAK9F,EAAMb,EAAOgB,IAC/BgG,EAAYnG,GACJA,EAAMmB,EAAOhB,GAAeiG,EACnCpG,EAAMiF,EAAOtB,EACb7F,EACAkC,IAGKlC,GAERoC,aAAIF,EAAOG,UACHA,KAAQe,EAAOlB,IAEvBN,iBAAQM,UACAoM,QAAQ1M,QAAQwB,EAAOlB,KAE/BM,aACCN,EACAG,EACArC,OAEM4D,EAAOqE,EAAuB7E,EAAOlB,GAAQG,MAC/CuB,MAAAA,SAAAA,EAAMpB,WAGToB,EAAKpB,IAAIhC,KAAK0B,EAAMmF,EAAQrH,IACrB,MAEHkC,EAAMwE,EAAW,KAGf2C,EAAUrB,EAAK5E,EAAOlB,GAAQG,GAE9BwM,EAAiCxF,MAAAA,SAAAA,EAAUpJ,MAC7C4O,GAAgBA,EAAaxN,IAAUrB,SAC1CkC,EAAMmB,EAAOhB,GAAQrC,EACrBkC,EAAM2F,EAAUxF,IAAQ,GACjB,KAEJO,EAAG5C,EAAOqJ,UAAuB9C,IAAVvG,GAAuBoC,EAAIF,EAAMb,EAAOgB,IAClE,OAAO,EACRgG,EAAYnG,GACZkG,EAAYlG,UAIXA,EAAMmB,EAAOhB,KAAUrC,SAEZuG,IAAVvG,GAAuBqC,KAAQH,EAAMmB,IAEtCyL,OAAOC,MAAM/O,IAAU8O,OAAOC,MAAM7M,EAAMmB,EAAOhB,MAKnDH,EAAMmB,EAAOhB,GAAQrC,EACrBkC,EAAM2F,EAAUxF,IAAQ,IAJhB,GAOT2M,wBAAe9M,EAAOG,eAEWkE,IAA5ByB,EAAK9F,EAAMb,EAAOgB,IAAuBA,KAAQH,EAAMb,GAC1Da,EAAM2F,EAAUxF,IAAQ,EACxBgG,EAAYnG,GACZkG,EAAYlG,WAGLA,EAAM2F,EAAUxF,GAGpBH,EAAMmB,UAAcnB,EAAMmB,EAAMhB,IAC7B,GAIR8F,kCAAyBjG,EAAOG,OACzB4M,EAAQ7L,EAAOlB,GACf0B,EAAO0K,QAAQnG,yBAAyB8G,EAAO5M,UAChDuB,EACE,CACNC,UAAU,EACVC,iBAAc5B,EAAMC,GAA2C,WAATE,EACtD0B,WAAYH,EAAKG,WACjB/D,MAAOiP,EAAM5M,IALIuB,GAQnB6G,0BACCtL,EAAI,KAELkB,wBAAe6B,UACP9B,OAAOC,eAAe6B,EAAMb,IAEpC0L,0BACC5N,EAAI,MAQA0J,GAA8C,GACpDvH,EAAKsH,IAAa,SAAC9G,EAAKoN,GAEvBrG,GAAW/G,GAAO,kBACjBqN,UAAU,GAAKA,UAAU,GAAG,GACrBD,EAAGzP,MAAMqK,KAAMqF,eAGxBtG,GAAWmG,eAAiB,SAAS9M,EAAOG,wBACvCyC,sBAAWiK,MAAMK,SAAS/M,KAAelD,EAAI,IAE1C0J,GAAWrG,IAAKhC,KAAKsJ,KAAM5H,EAAOG,OAAMkE,IAEhDsC,GAAWrG,IAAM,SAASN,EAAOG,EAAMrC,wBAClC8E,sBAAoB,WAATzC,GAAqB0M,MAAMK,SAAS/M,KAAelD,EAAI,IAC/DyJ,GAAYpG,IAAKhC,KAAKsJ,KAAM5H,EAAM,GAAIG,EAAMrC,EAAOkC,EAAM,SCpMpDmN,GAAb,sBAKaC,qBAJWjB,UAEA,eA4BH,SAAC9K,EAAWgM,EAAcrK,MAEzB,mBAAT3B,GAAyC,mBAAXgM,EAAuB,KACzDC,EAAcD,EACpBA,EAAShM,MAEHkM,EAAOnC,SACN,SAEN/J,uBAAAA,IAAAA,EAAOiM,8BACJnQ,+BAAAA,2BAEIoQ,EAAKC,QAAQnM,GAAM,SAACyC,kBAAmBuJ,GAAO/O,cAAKkN,EAAM1H,UAAU3G,YAQxE+G,KAJkB,mBAAXmJ,GAAuBpQ,EAAI,QAChBoH,IAAlBrB,GAAwD,mBAAlBA,GACzC/F,EAAI,GAKDe,EAAYqD,GAAO,KAChB0B,EAAQU,EAAW2H,GACnBrE,EAAQX,EAAYgF,EAAM/J,OAAMgD,GAClCoJ,GAAW,MAEdvJ,EAASmJ,EAAOtG,GAChB0G,GAAW,UAGPA,EAAUrK,EAAYL,GACrBM,EAAWN,SAEM,oBAAZ2K,SAA2BxJ,aAAkBwJ,QAChDxJ,EAAOyJ,MACb,SAAAzJ,UACCpB,EAAkBC,EAAOC,GAClBiB,EAAcC,EAAQnB,MAE9B,SAAA7F,SACCkG,EAAYL,GACN7F,MAIT4F,EAAkBC,EAAOC,GAClBiB,EAAcC,EAAQnB,IACvB,IAAK1B,GAAwB,iBAATA,EAAmB,SAE9BgD,KADfH,EAASmJ,EAAOhM,MACU6C,EAAS7C,GAC/B6C,IAAWU,IAASV,OAASG,GAC7B+G,EAAKvF,GAAa9D,EAAOmC,GAAQ,GACjClB,EAAe,KACZyG,EAAa,GACbmE,EAAc,GACpBvL,EAAU,WAAWsC,EAA4BtD,EAAM6C,EAAQuF,EAAGmE,GAClE5K,EAAcyG,EAAGmE,UAEX1J,EACDjH,EAAI,GAAIoE,4BAG0B,SAACA,EAAWgM,MAEjC,mBAAThM,SACH,SAACrB,8BAAe7C,+BAAAA,2BACtBiO,EAAKyC,mBAAmB7N,GAAO,SAAC8D,UAAezC,gBAAKyC,UAAU3G,YAG5DkM,EAAkBQ,EAChB3F,EAASkH,EAAKoC,QAAQnM,EAAMgM,GAAQ,SAAC5D,EAAYmE,GACtDvE,EAAUI,EACVI,EAAiB+D,WAGK,oBAAZF,SAA2BxJ,aAAkBwJ,QAChDxJ,EAAOyJ,MAAK,SAAAG,SAAa,CAACA,EAAWzE,EAAUQ,MAEhD,CAAC3F,EAAQmF,EAAUQ,IAzGQ,kBAAvBuD,MAAAA,SAAAA,EAAQW,aAClBnG,KAAKoG,cAAcZ,EAAQW,YACM,kBAAvBX,MAAAA,SAAAA,EAAQa,aAClBrG,KAAKsG,cAAcd,EAAQa,uCAyG7BE,YAAA,SAAiC9M,GAC3BrD,EAAYqD,IAAOpE,EAAI,GACxBY,EAAQwD,KAAOA,EAAO8F,EAAQ9F,QAC5B0B,EAAQU,EAAWmE,MACnBb,EAAQX,EAAYwB,KAAMvG,OAAMgD,UACtC0C,EAAMhJ,GAAayI,GAAY,EAC/BnD,EAAWN,GACJgE,KAGRqH,YAAA,SACCtK,EACAd,OAEMhD,EAAoB8D,GAAUA,EAAc/F,yCAE5CiC,GAAUA,EAAMwG,GAAWvJ,EAAI,GAChC+C,EAAMkF,GAAYjI,EAAI,SAEZ8F,EAAS/C,EAAjBiF,SACPnC,EAAkBC,EAAOC,GAClBiB,OAAcI,EAAWtB,MAQjCmL,cAAA,SAAcpQ,QACR+H,EAAc/H,KASpBkQ,cAAA,SAAclQ,GACTA,IAAUqO,GACblP,EAAI,SAEAqH,EAAcxG,KAGpBuQ,aAAA,SAAkChN,EAASgI,OAGtC5H,MACCA,EAAI4H,EAAQ5L,OAAS,EAAGgE,GAAK,EAAGA,IAAK,KACnC6H,EAAQD,EAAQ5H,MACI,IAAtB6H,EAAMxE,KAAKrH,QAA6B,YAAb6L,EAAMC,GAAkB,CACtDlI,EAAOiI,EAAMxL,aAMX2D,GAAK,IACR4H,EAAUA,EAAQ/H,MAAMG,EAAI,QAGvB6M,EAAmBjM,EAAU,WAAW+G,SAC1CvL,EAAQwD,GAEJiN,EAAiBjN,EAAMgI,GAGxBzB,KAAK4F,QAAQnM,GAAM,SAACyC,UAC1BwK,EAAiBxK,EAAOuF,SAxL3B,GMZM3F,GAAQ,IAAIyJ,GAqBLK,GAAoB9J,GAAM8J,QAO1BK,GAA0CnK,GAAMmK,mBAAmBU,KAC/E7K,IAQYwK,GAAgBxK,GAAMwK,cAAcK,KAAK7K,IAQzCsK,GAAgBtK,GAAMsK,cAAcO,KAAK7K,IAOzC2K,GAAe3K,GAAM2K,aAAaE,KAAK7K,IAMvCyK,GAAczK,GAAMyK,YAAYI,KAAK7K,IAUrC0K,GAAc1K,GAAM0K,YAAYG,KAAK7K"}