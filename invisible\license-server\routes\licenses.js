const express = require('express');
const { verifyToken } = require('../middleware/auth');
const License = require('../models/License');
const User = require('../models/User');

const router = express.Router();

// 📋 GET USER'S LICENSES
router.get('/my-licenses', verifyToken, async (req, res) => {
    try {
        const licenses = await License.find({ userId: req.userId })
            .sort({ createdAt: -1 });

        const licensesWithStatus = licenses.map(license => license.getStatus());

        res.json({
            success: true,
            data: {
                licenses: licensesWithStatus,
                totalLicenses: licenses.length,
                activeLicenses: licenses.filter(l => l.isValid()).length
            }
        });

    } catch (error) {
        console.error('❌ License fetch error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch licenses'
        });
    }
});

// 🔍 VALIDATE LICENSE
router.post('/validate', verifyToken, async (req, res) => {
    try {
        const { licenseKey, hardwareId } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Find license
        const license = await License.findOne({
            licenseKey,
            userId: req.userId
        });

        if (!license) {
            return res.status(404).json({
                success: false,
                message: 'License not found'
            });
        }

        // Check if license is valid
        if (!license.isValid()) {
            return res.status(403).json({
                success: false,
                message: 'License is expired or invalid',
                data: {
                    license: license.getStatus()
                }
            });
        }

        // Hardware binding (if provided)
        if (hardwareId) {
            if (!license.hardwareId) {
                // First time activation - bind to hardware
                license.hardwareId = hardwareId;
                license.activationDate = new Date();
                await license.save();
            } else if (license.hardwareId !== hardwareId) {
                return res.status(403).json({
                    success: false,
                    message: 'License is bound to different hardware'
                });
            }
        }

        // Update last used
        license.lastUsed = new Date();
        await license.save();

        res.json({
            success: true,
            message: 'License is valid',
            data: {
                license: license.getStatus()
            }
        });

    } catch (error) {
        console.error('❌ License validation error:', error);
        res.status(500).json({
            success: false,
            message: 'License validation failed'
        });
    }
});

// 📸 USE SCREENSHOT
router.post('/use-screenshot', verifyToken, async (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Find license
        const license = await License.findOne({
            licenseKey,
            userId: req.userId
        });

        if (!license) {
            return res.status(404).json({
                success: false,
                message: 'License not found'
            });
        }

        // Check if license is valid
        if (!license.isValid()) {
            return res.status(403).json({
                success: false,
                message: 'License is expired or invalid',
                data: {
                    license: license.getStatus()
                }
            });
        }

        // Check if screenshots available
        if (license.screenshotsUsed >= license.screenshotsLimit) {
            return res.status(403).json({
                success: false,
                message: 'Screenshot limit reached. Please renew your license.',
                data: {
                    license: license.getStatus()
                }
            });
        }

        // Increment screenshot usage
        license.screenshotsUsed += 1;
        license.lastUsed = new Date();
        await license.save();

        // Update user stats
        const user = await User.findById(req.userId);
        user.totalScreenshotsUsed += 1;
        await user.save();

        res.json({
            success: true,
            message: 'Screenshot used successfully',
            data: {
                license: license.getStatus(),
                screenshotsRemaining: license.getRemainingScreenshots()
            }
        });

    } catch (error) {
        console.error('❌ Screenshot usage error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to use screenshot'
        });
    }
});

// 🔑 ACTIVATE LICENSE (NO AUTH REQUIRED - FOR CLIENT APP)
router.post('/activate', async (req, res) => {
    try {
        const { license_key, email, device_id } = req.body;

        if (!license_key || !email) {
            return res.status(400).json({
                success: false,
                error: 'License key and email are required'
            });
        }

        console.log('🔑 License activation attempt:', { license_key: license_key.substring(0, 8) + '...', email });

        // Find license by key
        const license = await License.findOne({ licenseKey: license_key });

        if (!license) {
            return res.status(404).json({
                success: false,
                error: 'Invalid license key'
            });
        }

        // Check if license is valid
        if (!license.isValid()) {
            return res.status(403).json({
                success: false,
                error: 'License is expired or limit reached'
            });
        }

        // Find user by email (for validation)
        const user = await User.findById(license.userId);
        if (!user || user.email !== email) {
            return res.status(403).json({
                success: false,
                error: 'Email does not match license owner'
            });
        }

        // Bind to device if provided
        if (device_id) {
            if (!license.hardwareId) {
                license.hardwareId = device_id;
                license.activationDate = new Date();
            } else if (license.hardwareId !== device_id) {
                return res.status(403).json({
                    success: false,
                    error: 'License is already bound to another device'
                });
            }
        }

        // Update last used
        license.lastUsed = new Date();
        await license.save();

        console.log('✅ License activated successfully for:', email);

        res.json({
            success: true,
            user_id: user._id.toString(),
            plan: license.tier === 1 ? 'basic' : 'premium',
            screenshot_limit: license.screenshotsLimit,
            expiry_date: license.expiryDate.toISOString(),
            screenshots_used: license.screenshotsUsed,
            screenshots_remaining: license.getRemainingScreenshots()
        });

    } catch (error) {
        console.error('❌ License activation error:', error);
        res.status(500).json({
            success: false,
            error: 'License activation failed'
        });
    }
});

// 🔍 VALIDATE LICENSE BY KEY (NO AUTH REQUIRED - FOR CLIENT APP)
router.get('/validate/:license_key', async (req, res) => {
    try {
        const { license_key } = req.params;

        if (!license_key) {
            return res.status(400).json({
                valid: false,
                error: 'License key is required'
            });
        }

        console.log('🔍 License validation for:', license_key.substring(0, 8) + '...');

        // Find license
        const license = await License.findOne({ licenseKey: license_key });

        if (!license) {
            return res.status(404).json({
                valid: false,
                error: 'License not found'
            });
        }

        // Update last used
        license.lastUsed = new Date();
        await license.save();

        const isValid = license.isValid();
        console.log('🔍 License validation result:', isValid);

        res.json({
            valid: isValid,
            plan: license.tier === 1 ? 'basic' : 'premium',
            screenshot_limit: license.screenshotsLimit,
            screenshots_used: license.screenshotsUsed,
            screenshots_remaining: license.getRemainingScreenshots(),
            expiry_date: license.expiryDate.toISOString(),
            days_remaining: license.getDaysRemaining()
        });

    } catch (error) {
        console.error('❌ License validation error:', error);
        res.status(500).json({
            valid: false,
            error: 'License validation failed'
        });
    }
});

// Simple test route
router.get('/test', (req, res) => {
    res.json({
        success: true,
        message: 'Licenses route working!'
    });
});

module.exports = router;
