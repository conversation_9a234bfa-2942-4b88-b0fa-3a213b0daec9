// const { app, BrowserWindow, globalShortcut, ipcMain, screen, nativeImage, desktopCapturer } = require('electron');
// const path = require('path');
// const fs = require('fs');
// const dotenv = require('dotenv');

// // Load environment variables from .env file
// dotenv.config();

// // Add this line to check if the API key is loaded
// console.log('QWEN_API_KEY from env:', process.env.QWEN_API_KEY ? 'Found (length: ' + process.env.QWEN_API_KEY.length + ')' : 'Not found');
// console.log('GEMINI_API_KEY from env:', process.env.GEMINI_API_KEY ? 'Found (length: ' + process.env.GEMINI_API_KEY.length + ')' : 'Not found');

// // 📁 ENSURE REQUIRED DIRECTORIES EXIST
// function ensureDirectoriesExist() {
//     const requiredDirs = [
//         path.join(__dirname, 'screenshots'),
//         path.join(__dirname, 'temp'),
//         path.join(__dirname, 'cache')
//     ];

//     requiredDirs.forEach(dir => {
//         try {
//             if (!fs.existsSync(dir)) {
//                 fs.mkdirSync(dir, { recursive: true });
//                 console.log(`✅ Created directory: ${dir}`);
//             } else {
//                 console.log(`✅ Directory exists: ${dir}`);
//             }
//         } catch (error) {
//             console.error(`❌ Failed to create directory ${dir}:`, error);
//         }
//     });
// }

// console.log('GROQ_API_KEY from env:', process.env.GROQ_API_KEY ? 'Found (length: ' + process.env.GROQ_API_KEY.length + ')' : 'Not found');

// // State management
// const state = {
//     mainWindow: null,
//     isWindowVisible: false,
//     windowPosition: { x: 0, y: 0 },
//     windowSize: { width: 800, height: 600 },
//     currentX: 100,
//     currentY: 100,
//     currentOpacity: 1,
//     step: 60,
//     conversationHistory: [],
//     lastScreenshot: null,
//     screenshotPath: null,
//     // Window resizing state
//     originalWidth: 800,
//     originalHeight: 600,
//     currentSizeIndex: 0,  // Index in the sizes array
//     windowSizes: [
//         { width: 800, height: 600 },  // 100% - Default
//         { width: 480, height: 320 },  // 60% - Smaller
//         { width: 360, height: 240 },  // 45% - Much smaller
//         { width: 240, height: 160 },  // 30% - Very small
//         { width: 150, height: 100 }   // 18.75% - SUPER STEALTH!
//     ]
// };

// // Window visibility functions
// function hideMainWindow() {
//     if (!state.mainWindow?.isDestroyed()) {
//         const bounds = state.mainWindow.getBounds();
//         state.windowPosition = { x: bounds.x, y: bounds.y };
//         state.windowSize = { width: bounds.width, height: bounds.height };
//         state.mainWindow.setIgnoreMouseEvents(true, { forward: true });
//         state.mainWindow.setOpacity(0);
//         state.isWindowVisible = false;
//         console.log('Window hidden, opacity set to 0');
//     }
// }

// function showMainWindow() {
//     if (!state.mainWindow?.isDestroyed()) {
//         if (state.windowPosition && state.windowSize) {
//             state.mainWindow.setBounds({
//                 ...state.windowPosition,
//                 ...state.windowSize
//             });
//         }
//         state.mainWindow.setIgnoreMouseEvents(false);
//         state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);
//         state.mainWindow.setVisibleOnAllWorkspaces(true, {
//             visibleOnFullScreen: true
//         });
//         state.mainWindow.setContentProtection(true);
//         state.mainWindow.setOpacity(0); // Set opacity to 0 before showing
//         state.mainWindow.showInactive(); // Use showInactive instead of show+focus
//         state.mainWindow.setOpacity(state.currentOpacity); // Then set opacity to current level

//         // 🚀 AUTO-FOCUS WINDOW FOR IMMEDIATE KEYBOARD CONTROL
//         setTimeout(() => {
//             if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                 state.mainWindow.focus();
//                 console.log('✅ Window auto-focused for immediate keyboard control');
//             }
//         }, 100); // Small delay to ensure window is fully shown

//         state.isWindowVisible = true;
//         console.log('Window shown with showInactive(), opacity set to', state.currentOpacity, '+ AUTO-FOCUSED');
//     }
// }

// function toggleMainWindow() {
//     console.log(`Toggling window. Current state: ${state.isWindowVisible ? 'visible' : 'hidden'}`);
//     if (state.isWindowVisible) {
//         hideMainWindow();
//     } else {
//         showMainWindow();
//     }
//     return { success: true, isVisible: state.isWindowVisible };
// }

// function decreaseOpacity() {
//     if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

//     state.currentOpacity = state.currentOpacity || 1;
//     state.currentOpacity = Math.max(0.1, state.currentOpacity - 0.1);
//     state.mainWindow.setOpacity(state.currentOpacity);
//     console.log('Opacity decreased to:', state.currentOpacity);
// }

// function increaseOpacity() {
//     if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

//     state.currentOpacity = state.currentOpacity || 1;
//     state.currentOpacity = Math.min(1, state.currentOpacity + 0.1);
//     state.mainWindow.setOpacity(state.currentOpacity);
//     console.log('Opacity increased to:', state.currentOpacity);
// }

// function resetView() {
//     if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

//     // Reset position to center of screen
//     const { width, height } = screen.getPrimaryDisplay().workAreaSize;
//     const windowWidth = 800;
//     const windowHeight = 600;

//     state.mainWindow.setBounds({
//         x: Math.floor((width - windowWidth) / 2),
//         y: Math.floor((height - windowHeight) / 2),
//         width: windowWidth,
//         height: windowHeight
//     });

//     // Reset opacity to full
//     state.currentOpacity = 1;
//     state.mainWindow.setOpacity(1);

//     // Make sure it's visible
//     if (!state.isWindowVisible) {
//         showMainWindow();
//     }

//     console.log('View reset - centered, full opacity, visible');
// }

// // Window movement functions
// function moveWindowHorizontal(updateFn) {
//     if (!state.mainWindow) return { success: false };
//     state.currentX = updateFn(state.currentX);
//     state.mainWindow.setPosition(
//         Math.round(state.currentX),
//         Math.round(state.currentY)
//     );
//     return { success: true };
// }

// function moveWindowVertical(updateFn) {
//     if (!state.mainWindow) return { success: false };
//     state.currentY = updateFn(state.currentY);
//     state.mainWindow.setPosition(
//         Math.round(state.currentX),
//         Math.round(state.currentY)
//     );
//     return { success: true };
// }

// function moveWindowLeft() {
//     return moveWindowHorizontal(x => x - state.step);
// }

// function moveWindowRight() {
//     return moveWindowHorizontal(x => x + state.step);
// }

// function moveWindowUp() {
//     return moveWindowVertical(y => y - state.step);
// }

// function moveWindowDown() {
//     return moveWindowVertical(y => y + state.step);
// }

// // Window resizing functions
// function shrinkWindow() {
//     console.log('🔧 shrinkWindow() called');
//     console.log('Window exists:', !!state.mainWindow);
//     console.log('Window destroyed:', state.mainWindow ? state.mainWindow.isDestroyed() : 'N/A');

//     if (!state.mainWindow || state.mainWindow.isDestroyed()) {
//         console.log('❌ No window or window destroyed');
//         return { success: false };
//     }

//     console.log('Current size index:', state.currentSizeIndex);
//     console.log('Available sizes:', state.windowSizes.length);

//     // Move to next smaller size
//     const nextIndex = state.currentSizeIndex + 1;

//     if (nextIndex >= state.windowSizes.length) {
//         console.log('⚠️ Window already at minimum size');
//         return { success: true, message: 'Already at minimum size' };
//     }

//     state.currentSizeIndex = nextIndex;
//     const newSize = state.windowSizes[nextIndex];

//     console.log(`New dimensions: ${newSize.width}x${newSize.height}`);

//     // Get current position to maintain center
//     const currentBounds = state.mainWindow.getBounds();
//     console.log('Current bounds:', currentBounds);

//     const centerX = currentBounds.x + currentBounds.width / 2;
//     const centerY = currentBounds.y + currentBounds.height / 2;
//     console.log(`Center: ${centerX}, ${centerY}`);

//     // Calculate new position to keep window centered
//     const newX = Math.round(centerX - newSize.width / 2);
//     const newY = Math.round(centerY - newSize.height / 2);
//     console.log(`New position: ${newX}, ${newY}`);

//     try {
//         state.mainWindow.setBounds({
//             x: newX,
//             y: newY,
//             width: newSize.width,
//             height: newSize.height
//         });

//         const percentage = Math.round((newSize.width / state.originalWidth) * 100);
//         console.log(`✅ Window shrunk to ${percentage}% (${newSize.width}x${newSize.height})`);
//         return { success: true, percentage: percentage, size: newSize };
//     } catch (error) {
//         console.error('❌ Error setting window bounds:', error);
//         return { success: false, error: error.message };
//     }
// }

// function resetWindowSize() {
//     console.log('🔧 resetWindowSize() called');
//     console.log('Window exists:', !!state.mainWindow);

//     if (!state.mainWindow || state.mainWindow.isDestroyed()) {
//         console.log('❌ No window or window destroyed');
//         return { success: false };
//     }

//     console.log('Resetting size index from', state.currentSizeIndex, 'to 0');
//     state.currentSizeIndex = 0;
//     const originalSize = state.windowSizes[0];

//     // Get current position to maintain center
//     const currentBounds = state.mainWindow.getBounds();
//     console.log('Current bounds:', currentBounds);

//     const centerX = currentBounds.x + currentBounds.width / 2;
//     const centerY = currentBounds.y + currentBounds.height / 2;
//     console.log(`Center: ${centerX}, ${centerY}`);

//     // Calculate new position to keep window centered
//     const newX = Math.round(centerX - originalSize.width / 2);
//     const newY = Math.round(centerY - originalSize.height / 2);
//     console.log(`New position: ${newX}, ${newY}`);

//     try {
//         state.mainWindow.setBounds({
//             x: newX,
//             y: newY,
//             width: originalSize.width,
//             height: originalSize.height
//         });
//         console.log(`✅ Window reset to original size (${originalSize.width}x${originalSize.height})`);
//         return { success: true, size: originalSize };
//     } catch (error) {
//         console.error('❌ Error resetting window bounds:', error);
//         return { success: false, error: error.message };
//     }
// }

// // Screenshot functionality with enhanced error handling
// async function takeScreenshot() {
//     try {
//         console.log('🔥 Taking screenshot...');

//         // Check if app has screen capture permissions
//         const hasPermission = await checkScreenCapturePermission();
//         if (!hasPermission) {
//             throw new Error('Screen capture permission denied. Please run as Administrator or grant screen recording permissions.');
//         }

//         // Get the primary display
//         const primaryDisplay = screen.getPrimaryDisplay();
//         const { width, height } = primaryDisplay.workAreaSize;
//         console.log(`📐 Screen dimensions: ${width}x${height}`);

//         // Create a screenshot using desktopCapturer with enhanced options
//         const sources = await desktopCapturer.getSources({
//             types: ['screen'],
//             thumbnailSize: { width: Math.min(width, 1920), height: Math.min(height, 1080) },
//             fetchWindowIcons: false
//         });

//         console.log(`📸 Found ${sources.length} screen sources`);

//         if (sources.length > 0) {
//             const screenshot = sources[0].thumbnail;
//             console.log(`📊 Screenshot size: ${screenshot.getSize().width}x${screenshot.getSize().height}`);

//             // Use user's AppData directory for writable storage
//             const userDataPath = app.getPath('userData');
//             const screenshotsDir = path.join(userDataPath, 'screenshots');
//             const screenshotPath = path.join(screenshotsDir, `screenshot_${Date.now()}.png`);

//             // Create screenshots directory if it doesn't exist
//             if (!fs.existsSync(screenshotsDir)) {
//                 try {
//                     fs.mkdirSync(screenshotsDir, { recursive: true });
//                     console.log('✅ Created screenshots directory:', screenshotsDir);
//                 } catch (dirError) {
//                     console.error('❌ Failed to create screenshots directory:', dirError);
//                     throw new Error(`Failed to create screenshots directory: ${dirError.message}`);
//                 }
//             }

//             // Save screenshot with error handling
//             try {
//                 const pngBuffer = screenshot.toPNG();
//                 fs.writeFileSync(screenshotPath, pngBuffer);
//                 console.log(`✅ Screenshot saved: ${screenshotPath} (${pngBuffer.length} bytes)`);
//             } catch (saveError) {
//                 console.error('❌ Failed to save screenshot:', saveError);
//                 throw new Error(`Failed to save screenshot: ${saveError.message}`);
//             }

//             state.lastScreenshot = screenshot;
//             state.screenshotPath = screenshotPath;

//             // Notify the renderer process
//             if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                 state.mainWindow.webContents.send('screenshot-taken', {
//                     path: screenshotPath,
//                     timestamp: new Date().toISOString()
//                 });

//                 // 🚀 ENSURE WINDOW FOCUS FOR SEAMLESS WORKFLOW
//                 // Focus the window silently so Ctrl+Enter works immediately
//                 setTimeout(() => {
//                     if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                         state.mainWindow.focus();
//                         console.log('✅ Invisible window focused for seamless Ctrl+Enter workflow');
//                     }
//                 }, 150); // Small delay to ensure screenshot processing is complete
//             }

//             return { success: true, path: screenshotPath };
//         } else {
//             throw new Error('No screen sources found. This may be due to permission restrictions or security software blocking screen capture.');
//         }
//     } catch (error) {
//         console.error('❌ Error taking screenshot:', error);
//         return { success: false, error: error.message };
//     }
// }

// // Check screen capture permissions
// async function checkScreenCapturePermission() {
//     try {
//         // Try to get screen sources to test permissions
//         const testSources = await desktopCapturer.getSources({
//             types: ['screen'],
//             thumbnailSize: { width: 150, height: 150 }
//         });
//         return testSources.length > 0;
//     } catch (error) {
//         console.error('❌ Screen capture permission check failed:', error);
//         return false;
//     }
// }

// async function analyzeScreenshot() {
//     try {
//         if (!state.screenshotPath || !fs.existsSync(state.screenshotPath)) {
//             throw new Error('No screenshot available. Please take a screenshot first using Ctrl+H');
//         }

//         console.log('Starting DSA problem analysis...');

//         // Notify the renderer process to start analysis
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('analyze-screenshot', {
//                 path: state.screenshotPath,
//                 timestamp: new Date().toISOString()
//             });
//         }

//         return { success: true, message: 'Analysis started' };
//     } catch (error) {
//         console.error('Error analyzing screenshot:', error);
//         return { success: false, error: error.message };
//     }
// }

// // Add this handler for chat messages
// function setupIpcHandlers() {
//     // Your existing handlers for window movement, etc.
//     ipcMain.handle('toggle-window', toggleMainWindow);
//     ipcMain.handle('move-window-left', moveWindowLeft);
//     ipcMain.handle('move-window-right', moveWindowRight);
//     ipcMain.handle('move-window-up', moveWindowUp);
//     ipcMain.handle('move-window-down', moveWindowDown);
//     ipcMain.handle('decrease-opacity', () => { decreaseOpacity(); return { success: true }; });
//     ipcMain.handle('increase-opacity', () => { increaseOpacity(); return { success: true }; });
//     ipcMain.handle('reset-view', () => { resetView(); return { success: true }; });
//     ipcMain.handle('quit-app', () => { app.quit(); return { success: true }; });

//     // Window resizing handlers
//     ipcMain.handle('shrink-window', () => { return shrinkWindow(); });
//     ipcMain.handle('reset-window-size', () => { return resetWindowSize(); });

//     // Screenshot handlers
//     ipcMain.handle('take-screenshot', takeScreenshot);
//     ipcMain.handle('analyze-screenshot', analyzeScreenshot);

//     // Chat message handler
//     ipcMain.handle('send-message', async (_, message) => {
//         try {
//             console.log("Received message:", message);

//             // Add user message to history
//             state.conversationHistory.push({ role: "user", content: message });

//             // Keep conversation history to a reasonable size
//             if (state.conversationHistory.length > 20) {
//                 state.conversationHistory = state.conversationHistory.slice(-20);
//             }

//             // The frontend will handle the API call directly
//             return { success: true };
//         } catch (error) {
//             console.error('Error:', error);
//             return { success: false, error: error.message };
//         }
//     });

//     // Save API key handler with proper writable directory
//     ipcMain.handle('save-api-key', async (_, key, type = 'qwen') => {
//         try {
//             // Use user's AppData directory for writable storage
//             const userDataPath = app.getPath('userData');

//             // Ensure the directory exists
//             if (!fs.existsSync(userDataPath)) {
//                 fs.mkdirSync(userDataPath, { recursive: true });
//             }

//             let keyPath;
//             if (type === 'qwen') {
//                 keyPath = path.join(userDataPath, 'qwen-api-key.txt');
//             } else if (type === 'gemini') {
//                 keyPath = path.join(userDataPath, 'gemini-api-key.txt');
//             } else if (type === 'openai') {
//                 keyPath = path.join(userDataPath, 'openai-api-key.txt');
//             } else if (type === 'anthropic') {
//                 keyPath = path.join(userDataPath, 'anthropic-api-key.txt');
//             } else if (type === 'deepgram') {
//                 keyPath = path.join(userDataPath, 'deepgram-api-key.txt');
//             } else if (type === 'whisper') {
//                 keyPath = path.join(userDataPath, 'whisper-api-key.txt');
//             } else if (type === 'groq') {
//                 keyPath = path.join(userDataPath, 'groq-api-key.txt');
//             } else {
//                 keyPath = path.join(userDataPath, 'api-key.txt');
//             }

//             fs.writeFileSync(keyPath, key, 'utf8');
//             console.log(`✅ ${type.toUpperCase()} API key saved successfully to:`, keyPath);
//             return { success: true, message: `${type.toUpperCase()} API key saved successfully!` };
//         } catch (error) {
//             console.error(`❌ Error saving ${type} API key:`, error);
//             return { success: false, error: error.message };
//         }
//     });

//     // Read file as base64 handler
//     ipcMain.handle('read-file-as-base64', async (_, filePath) => {
//         try {
//             console.log('Reading file as base64:', filePath);

//             if (!fs.existsSync(filePath)) {
//                 throw new Error(`File does not exist: ${filePath}`);
//             }

//             const fileBuffer = fs.readFileSync(filePath);
//             const base64Data = fileBuffer.toString('base64');

//             console.log(`Successfully read file as base64, size: ${base64Data.length} characters`);
//             return { success: true, data: base64Data };
//         } catch (error) {
//             console.error('Error reading file as base64:', error);
//             return { success: false, error: error.message };
//         }
//     });

//     // Get API key handler with proper writable directory
//     ipcMain.handle('get-api-key', async (_, type = 'qwen') => {
//         try {
//             const userDataPath = app.getPath('userData');

//             if (type === 'qwen') {
//                 // Try to get Qwen API key from environment variable
//                 const envApiKey = process.env.QWEN_API_KEY;
//                 if (envApiKey) {
//                     console.log('Qwen API key loaded from environment variable');
//                     return { success: true, key: envApiKey.trim() };
//                 }

//                 // Fall back to user data file if environment variable is not set
//                 const keyPath = path.join(userDataPath, 'qwen-api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8').trim();
//                     console.log('Qwen API key loaded from user data file, length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('No Qwen API key found in environment or user data file');
//                 return { success: true, key: '' };
//             } else if (type === 'gemini') {
//                 // Try to get Gemini API key from environment variable
//                 const envApiKey = process.env.GEMINI_API_KEY;
//                 if (envApiKey) {
//                     console.log('Gemini API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 // Fall back to file if environment variable is not set
//                 const keyPath = path.join(__dirname, 'gemini-api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8');
//                     console.log('Gemini API key loaded from file, length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('No Gemini API key found in environment or file');
//                 return { success: true, key: '' };
//             } else if (type === 'openai') {
//                 // OpenAI API key
//                 const envApiKey = process.env.OPENAI_API_KEY;
//                 if (envApiKey) {
//                     console.log('OpenAI API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 const keyPath = path.join(__dirname, 'api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8');
//                     console.log('OpenAI API key loaded from file, length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('No OpenAI API key found in environment or file');
//                 return { success: true, key: '' };
//             } else if (type === 'anthropic') {
//                 // Anthropic API key
//                 const envApiKey = process.env.ANTHROPIC_API_KEY;
//                 if (envApiKey) {
//                     console.log('Anthropic API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 const keyPath = path.join(__dirname, 'anthropic-api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8');
//                     console.log('Anthropic API key loaded from file, length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('No Anthropic API key found in environment or file');
//                 return { success: true, key: '' };
//             } else if (type === 'deepgram') {
//                 // Deepgram API key
//                 const envApiKey = process.env.DEEPGRAM_API_KEY;
//                 if (envApiKey) {
//                     console.log('🎤 Deepgram API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 const keyPath = path.join(__dirname, 'deepgram-api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8').trim();
//                     console.log('🎤 Deepgram API key loaded from file, length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('❌ No Deepgram API key found in environment or file');
//                 return { success: true, key: '' };
//             } else if (type === 'groq') {
//                 // Groq API key
//                 const envApiKey = process.env.GROQ_API_KEY;
//                 if (envApiKey) {
//                     console.log('⚡ Groq API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 console.log('❌ No Groq API key found in environment or file');
//                 return { success: true, key: '' };
//             } else if (type === 'whisper') {
//                 // Whisper API key
//                 const envApiKey = process.env.WHISPER_API_KEY;
//                 if (envApiKey) {
//                     console.log('🎤 Whisper API key loaded from environment variable');
//                     return { success: true, key: envApiKey };
//                 }

//                 console.log('❌ No Whisper API key found in environment or file');
//                 return { success: true, key: '' };
//             } else {
//                 // Default to OpenAI for backward compatibility
//                 const envApiKey = process.env.OPENAI_API_KEY;
//                 if (envApiKey) {
//                     console.log('OpenAI API key loaded from environment variable (default)');
//                     return { success: true, key: envApiKey };
//                 }

//                 const keyPath = path.join(__dirname, 'api-key.txt');
//                 if (fs.existsSync(keyPath)) {
//                     const key = fs.readFileSync(keyPath, 'utf8');
//                     console.log('OpenAI API key loaded from file (default), length:', key.length);
//                     return { success: true, key: key };
//                 }

//                 console.log('No API key found for type:', type);
//                 return { success: true, key: '' };
//             }
//         } catch (error) {
//             console.error('Error getting API key:', error);
//             return { success: false, error: error.message };
//         }
//     });
// }

// // Create the invisible window
// function createWindow() {
//     // Create the browser window
//     state.mainWindow = new BrowserWindow({
//         width: 800,
//         height: 600,
//         minWidth: 150,
//         minHeight: 100,
//         x: state.currentX,
//         y: 50,
//         alwaysOnTop: true,
//         webPreferences: {
//             nodeIntegration: false,
//             contextIsolation: true,
//             preload: path.join(__dirname, 'preload.js'),
//             scrollBounce: true,
//             webSecurity: false,
//             cache: false
//         },
//         show: true,
//         frame: false,
//         transparent: true,
//         fullscreenable: false,
//         hasShadow: false,
//         opacity: 0.0,  // Start with zero opacity (invisible)
//         backgroundColor: "#00000000",
//         focusable: true,
//         skipTaskbar: true,
//         type: "panel",
//         paintWhenInitiallyHidden: true,
//         titleBarStyle: "hidden",
//         enableLargerThanScreen: true,
//         movable: true
//     });

//     // Handle microphone permissions
//     state.mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
//         console.log(`🎤 Permission requested: ${permission}`);
//         if (permission === 'microphone' || permission === 'media') {
//             console.log('🎤 Microphone permission requested - GRANTED');
//             callback(true);
//         } else {
//             console.log(`❌ Permission denied for: ${permission}`);
//             callback(false);
//         }
//     });

//     // Also handle permission check requests
//     state.mainWindow.webContents.session.setPermissionCheckHandler((webContents, permission, requestingOrigin) => {
//         console.log(`🔍 Permission check: ${permission} from ${requestingOrigin}`);
//         if (permission === 'microphone' || permission === 'media') {
//             console.log('🎤 Microphone permission check - GRANTED');
//             return true;
//         }
//         return false;
//     });

//     // Load the index.html file
//     state.mainWindow.loadFile(path.join(__dirname, 'index.html'));

//     // Enhanced screen capture resistance
//     state.mainWindow.setContentProtection(true);

//     state.mainWindow.setVisibleOnAllWorkspaces(true, {
//         visibleOnFullScreen: true
//     });
//     state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);

//     // Additional screen capture resistance settings
//     if (process.platform === "darwin") {
//         // Prevent window from being captured in screenshots
//         state.mainWindow.setHiddenInMissionControl(true);
//         state.mainWindow.setWindowButtonVisibility(false);
//         state.mainWindow.setBackgroundColor("#00000000");

//         // Prevent window from being included in window switcher
//         state.mainWindow.setSkipTaskbar(true);

//         // Disable window shadow
//         state.mainWindow.setHasShadow(false);
//     }

//     // Prevent the window from being captured by screen recording
//     state.mainWindow.webContents.setBackgroundThrottling(false);
//     state.mainWindow.webContents.setFrameRate(60);

//     // Set up window listeners
//     state.mainWindow.on("move", () => {
//         const bounds = state.mainWindow.getBounds();
//         state.windowPosition = { x: bounds.x, y: bounds.y };
//         state.currentX = bounds.x;
//         state.currentY = bounds.y;
//     });

//     state.mainWindow.on("resize", () => {
//         const bounds = state.mainWindow.getBounds();
//         state.windowSize = { width: bounds.width, height: bounds.height };
//     });

//     state.mainWindow.on("closed", () => {
//         state.mainWindow = null;
//     });

//     // Initialize window state
//     const bounds = state.mainWindow.getBounds();
//     state.windowPosition = { x: bounds.x, y: bounds.y };
//     state.windowSize = { width: bounds.width, height: bounds.height };
//     state.currentX = bounds.x;
//     state.currentY = bounds.y;
//     state.isWindowVisible = false;

//     // Set up IPC handlers
//     setupIpcHandlers();
// }

// // App initialization
// app.whenReady().then(() => {
//     console.log('🚀 Invisible Assessment Tool starting...');

//     // Ensure required directories exist before creating window
//     ensureDirectoriesExist();

//     createWindow();
//     console.log('✅ Main window created');

//     // Register global shortcuts with error handling
//     try {
//         const ctrlBRegistered = globalShortcut.register('CommandOrControl+B', toggleMainWindow);
//         console.log('🔥 Ctrl+B shortcut registered:', ctrlBRegistered);

//         if (!ctrlBRegistered) {
//             console.error('❌ Failed to register Ctrl+B shortcut');
//         }

//         globalShortcut.register('CommandOrControl+Left', moveWindowLeft);
//         globalShortcut.register('CommandOrControl+Right', moveWindowRight);
//         globalShortcut.register('CommandOrControl+Up', moveWindowUp);
//         globalShortcut.register('CommandOrControl+Down', moveWindowDown);
//     } catch (error) {
//         console.error('❌ Error registering global shortcuts:', error);
//     }

//     // Screenshot shortcuts with enhanced error handling and fallback
//     const screenshotRegistered = globalShortcut.register('CommandOrControl+H', async () => {
//         console.log('🔥 Ctrl+H pressed - Taking screenshot');
//         try {
//             const result = await takeScreenshot();
//             if (result.success) {
//                 console.log('✅ Screenshot taken successfully:', result.path);
//                 // Show brief notification that screenshot was taken
//                 if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                     state.mainWindow.webContents.send('show-notification', {
//                         type: 'success',
//                         message: '📸 Screenshot captured!',
//                         duration: 2000
//                     });
//                 }
//             } else {
//                 console.error('❌ Screenshot failed:', result.error);
//                 // Show error notification
//                 if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                     state.mainWindow.webContents.send('show-notification', {
//                         type: 'error',
//                         message: '❌ Screenshot failed: ' + result.error,
//                         duration: 5000
//                     });
//                 }
//             }
//         } catch (error) {
//             console.error('❌ Screenshot error:', error);
//             // Show error notification
//             if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//                 state.mainWindow.webContents.send('show-notification', {
//                     type: 'error',
//                     message: '❌ Screenshot error: ' + error.message,
//                     duration: 5000
//                 });
//             }
//         }
//     });
//     console.log('📸 Ctrl+H screenshot shortcut registered:', screenshotRegistered);

//     // If global shortcut registration failed, show warning
//     if (!screenshotRegistered) {
//         console.error('❌ CRITICAL: Failed to register Ctrl+H screenshot shortcut!');
//         console.error('💡 SOLUTION: Run as Administrator or check for conflicting software');
//     }

//     // 🚀 GLOBAL ANALYSIS SHORTCUTS FOR SEAMLESS WORKFLOW
//     globalShortcut.register('CommandOrControl+Return', () => {
//         console.log('Ctrl+Enter pressed globally - Triggering analysis');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('global-analyze-screenshots');
//         }
//     });

//     // Note: Ctrl+R is handled in renderer for mode-specific behavior (reset vs fast coding)

//     globalShortcut.register('CommandOrControl+Shift+S', () => {
//         console.log('Ctrl+Shift+S pressed globally - MCQ fast mode');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('global-mcq-fast');
//         }
//     });

//     // Mode switching shortcuts
//     globalShortcut.register('CommandOrControl+D', () => {
//         console.log('Ctrl+D pressed - Switching to DSA mode');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-mode', 'dsa');
//         }
//     });
//     globalShortcut.register('CommandOrControl+C', () => {
//         console.log('Ctrl+C pressed - Switching to Chatbot mode');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-mode', 'chatbot');
//         }
//     });
//     globalShortcut.register('CommandOrControl+N', () => {
//         console.log('Ctrl+N pressed - Switching to Interview Copilot mode');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-mode', 'interview');
//         }
//     });

//     // Opacity control shortcuts
//     globalShortcut.register('CommandOrControl+[', () => {
//         console.log('Ctrl+[ pressed - Decreasing opacity');
//         decreaseOpacity();
//     });
//     globalShortcut.register('CommandOrControl+]', () => {
//         console.log('Ctrl+] pressed - Increasing opacity');
//         increaseOpacity();
//     });

//     // Note: Ctrl+R is handled in renderer for mode-specific behavior

//     // Quit app shortcut
//     globalShortcut.register('CommandOrControl+Q', () => {
//         console.log('Ctrl+Q pressed - Quitting app');
//         app.quit();
//     });

//     // Window resizing shortcuts
//     globalShortcut.register('CommandOrControl+M', () => {
//         console.log('🔥 GLOBAL Ctrl+M pressed - Shrinking window');
//         const result = shrinkWindow();
//         console.log('Shrink result:', result);
//     });

//     globalShortcut.register('CommandOrControl+Shift+M', () => {
//         console.log('🔥 GLOBAL Ctrl+Shift+M pressed - Resetting window size');
//         const result = resetWindowSize();
//         console.log('Reset result:', result);
//     });

//     // 🎤 INTERVIEW MODE TOGGLE SHORTCUT
//     globalShortcut.register('CommandOrControl+Shift+L', () => {
//         console.log('🎤 GLOBAL Ctrl+Shift+L pressed - Toggling interview listening');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('toggle-interview-listening');
//         }
//     });

//     // 🔧 DEBUG ASSISTANT SHORTCUTS
//     globalShortcut.register('CommandOrControl+Shift+D', () => {
//         console.log('🔧 GLOBAL Ctrl+Shift+D pressed - Debug session analysis');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('debug-analyze-session');
//         }
//     });

//     globalShortcut.register('CommandOrControl+Shift+A', () => {
//         console.log('🔧 GLOBAL Ctrl+Shift+A pressed - Add to debug session');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('debug-add-screenshot');
//         }
//     });

//     globalShortcut.register('CommandOrControl+Shift+X', () => {
//         console.log('🔧 GLOBAL Ctrl+Shift+X pressed - Clear debug session');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('debug-clear-session');
//         }
//     });

//     // ⚙️ API SWITCHING SHORTCUTS
//     globalShortcut.register('CommandOrControl+W', () => {
//         console.log('⚙️ GLOBAL Ctrl+W pressed - Switch to Qwen API');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-api', 'qwen');
//         }
//     });

//     globalShortcut.register('CommandOrControl+G', () => {
//         console.log('⚙️ GLOBAL Ctrl+G pressed - Switch to Gemini API');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-api', 'gemini');
//         }
//     });

//     globalShortcut.register('CommandOrControl+O', () => {
//         console.log('⚙️ GLOBAL Ctrl+O pressed - Switch to OpenAI API');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-api', 'openai');
//         }
//     });

//     // 🛠️ UTILITY SHORTCUTS
//     globalShortcut.register('CommandOrControl+L', () => {
//         console.log('🛠️ GLOBAL Ctrl+L pressed - Clear chat');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('clear-chat');
//         }
//     });

//     globalShortcut.register('CommandOrControl+S', () => {
//         console.log('⚙️ GLOBAL Ctrl+S pressed - Open settings');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('open-settings');
//         }
//     });

//     // 📋 CODE COPYING SHORTCUTS
//     globalShortcut.register('CommandOrControl+Shift+V', () => {
//         console.log('📋 GLOBAL Ctrl+Shift+V pressed - Copy last code block (alternative)');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('copy-last-code');
//         }
//     });

//     // 🏃‍♂ FAST PROCESSING SHORTCUTS
//     globalShortcut.register('CommandOrControl+R', () => {
//         console.log('🏃‍♂ GLOBAL Ctrl+R pressed - Fast coding processing');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('fast-coding-processing');
//         }
//     });

//     // 💬 CHATBOT INPUT FOCUS
//     globalShortcut.register('CommandOrControl+I', () => {
//         console.log('💬 GLOBAL Ctrl+I pressed - Focus input field');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('focus-input-field');
//         }
//     });

//     // ⚙️ ANTHROPIC API SWITCHING
//     globalShortcut.register('CommandOrControl+Alt+A', () => {
//         console.log('⚙️ GLOBAL Ctrl+Alt+A pressed - Switch to Anthropic API');
//         if (state.mainWindow && !state.mainWindow.isDestroyed()) {
//             state.mainWindow.webContents.send('switch-api', 'anthropic');
//         }
//     });

//     app.on('activate', function () {
//         if (BrowserWindow.getAllWindows().length === 0) createWindow();
//     });

//     // Log successful startup
//     console.log('🎉 Invisible Assessment Tool started successfully!');
//     console.log('💡 Press Ctrl+B to show interface');

// }).catch(error => {
//     console.error('❌ Failed to start Invisible Assessment Tool:', error);
// });

// // Handle second instance
// app.on("second-instance", () => {
//     if (!state.mainWindow) {
//         createWindow();
//     } else {
//         if (state.mainWindow.isMinimized()) state.mainWindow.restore();
//         state.mainWindow.focus();
//     }
// });

// // Prevent multiple instances of the app
// if (!app.requestSingleInstanceLock()) {
//     app.quit();
// } else {
//     app.on("window-all-closed", () => {
//         if (process.platform !== "darwin") {
//             app.quit();
//         }
//     });
// }

// app.on("activate", () => {
//     if (BrowserWindow.getAllWindows().length === 0) {
//         createWindow();
//     }
// });

// // Clean up shortcuts when app is quitting
// app.on('will-quit', () => {
//     globalShortcut.unregisterAll();
// });



const { app, BrowserWindow, globalShortcut, ipcMain, screen, nativeImage, desktopCapturer } = require('electron');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// 🔧 ENHANCED ENVIRONMENT LOADING FOR INSTALLED SOFTWARE
// Try multiple .env file locations for different deployment scenarios
const envPaths = [
    path.join(__dirname, '.env'),                    // Development
    path.join(__dirname, '..', '.env'),              // One level up
    path.join(__dirname, 'resources', 'app', '.env'), // Electron packaged
    path.join(process.resourcesPath, 'app', '.env'),  // Installed software
    path.join(process.cwd(), '.env')                  // Current working directory
];

let envLoaded = false;
for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
        console.log(`🔧 Loading .env from: ${envPath}`);
        dotenv.config({ path: envPath });
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    console.log('⚠️ No .env file found, using system environment variables only');
    dotenv.config(); // Fallback to default behavior
}

// Add this line to check if the API key is loaded
console.log('QWEN_API_KEY from env:', process.env.QWEN_API_KEY ? 'Found (length: ' + process.env.QWEN_API_KEY.length + ')' : 'Not found');
console.log('GEMINI_API_KEY from env:', process.env.GEMINI_API_KEY ? 'Found (length: ' + process.env.GEMINI_API_KEY.length + ')' : 'Not found');
console.log('GROQ_API_KEY from env:', process.env.GROQ_API_KEY ? 'Found (length: ' + process.env.GROQ_API_KEY.length + ')' : 'Not found');

// State management
const state = {
    mainWindow: null,
    isWindowVisible: false,
    windowPosition: { x: 0, y: 0 },
    windowSize: { width: 800, height: 600 },
    currentX: 100,
    currentY: 100,
    currentOpacity: 1,
    step: 60,
    conversationHistory: [],
    lastScreenshot: null,
    screenshotPath: null,
    // Window resizing state
    originalWidth: 800,
    originalHeight: 600,
    currentSizeIndex: 0,  // Index in the sizes array
    windowSizes: [
        { width: 800, height: 600 },  // 100% - Default
        { width: 480, height: 320 },  // 60% - Smaller
        { width: 360, height: 240 },  // 45% - Much smaller
        { width: 240, height: 160 },  // 30% - Very small
        { width: 150, height: 100 }   // 18.75% - SUPER STEALTH!
    ]
};

// Window visibility functions
function hideMainWindow() {
    if (!state.mainWindow?.isDestroyed()) {
        const bounds = state.mainWindow.getBounds();
        state.windowPosition = { x: bounds.x, y: bounds.y };
        state.windowSize = { width: bounds.width, height: bounds.height };
        state.mainWindow.setIgnoreMouseEvents(true, { forward: true });
        state.mainWindow.setOpacity(0);
        state.isWindowVisible = false;
        console.log('Window hidden, opacity set to 0');
    }
}

function showMainWindow() {
    if (!state.mainWindow?.isDestroyed()) {
        if (state.windowPosition && state.windowSize) {
            state.mainWindow.setBounds({
                ...state.windowPosition,
                ...state.windowSize
            });
        }
        state.mainWindow.setIgnoreMouseEvents(false);
        state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);
        state.mainWindow.setVisibleOnAllWorkspaces(true, {
            visibleOnFullScreen: true
        });
        state.mainWindow.setContentProtection(true);
        state.mainWindow.setOpacity(0); // Set opacity to 0 before showing
        state.mainWindow.showInactive(); // Use showInactive instead of show+focus
        state.mainWindow.setOpacity(state.currentOpacity); // Then set opacity to current level

        // 🚀 AUTO-FOCUS WINDOW FOR IMMEDIATE KEYBOARD CONTROL
        setTimeout(() => {
            if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                state.mainWindow.focus();
                console.log('✅ Window auto-focused for immediate keyboard control');
            }
        }, 100); // Small delay to ensure window is fully shown

        state.isWindowVisible = true;
        console.log('Window shown with showInactive(), opacity set to', state.currentOpacity, '+ AUTO-FOCUSED');
    }
}

function toggleMainWindow() {
    console.log(`Toggling window. Current state: ${state.isWindowVisible ? 'visible' : 'hidden'}`);
    if (state.isWindowVisible) {
        hideMainWindow();
    } else {
        showMainWindow();
    }
    return { success: true, isVisible: state.isWindowVisible };
}

function decreaseOpacity() {
    if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

    state.currentOpacity = state.currentOpacity || 1;
    state.currentOpacity = Math.max(0.1, state.currentOpacity - 0.1);
    state.mainWindow.setOpacity(state.currentOpacity);
    console.log('Opacity decreased to:', state.currentOpacity);
}

function increaseOpacity() {
    if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

    state.currentOpacity = state.currentOpacity || 1;
    state.currentOpacity = Math.min(1, state.currentOpacity + 0.1);
    state.mainWindow.setOpacity(state.currentOpacity);
    console.log('Opacity increased to:', state.currentOpacity);
}

function resetView() {
    if (!state.mainWindow || state.mainWindow.isDestroyed()) return;

    // Reset position to center of screen
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    const windowWidth = 800;
    const windowHeight = 600;

    state.mainWindow.setBounds({
        x: Math.floor((width - windowWidth) / 2),
        y: Math.floor((height - windowHeight) / 2),
        width: windowWidth,
        height: windowHeight
    });

    // Reset opacity to full
    state.currentOpacity = 1;
    state.mainWindow.setOpacity(1);

    // Make sure it's visible
    if (!state.isWindowVisible) {
        showMainWindow();
    }

    console.log('View reset - centered, full opacity, visible');
}

// Window movement functions
function moveWindowHorizontal(updateFn) {
    if (!state.mainWindow) return { success: false };
    state.currentX = updateFn(state.currentX);
    state.mainWindow.setPosition(
        Math.round(state.currentX),
        Math.round(state.currentY)
    );
    return { success: true };
}

function moveWindowVertical(updateFn) {
    if (!state.mainWindow) return { success: false };
    state.currentY = updateFn(state.currentY);
    state.mainWindow.setPosition(
        Math.round(state.currentX),
        Math.round(state.currentY)
    );
    return { success: true };
}

function moveWindowLeft() {
    return moveWindowHorizontal(x => x - state.step);
}

function moveWindowRight() {
    return moveWindowHorizontal(x => x + state.step);
}

function moveWindowUp() {
    return moveWindowVertical(y => y - state.step);
}

function moveWindowDown() {
    return moveWindowVertical(y => y + state.step);
}

// Window resizing functions
function shrinkWindow() {
    console.log('🔧 shrinkWindow() called');
    console.log('Window exists:', !!state.mainWindow);
    console.log('Window destroyed:', state.mainWindow ? state.mainWindow.isDestroyed() : 'N/A');

    if (!state.mainWindow || state.mainWindow.isDestroyed()) {
        console.log('❌ No window or window destroyed');
        return { success: false };
    }

    console.log('Current size index:', state.currentSizeIndex);
    console.log('Available sizes:', state.windowSizes.length);

    // Move to next smaller size
    const nextIndex = state.currentSizeIndex + 1;

    if (nextIndex >= state.windowSizes.length) {
        console.log('⚠️ Window already at minimum size');
        return { success: true, message: 'Already at minimum size' };
    }

    state.currentSizeIndex = nextIndex;
    const newSize = state.windowSizes[nextIndex];

    console.log(`New dimensions: ${newSize.width}x${newSize.height}`);

    // Get current position to maintain center
    const currentBounds = state.mainWindow.getBounds();
    console.log('Current bounds:', currentBounds);

    const centerX = currentBounds.x + currentBounds.width / 2;
    const centerY = currentBounds.y + currentBounds.height / 2;
    console.log(`Center: ${centerX}, ${centerY}`);

    // Calculate new position to keep window centered
    const newX = Math.round(centerX - newSize.width / 2);
    const newY = Math.round(centerY - newSize.height / 2);
    console.log(`New position: ${newX}, ${newY}`);

    try {
        state.mainWindow.setBounds({
            x: newX,
            y: newY,
            width: newSize.width,
            height: newSize.height
        });

        const percentage = Math.round((newSize.width / state.originalWidth) * 100);
        console.log(`✅ Window shrunk to ${percentage}% (${newSize.width}x${newSize.height})`);
        return { success: true, percentage: percentage, size: newSize };
    } catch (error) {
        console.error('❌ Error setting window bounds:', error);
        return { success: false, error: error.message };
    }
}

function resetWindowSize() {
    console.log('🔧 resetWindowSize() called');
    console.log('Window exists:', !!state.mainWindow);

    if (!state.mainWindow || state.mainWindow.isDestroyed()) {
        console.log('❌ No window or window destroyed');
        return { success: false };
    }

    console.log('Resetting size index from', state.currentSizeIndex, 'to 0');
    state.currentSizeIndex = 0;
    const originalSize = state.windowSizes[0];

    // Get current position to maintain center
    const currentBounds = state.mainWindow.getBounds();
    console.log('Current bounds:', currentBounds);

    const centerX = currentBounds.x + currentBounds.width / 2;
    const centerY = currentBounds.y + currentBounds.height / 2;
    console.log(`Center: ${centerX}, ${centerY}`);

    // Calculate new position to keep window centered
    const newX = Math.round(centerX - originalSize.width / 2);
    const newY = Math.round(centerY - originalSize.height / 2);
    console.log(`New position: ${newX}, ${newY}`);

    try {
        state.mainWindow.setBounds({
            x: newX,
            y: newY,
            width: originalSize.width,
            height: originalSize.height
        });
        console.log(`✅ Window reset to original size (${originalSize.width}x${originalSize.height})`);
        return { success: true, size: originalSize };
    } catch (error) {
        console.error('❌ Error resetting window bounds:', error);
        return { success: false, error: error.message };
    }
}

// Screenshot functionality
async function takeScreenshot() {
    try {
        console.log('Taking screenshot...');

        // Get the primary display
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;

        // Create a screenshot using desktopCapturer
        const sources = await desktopCapturer.getSources({
            types: ['screen'],
            thumbnailSize: { width, height }
        });

        if (sources.length > 0) {
            const screenshot = sources[0].thumbnail;
            const screenshotPath = path.join(__dirname, 'screenshots', `screenshot_${Date.now()}.png`);

            // Create screenshots directory if it doesn't exist
            const screenshotsDir = path.join(__dirname, 'screenshots');
            if (!fs.existsSync(screenshotsDir)) {
                fs.mkdirSync(screenshotsDir, { recursive: true });
            }

            // Save screenshot
            fs.writeFileSync(screenshotPath, screenshot.toPNG());

            state.lastScreenshot = screenshot;
            state.screenshotPath = screenshotPath;

            console.log('Screenshot saved to:', screenshotPath);

            // Notify the renderer process
            if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                state.mainWindow.webContents.send('screenshot-taken', {
                    path: screenshotPath,
                    timestamp: new Date().toISOString()
                });

                // 🚀 ENSURE WINDOW FOCUS FOR SEAMLESS WORKFLOW
                // Focus the window silently so Ctrl+Enter works immediately
                setTimeout(() => {
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.focus();
                        console.log('✅ Invisible window focused for seamless Ctrl+Enter workflow');
                    }
                }, 150); // Small delay to ensure screenshot processing is complete
            }

            return { success: true, path: screenshotPath };
        } else {
            throw new Error('No screen sources found');
        }
    } catch (error) {
        console.error('Error taking screenshot:', error);
        return { success: false, error: error.message };
    }
}

async function analyzeScreenshot() {
    try {
        if (!state.screenshotPath || !fs.existsSync(state.screenshotPath)) {
            throw new Error('No screenshot available. Please take a screenshot first using Ctrl+H');
        }

        console.log('Starting DSA problem analysis...');

        // Notify the renderer process to start analysis
        if (state.mainWindow && !state.mainWindow.isDestroyed()) {
            state.mainWindow.webContents.send('analyze-screenshot', {
                path: state.screenshotPath,
                timestamp: new Date().toISOString()
            });
        }

        return { success: true, message: 'Analysis started' };
    } catch (error) {
        console.error('Error analyzing screenshot:', error);
        return { success: false, error: error.message };
    }
}

// Add this handler for chat messages
function setupIpcHandlers() {
    // Your existing handlers for window movement, etc.
    ipcMain.handle('toggle-window', toggleMainWindow);
    ipcMain.handle('move-window-left', moveWindowLeft);
    ipcMain.handle('move-window-right', moveWindowRight);
    ipcMain.handle('move-window-up', moveWindowUp);
    ipcMain.handle('move-window-down', moveWindowDown);
    ipcMain.handle('decrease-opacity', () => { decreaseOpacity(); return { success: true }; });
    ipcMain.handle('increase-opacity', () => { increaseOpacity(); return { success: true }; });
    ipcMain.handle('reset-view', () => { resetView(); return { success: true }; });
    ipcMain.handle('quit-app', () => { app.quit(); return { success: true }; });

    // Window resizing handlers
    ipcMain.handle('shrink-window', () => { return shrinkWindow(); });
    ipcMain.handle('reset-window-size', () => { return resetWindowSize(); });

    // Screenshot handlers
    ipcMain.handle('take-screenshot', takeScreenshot);
    ipcMain.handle('analyze-screenshot', analyzeScreenshot);

    // Chat message handler
    ipcMain.handle('send-message', async (_, message) => {
        try {
            console.log("Received message:", message);

            // Add user message to history
            state.conversationHistory.push({ role: "user", content: message });

            // Keep conversation history to a reasonable size
            if (state.conversationHistory.length > 20) {
                state.conversationHistory = state.conversationHistory.slice(-20);
            }

            // The frontend will handle the API call directly
            return { success: true };
        } catch (error) {
            console.error('Error:', error);
            return { success: false, error: error.message };
        }
    });

    // Save API key handler
    ipcMain.handle('save-api-key', async (_, key, type = 'qwen') => {
        try {
            let keyPath;
            if (type === 'qwen') {
                keyPath = path.join(__dirname, 'qwen-api-key.txt');
            } else if (type === 'gemini') {
                keyPath = path.join(__dirname, 'gemini-api-key.txt');
            } else {
                keyPath = path.join(__dirname, 'api-key.txt');
            }

            fs.writeFileSync(keyPath, key, 'utf8');
            console.log(`${type.toUpperCase()} API key saved successfully to:`, keyPath);
            return { success: true };
        } catch (error) {
            console.error(`Error saving ${type} API key:`, error);
            return { success: false, error: error.message };
        }
    });

    // 💰 OPEN EXTERNAL URL HANDLER (for renewal page)
    ipcMain.handle('open-external', async (_, url) => {
        try {
            const { shell } = require('electron');
            await shell.openExternal(url);
            console.log('💰 Opened external URL:', url);
            return { success: true };
        } catch (error) {
            console.error('Error opening external URL:', error);
            return { success: false, error: error.message };
        }
    });

    // Read file as base64 handler
    ipcMain.handle('read-file-as-base64', async (_, filePath) => {
        try {
            console.log('Reading file as base64:', filePath);

            if (!fs.existsSync(filePath)) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            const fileBuffer = fs.readFileSync(filePath);
            const base64Data = fileBuffer.toString('base64');

            console.log(`Successfully read file as base64, size: ${base64Data.length} characters`);
            return { success: true, data: base64Data };
        } catch (error) {
            console.error('Error reading file as base64:', error);
            return { success: false, error: error.message };
        }
    });

    // 🔧 ENHANCED API KEY HANDLER WITH MULTIPLE PATH SUPPORT
    ipcMain.handle('get-api-key', async (_, type = 'qwen') => {
        try {
            console.log(`🔑 Getting API key for type: ${type}`);

            if (type === 'qwen') {
                // Try to get Qwen API key from environment variable
                const envApiKey = process.env.QWEN_API_KEY;
                if (envApiKey) {
                    console.log('✅ Qwen API key loaded from environment variable');
                    return { success: true, key: envApiKey.trim() };
                }

                // Try multiple file locations
                const keyPaths = [
                    path.join(__dirname, 'qwen-api-key.txt'),
                    path.join(__dirname, '..', 'qwen-api-key.txt'),
                    path.join(__dirname, 'resources', 'app', 'qwen-api-key.txt'),
                    path.join(process.resourcesPath, 'app', 'qwen-api-key.txt'),
                    path.join(process.cwd(), 'qwen-api-key.txt')
                ];

                for (const keyPath of keyPaths) {
                    if (fs.existsSync(keyPath)) {
                        const key = fs.readFileSync(keyPath, 'utf8').trim();
                        console.log(`✅ Qwen API key loaded from file: ${keyPath}, length: ${key.length}`);
                        return { success: true, key: key };
                    }
                }

                console.log('❌ No Qwen API key found in environment or files');
                return { success: true, key: '' };
            } else if (type === 'gemini') {
                // Try to get Gemini API key from environment variable
                const envApiKey = process.env.GEMINI_API_KEY;
                if (envApiKey) {
                    console.log('Gemini API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                // Fall back to file if environment variable is not set
                const keyPath = path.join(__dirname, 'gemini-api-key.txt');
                if (fs.existsSync(keyPath)) {
                    const key = fs.readFileSync(keyPath, 'utf8');
                    console.log('Gemini API key loaded from file, length:', key.length);
                    return { success: true, key: key };
                }

                console.log('No Gemini API key found in environment or file');
                return { success: true, key: '' };
            } else if (type === 'openai') {
                // OpenAI API key
                const envApiKey = process.env.OPENAI_API_KEY;
                if (envApiKey) {
                    console.log('OpenAI API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                const keyPath = path.join(__dirname, 'api-key.txt');
                if (fs.existsSync(keyPath)) {
                    const key = fs.readFileSync(keyPath, 'utf8');
                    console.log('OpenAI API key loaded from file, length:', key.length);
                    return { success: true, key: key };
                }

                console.log('No OpenAI API key found in environment or file');
                return { success: true, key: '' };
            } else if (type === 'anthropic') {
                // Anthropic API key
                const envApiKey = process.env.ANTHROPIC_API_KEY;
                if (envApiKey) {
                    console.log('Anthropic API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                const keyPath = path.join(__dirname, 'anthropic-api-key.txt');
                if (fs.existsSync(keyPath)) {
                    const key = fs.readFileSync(keyPath, 'utf8');
                    console.log('Anthropic API key loaded from file, length:', key.length);
                    return { success: true, key: key };
                }

                console.log('No Anthropic API key found in environment or file');
                return { success: true, key: '' };
            } else if (type === 'deepgram') {
                // 🎤 ENHANCED DEEPGRAM API KEY LOADING
                const envApiKey = process.env.DEEPGRAM_API_KEY;
                if (envApiKey) {
                    console.log('✅ Deepgram API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                // Try multiple file locations for Deepgram API key
                const keyPaths = [
                    path.join(__dirname, 'deepgram-api-key.txt'),
                    path.join(__dirname, '..', 'deepgram-api-key.txt'),
                    path.join(__dirname, 'resources', 'app', 'deepgram-api-key.txt'),
                    path.join(process.resourcesPath, 'app', 'deepgram-api-key.txt'),
                    path.join(process.cwd(), 'deepgram-api-key.txt')
                ];

                for (const keyPath of keyPaths) {
                    if (fs.existsSync(keyPath)) {
                        const key = fs.readFileSync(keyPath, 'utf8').trim();
                        console.log(`✅ Deepgram API key loaded from file: ${keyPath}, length: ${key.length}`);
                        return { success: true, key: key };
                    }
                }

                console.log('❌ No Deepgram API key found in environment or files');
                return { success: true, key: '' };
            } else if (type === 'groq') {
                // Groq API key
                const envApiKey = process.env.GROQ_API_KEY;
                if (envApiKey) {
                    console.log('⚡ Groq API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                console.log('❌ No Groq API key found in environment or file');
                return { success: true, key: '' };
            } else if (type === 'whisper') {
                // Whisper API key
                const envApiKey = process.env.WHISPER_API_KEY;
                if (envApiKey) {
                    console.log('🎤 Whisper API key loaded from environment variable');
                    return { success: true, key: envApiKey };
                }

                console.log('❌ No Whisper API key found in environment or file');
                return { success: true, key: '' };
            } else {
                // Default to OpenAI for backward compatibility
                const envApiKey = process.env.OPENAI_API_KEY;
                if (envApiKey) {
                    console.log('OpenAI API key loaded from environment variable (default)');
                    return { success: true, key: envApiKey };
                }

                const keyPath = path.join(__dirname, 'api-key.txt');
                if (fs.existsSync(keyPath)) {
                    const key = fs.readFileSync(keyPath, 'utf8');
                    console.log('OpenAI API key loaded from file (default), length:', key.length);
                    return { success: true, key: key };
                }

                console.log('No API key found for type:', type);
                return { success: true, key: '' };
            }
        } catch (error) {
            console.error('Error getting API key:', error);
            return { success: false, error: error.message };
        }
    });
}

// Create the invisible window
function createWindow() {
    // Create the browser window
    state.mainWindow = new BrowserWindow({
        width: 800,
        height: 600,
        minWidth: 150,
        minHeight: 100,
        x: state.currentX,
        y: 50,
        alwaysOnTop: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
            scrollBounce: true,
            webSecurity: false,
            cache: false
        },
        show: true,
        frame: false,
        transparent: true,
        fullscreenable: false,
        hasShadow: false,
        opacity: 0.0,  // Start with zero opacity (invisible)
        backgroundColor: "#00000000",
        focusable: true,
        skipTaskbar: true,
        type: "panel",
        paintWhenInitiallyHidden: true,
        titleBarStyle: "hidden",
        enableLargerThanScreen: true,
        movable: true
    });

    // 🔇 SILENT MICROPHONE PERMISSIONS (NO SPAM)
    let permissionGranted = false;

    state.mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
        if (permission === 'microphone' || permission === 'media') {
            if (!permissionGranted) {
                console.log('🎤 Microphone permission granted (one-time log)');
                permissionGranted = true;
            }
            callback(true);
        } else {
            // Only log non-media permissions
            console.log(`❌ Permission denied for: ${permission}`);
            callback(false);
        }
    });

    // Silent permission check handler
    state.mainWindow.webContents.session.setPermissionCheckHandler((webContents, permission, requestingOrigin) => {
        if (permission === 'microphone' || permission === 'media') {
            return true; // Grant silently
        }
        return false;
    });

    // 🔇 SUPPRESS ANNOYING CONSOLE ERRORS
    state.mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
        // Suppress network upload errors and other spam
        if (message.includes('OnSizeReceived failed') ||
            message.includes('chunked_data_pipe_upload_data_stream') ||
            message.includes('network::') ||
            message.includes('ERR_')) {
            return; // Don't log these annoying errors
        }
        // Allow other console messages through
    });

    // Load the index.html file
    state.mainWindow.loadFile(path.join(__dirname, 'index.html'));

    // 🔧 DEVELOPER TOOLS DISABLED FOR PRODUCTION (clean user experience)
    // state.mainWindow.webContents.openDevTools(); // Disabled for production
    console.log('🎯 Production mode - clean user experience');

    // Enhanced screen capture resistance
    state.mainWindow.setContentProtection(true);

    state.mainWindow.setVisibleOnAllWorkspaces(true, {
        visibleOnFullScreen: true
    });
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);

    // Additional screen capture resistance settings
    if (process.platform === "darwin") {
        // Prevent window from being captured in screenshots
        state.mainWindow.setHiddenInMissionControl(true);
        state.mainWindow.setWindowButtonVisibility(false);
        state.mainWindow.setBackgroundColor("#00000000");

        // Prevent window from being included in window switcher
        state.mainWindow.setSkipTaskbar(true);

        // Disable window shadow
        state.mainWindow.setHasShadow(false);
    }

    // Prevent the window from being captured by screen recording
    state.mainWindow.webContents.setBackgroundThrottling(false);
    state.mainWindow.webContents.setFrameRate(60);

    // Set up window listeners
    state.mainWindow.on("move", () => {
        const bounds = state.mainWindow.getBounds();
        state.windowPosition = { x: bounds.x, y: bounds.y };
        state.currentX = bounds.x;
        state.currentY = bounds.y;
    });

    state.mainWindow.on("resize", () => {
        const bounds = state.mainWindow.getBounds();
        state.windowSize = { width: bounds.width, height: bounds.height };
    });

    state.mainWindow.on("closed", () => {
        state.mainWindow = null;
    });

    // Initialize window state
    const bounds = state.mainWindow.getBounds();
    state.windowPosition = { x: bounds.x, y: bounds.y };
    state.windowSize = { width: bounds.width, height: bounds.height };
    state.currentX = bounds.x;
    state.currentY = bounds.y;
    state.isWindowVisible = false;

    // Set up IPC handlers
    setupIpcHandlers();
}

// App initialization
app.whenReady().then(() => {
    createWindow();

    // 🚀 BULLETPROOF GLOBAL SHORTCUTS SYSTEM
    try {
        console.log('🔧 Registering global shortcuts...');

        // 🎯 ESSENTIAL SHORTCUTS (HIGHEST PRIORITY)
        const essentialShortcuts = [
            { key: 'CommandOrControl+B', func: toggleMainWindow, name: 'Toggle Window (CRITICAL)' },
            {
                key: 'CommandOrControl+H', func: () => {
                    console.log('🔥 Global Ctrl+H - Taking screenshot');
                    takeScreenshot().then(result => {
                        if (result && result.success) {
                            console.log('✅ Global screenshot successful');
                        } else {
                            console.log('❌ Global screenshot failed:', result);
                        }
                    }).catch(error => {
                        console.error('❌ Global screenshot error:', error);
                    });
                }, name: 'Screenshot (CRITICAL)'
            },
            {
                key: 'CommandOrControl+Q', func: () => {
                    console.log('🔥 Ctrl+Q pressed - Quitting app (Global)');
                    app.quit();
                }, name: 'Quit App (CRITICAL)'
            }
        ];

        // 🎮 MODE SWITCHING SHORTCUTS
        const modeShortcuts = [
            {
                key: 'CommandOrControl+D', func: () => {
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-mode', 'dsa');
                    }
                }, name: 'DSA Mode'
            },
            {
                key: 'CommandOrControl+C', func: () => {
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-mode', 'chatbot');
                    }
                }, name: 'Chatbot Mode'
            },
            {
                key: 'CommandOrControl+N', func: () => {
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-mode', 'interview');
                    }
                }, name: 'Interview Mode'
            }
        ];

        // 🏃‍♂️ FAST PROCESSING SHORTCUTS
        const fastShortcuts = [
            {
                key: 'CommandOrControl+Return', func: () => {
                    console.log('🔥 Ctrl+Enter pressed - Global analysis');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('global-analyze-screenshots');
                    }
                }, name: 'Analyze Screenshots'
            },
            {
                key: 'CommandOrControl+R', func: () => {
                    console.log('🔥 Ctrl+R pressed - Fast coding (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('global-fast-coding');
                    }
                }, name: 'Fast Coding'
            },
            {
                key: 'CommandOrControl+Shift+S', func: () => {
                    console.log('🔥 Ctrl+Shift+S pressed - MCQ fast mode (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('global-mcq-fast');
                    }
                }, name: 'MCQ Fast Mode'
            }
        ];

        // 🎯 WINDOW CONTROL SHORTCUTS
        const windowShortcuts = [
            {
                key: 'CommandOrControl+M', func: () => {
                    console.log('🔥 Ctrl+M pressed - Shrink window (Global)');
                    const result = shrinkWindow();
                    console.log('Shrink result:', result);
                }, name: 'Shrink Window'
            },
            {
                key: 'CommandOrControl+Shift+M', func: () => {
                    console.log('🔥 Ctrl+Shift+M pressed - Reset window (Global)');
                    const result = resetWindowSize();
                    console.log('Reset result:', result);
                }, name: 'Reset Window'
            },
            {
                key: 'CommandOrControl+[', func: () => {
                    console.log('🔥 Ctrl+[ pressed - Decrease opacity (Global)');
                    decreaseOpacity();
                }, name: 'Decrease Opacity'
            },
            {
                key: 'CommandOrControl+]', func: () => {
                    console.log('🔥 Ctrl+] pressed - Increase opacity (Global)');
                    increaseOpacity();
                }, name: 'Increase Opacity'
            }
        ];

        // 🎯 WINDOW MOVEMENT SHORTCUTS
        const movementShortcuts = [
            { key: 'CommandOrControl+Left', func: moveWindowLeft, name: 'Move Left' },
            { key: 'CommandOrControl+Right', func: moveWindowRight, name: 'Move Right' },
            { key: 'CommandOrControl+Up', func: moveWindowUp, name: 'Move Up' },
            { key: 'CommandOrControl+Down', func: moveWindowDown, name: 'Move Down' }
        ];

        // 🔄 API SWITCHING SHORTCUTS
        const apiShortcuts = [
            {
                key: 'CommandOrControl+O', func: () => {
                    console.log('🔥 Ctrl+O pressed - OpenAI (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-api', 'openai');
                    }
                }, name: 'Switch to OpenAI'
            },
            {
                key: 'CommandOrControl+G', func: () => {
                    console.log('🔥 Ctrl+G pressed - Gemini (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-api', 'gemini');
                    }
                }, name: 'Switch to Gemini'
            },
            {
                key: 'CommandOrControl+W', func: () => {
                    console.log('🔥 Ctrl+W pressed - Qwen (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-api', 'qwen');
                    }
                }, name: 'Switch to Qwen'
            },
            {
                key: 'CommandOrControl+Shift+A', func: () => {
                    console.log('🔥 Ctrl+Shift+A pressed - Anthropic (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('switch-api', 'anthropic');
                    }
                }, name: 'Switch to Anthropic'
            }
        ];

        // 🎤 INTERVIEW MODE SHORTCUTS
        const interviewShortcuts = [
            {
                key: 'CommandOrControl+Shift+L', func: () => {
                    console.log('🔥 Ctrl+Shift+L pressed - Toggle listening (Global)');
                    if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                        state.mainWindow.webContents.send('toggle-interview-listening');
                    }
                }, name: 'Toggle Interview Listening'
            }
        ];

        // 🔧 REGISTER ALL SHORTCUTS WITH PRIORITY
        const allShortcuts = [
            ...essentialShortcuts,    // Highest priority
            ...modeShortcuts,
            ...fastShortcuts,
            ...windowShortcuts,
            ...movementShortcuts,
            ...apiShortcuts,
            ...interviewShortcuts
        ];

        let successCount = 0;
        let failCount = 0;

        allShortcuts.forEach(shortcut => {
            try {
                const success = globalShortcut.register(shortcut.key, shortcut.func);
                if (success) {
                    successCount++;
                } else {
                    console.log(`❌ Failed to register ${shortcut.name}: ${shortcut.key}`);
                    failCount++;
                }
            } catch (error) {
                console.error(`❌ Error registering ${shortcut.name}:`, error);
                failCount++;
            }
        });

        console.log(`🎯 Shortcuts: ${successCount} registered, ${failCount} failed`);

        // 🔧 FALLBACK SYSTEM FOR FAILED SHORTCUTS
        if (failCount > 0) {
            console.log('🔧 Some shortcuts failed - enabling fallback system');
            setTimeout(() => {
                if (state.mainWindow && !state.mainWindow.isDestroyed()) {
                    state.mainWindow.webContents.send('register-fallback-shortcuts', {
                        failedCount: failCount,
                        totalCount: allShortcuts.length
                    });
                }
            }, 1000);
        }

        // ✅ All shortcuts registered above - no duplicates needed

        app.on('activate', function () {
            if (BrowserWindow.getAllWindows().length === 0) createWindow();
        });

    } catch (error) {
        console.error('❌ Error registering global shortcuts:', error);
        console.log('🔧 Running without global shortcuts - use interface controls');
    }
});

// Handle second instance
app.on("second-instance", () => {
    if (!state.mainWindow) {
        createWindow();
    } else {
        if (state.mainWindow.isMinimized()) state.mainWindow.restore();
        state.mainWindow.focus();
    }
});

// Prevent multiple instances of the app
if (!app.requestSingleInstanceLock()) {
    app.quit();
} else {
    app.on("window-all-closed", () => {
        if (process.platform !== "darwin") {
            app.quit();
        }
    });
}

app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// Clean up shortcuts when app is quitting
app.on('will-quit', () => {
    globalShortcut.unregisterAll();
});
