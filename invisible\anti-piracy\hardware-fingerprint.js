// 🛡️ HARDWARE FINGERPRINTING MODULE
// Generates unique machine ID for anti-piracy protection

const os = require('os');
const crypto = require('crypto');
const { execSync } = require('child_process');

class HardwareFingerprint {
  constructor() {
    this.fingerprint = null;
  }

  // Generate unique hardware fingerprint
  async generateFingerprint() {
    try {
      const components = {
        // CPU Information
        cpu: this.getCPUInfo(),
        
        // Motherboard Information
        motherboard: await this.getMotherboardInfo(),
        
        // Memory Information
        memory: this.getMemoryInfo(),
        
        // Disk Information
        disk: await this.getDiskInfo(),
        
        // Network Information
        network: this.getNetworkInfo(),
        
        // System Information
        system: this.getSystemInfo()
      };

      // Create hash from all components
      const fingerprintData = JSON.stringify(components);
      this.fingerprint = crypto.createHash('sha256').update(fingerprintData).digest('hex');
      
      console.log('🔒 Hardware fingerprint generated:', this.fingerprint.substring(0, 16) + '...');
      return this.fingerprint;
    } catch (error) {
      console.error('❌ Error generating hardware fingerprint:', error);
      // Fallback fingerprint
      return this.generateFallbackFingerprint();
    }
  }

  // Get CPU information
  getCPUInfo() {
    const cpus = os.cpus();
    return {
      model: cpus[0]?.model || 'unknown',
      cores: cpus.length,
      speed: cpus[0]?.speed || 0
    };
  }

  // Get motherboard information (Windows)
  async getMotherboardInfo() {
    try {
      if (process.platform === 'win32') {
        const motherboard = execSync('wmic baseboard get serialnumber,manufacturer,product /format:csv', { encoding: 'utf8' });
        const lines = motherboard.split('\n').filter(line => line.trim() && !line.includes('Node'));
        if (lines.length > 0) {
          const parts = lines[0].split(',');
          return {
            manufacturer: parts[1] || 'unknown',
            product: parts[2] || 'unknown',
            serial: parts[3] || 'unknown'
          };
        }
      }
      return { manufacturer: 'unknown', product: 'unknown', serial: 'unknown' };
    } catch (error) {
      return { manufacturer: 'unknown', product: 'unknown', serial: 'unknown' };
    }
  }

  // Get memory information
  getMemoryInfo() {
    return {
      total: os.totalmem(),
      free: os.freemem(),
      platform: os.platform(),
      arch: os.arch()
    };
  }

  // Get disk information (Windows)
  async getDiskInfo() {
    try {
      if (process.platform === 'win32') {
        const diskInfo = execSync('wmic diskdrive get serialnumber,model,size /format:csv', { encoding: 'utf8' });
        const lines = diskInfo.split('\n').filter(line => line.trim() && !line.includes('Node'));
        if (lines.length > 0) {
          const parts = lines[0].split(',');
          return {
            model: parts[1] || 'unknown',
            serial: parts[2] || 'unknown',
            size: parts[3] || 'unknown'
          };
        }
      }
      return { model: 'unknown', serial: 'unknown', size: 'unknown' };
    } catch (error) {
      return { model: 'unknown', serial: 'unknown', size: 'unknown' };
    }
  }

  // Get network information
  getNetworkInfo() {
    const interfaces = os.networkInterfaces();
    const macs = [];
    
    for (const name in interfaces) {
      for (const iface of interfaces[name]) {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macs.push(iface.mac);
        }
      }
    }
    
    return {
      hostname: os.hostname(),
      macs: macs.sort() // Sort for consistency
    };
  }

  // Get system information
  getSystemInfo() {
    return {
      platform: os.platform(),
      release: os.release(),
      version: os.version(),
      arch: os.arch(),
      uptime: Math.floor(os.uptime() / 3600) // Hours uptime (rounded)
    };
  }

  // Generate fallback fingerprint if main method fails
  generateFallbackFingerprint() {
    const fallbackData = {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      totalmem: os.totalmem(),
      timestamp: Date.now()
    };
    
    const fallbackString = JSON.stringify(fallbackData);
    return crypto.createHash('sha256').update(fallbackString).digest('hex');
  }

  // Verify if current machine matches stored fingerprint
  async verifyFingerprint(storedFingerprint) {
    const currentFingerprint = await this.generateFingerprint();
    return currentFingerprint === storedFingerprint;
  }

  // Get current fingerprint
  getCurrentFingerprint() {
    return this.fingerprint;
  }
}

module.exports = HardwareFingerprint;
