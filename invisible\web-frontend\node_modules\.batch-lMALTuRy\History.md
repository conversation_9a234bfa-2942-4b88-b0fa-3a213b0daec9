0.6.1 / 2017-05-16
==================

  * fix `process.nextTick` detection in Node.js

0.6.0 / 2017-03-25
==================

  * always invoke end callback asynchronously
  * fix compatibility with component v1
  * fix license field

0.5.3 / 2015-10-01
==================

  * fix for browserify

0.5.2 / 2014-12-22
==================

  * add brower field
  * add license to package.json

0.5.1 / 2014-06-19
==================

 * add repository field to readme (exciting)

0.5.0 / 2013-07-29
==================

 * add `.throws(true)` to opt-in to responding with an array of error objects
 * make `new` optional

0.4.0 / 2013-06-05
==================

 * add catching of immediate callback errors

0.3.2 / 2013-03-15
==================

  * remove Emitter call in constructor

0.3.1 / 2013-03-13
==================

  * add Emitter() mixin for client. Closes #8

0.3.0 / 2013-03-13
==================

  * add component.json
  * add result example
  * add .concurrency support
  * add concurrency example
  * add parallel example

0.2.1 / 2012-11-08
==================

  * add .start, .end, and .duration properties
  * change dependencies to devDependencies

0.2.0 / 2012-10-04
==================

  * add progress events. Closes #5 (__BREAKING CHANGE__)

0.1.1 / 2012-07-03
==================

  * change "complete" event to "progress"

0.1.0 / 2012-07-03
==================

  * add Emitter inheritance and emit "complete" [burcu]

0.0.3 / 2012-06-02
==================

  * Callback results should be in the order of the queued functions.

0.0.2 / 2012-02-12
==================

  * any node

0.0.1 / 2010-01-03
==================

  * Initial release
