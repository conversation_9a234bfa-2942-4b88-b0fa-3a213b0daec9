'use strict';Object.defineProperty(exports, "__esModule", { value: true });exports.




















walkSync = walkSync;var _path = require('path');var _path2 = _interopRequireDefault(_path);var _fs = require('fs');function _interopRequireDefault(obj) {return obj && obj.__esModule ? obj : { 'default': obj };} /** @typedef {{ name: string, path: string, dirent: import('fs').Dirent }} Entry */ /**
                                                                                                                                                                                                                                                                                                        * Do a comprehensive walk of the provided src directory, and collect all entries.  Filter out
                                                                                                                                                                                                                                                                                                        * any directories or entries using the optional filter functions.
                                                                                                                                                                                                                                                                                                        * @param {string} root - path to the root of the folder we're walking
                                                                                                                                                                                                                                                                                                        * @param {{ deepFilter?: (entry: Entry) => boolean, entryFilter?: (entry: Entry) => boolean }} options
                                                                                                                                                                                                                                                                                                        * @param {Entry} currentEntry - entry for the current directory we're working in
                                                                                                                                                                                                                                                                                                        * @param {Entry[]} existingEntries - list of all entries so far
                                                                                                                                                                                                                                                                                                        * @returns {Entry[]} an array of directory entries
                                                                                                                                                                                                                                                                                                        */ /**
                                                                                                                                                                                                                                                                                                            * This is intended to provide similar capability as the sync api from @nodelib/fs.walk, until `eslint-plugin-import`
                                                                                                                                                                                                                                                                                                            * is willing to modernize and update their minimum node version to at least v16.  I intentionally made the
                                                                                                                                                                                                                                                                                                            * shape of the API (for the part we're using) the same as @nodelib/fs.walk so that that can be swapped in
                                                                                                                                                                                                                                                                                                            * when the repo is ready for it.
                                                                                                                                                                                                                                                                                                            */function walkSync(root, options, currentEntry, existingEntries) {// Extract the filter functions. Default to evaluating true, if no filter passed in.
  var _options$deepFilter = options.deepFilter,deepFilter = _options$deepFilter === undefined ? function () {return true;} : _options$deepFilter,_options$entryFilter = options.entryFilter,entryFilter = _options$entryFilter === undefined ? function () {return true;} : _options$entryFilter;var entryList = existingEntries || [];var currentRelativePath = currentEntry ? currentEntry.path : '.';var fullPath = currentEntry ? _path2['default'].join(root, currentEntry.path) : root;var dirents = (0, _fs.readdirSync)(fullPath, { withFileTypes: true });dirents.forEach(function (dirent) {/** @type {Entry} */var entry = { name: dirent.name, path: _path2['default'].join(currentRelativePath, dirent.name), dirent: dirent };


    if (dirent.isDirectory() && deepFilter(entry)) {
      entryList.push(entry);
      entryList = walkSync(root, options, entry, entryList);
    } else if (dirent.isFile() && entryFilter(entry)) {
      entryList.push(entry);
    }
  });

  return entryList;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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