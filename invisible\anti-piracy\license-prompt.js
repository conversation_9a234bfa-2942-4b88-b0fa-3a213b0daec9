// 🔐 LICENSE PROMPT MODULE
// Handles license key input during installation

const { dialog, BrowserWindow } = require('electron');
const path = require('path');

class LicensePrompt {
  constructor() {
    this.promptWindow = null;
    this.licenseKey = null;
    this.isResolved = false;
  }

  // Show license prompt dialog
  async showLicensePrompt() {
    return new Promise((resolve, reject) => {
      try {
        this.createPromptWindow(resolve, reject);
      } catch (error) {
        console.error('❌ Error showing license prompt:', error);
        reject(error);
      }
    });
  }

  // Create license prompt window
  createPromptWindow(resolve, reject) {
    this.promptWindow = new BrowserWindow({
      width: 500,
      height: 400,
      modal: true,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      center: true,
      title: 'License Key Required',
      icon: path.join(__dirname, '../assets/icon.ico'),
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });

    // Load license prompt HTML
    this.promptWindow.loadFile(path.join(__dirname, 'license-prompt.html'));

    // Handle window closed
    this.promptWindow.on('closed', () => {
      if (!this.isResolved) {
        reject(new Error('License prompt was closed'));
      }
      this.promptWindow = null;
    });

    // Handle license submission
    this.promptWindow.webContents.on('ipc-message', (event, channel, data) => {
      if (channel === 'license-submitted') {
        this.handleLicenseSubmission(data.licenseKey, resolve, reject);
      } else if (channel === 'license-cancelled') {
        this.handleLicenseCancellation(reject);
      }
    });

    // Show window
    this.promptWindow.show();
    this.promptWindow.focus();
  }

  // Handle license key submission
  async handleLicenseSubmission(licenseKey, resolve, reject) {
    try {
      if (!licenseKey || licenseKey.trim().length < 10) {
        this.showError('Please enter a valid license key (minimum 10 characters)');
        return;
      }

      // Validate license format
      if (!this.validateLicenseFormat(licenseKey.trim())) {
        this.showError('Invalid license key format. Please check your license key and try again.');
        return;
      }

      this.licenseKey = licenseKey.trim();
      this.isResolved = true;
      
      // Close window
      if (this.promptWindow) {
        this.promptWindow.close();
      }

      resolve(this.licenseKey);
    } catch (error) {
      console.error('❌ Error handling license submission:', error);
      this.showError('An error occurred while processing your license key.');
    }
  }

  // Handle license cancellation
  handleLicenseCancellation(reject) {
    this.isResolved = true;
    
    if (this.promptWindow) {
      this.promptWindow.close();
    }

    reject(new Error('License entry was cancelled by user'));
  }

  // Validate license key format
  validateLicenseFormat(licenseKey) {
    // Basic validation - should be alphanumeric with dashes
    const licensePattern = /^[A-Za-z0-9\-]+$/;
    return licensePattern.test(licenseKey) && licenseKey.length >= 10;
  }

  // Show error message in prompt window
  showError(message) {
    if (this.promptWindow && this.promptWindow.webContents) {
      this.promptWindow.webContents.send('show-error', message);
    }
  }

  // Show simple dialog prompt (fallback)
  async showSimplePrompt() {
    try {
      const result = await dialog.showMessageBox({
        type: 'question',
        title: 'License Key Required',
        message: 'Please enter your license key:',
        detail: 'You need a valid license key to use this software.',
        buttons: ['Cancel', 'Enter License Key'],
        defaultId: 1,
        cancelId: 0
      });

      if (result.response === 1) {
        // Show input dialog (this is a simplified version)
        return await this.showInputDialog();
      } else {
        throw new Error('License entry was cancelled');
      }
    } catch (error) {
      console.error('❌ Error showing simple prompt:', error);
      throw error;
    }
  }

  // Show input dialog (simplified)
  async showInputDialog() {
    return new Promise((resolve, reject) => {
      // This is a basic implementation
      // In a real scenario, you'd want a proper input dialog
      const licenseKey = require('electron').dialog.showInputBox({
        title: 'Enter License Key',
        label: 'License Key:',
        value: ''
      });

      if (licenseKey && this.validateLicenseFormat(licenseKey)) {
        resolve(licenseKey);
      } else {
        reject(new Error('Invalid or empty license key'));
      }
    });
  }

  // Get entered license key
  getLicenseKey() {
    return this.licenseKey;
  }

  // Close prompt window
  close() {
    if (this.promptWindow) {
      this.promptWindow.close();
      this.promptWindow = null;
    }
  }
}

module.exports = LicensePrompt;
