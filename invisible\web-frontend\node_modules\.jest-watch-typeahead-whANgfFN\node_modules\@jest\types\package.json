{"name": "@jest/types", "version": "28.1.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-types"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/schemas": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "devDependencies": {"@tsd/typescript": "~4.7.4", "tsd-lite": "^0.5.6"}, "publishConfig": {"access": "public"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1"}