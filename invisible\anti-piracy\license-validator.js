// 🔐 LICENSE VALIDATION MODULE
// Validates license keys and manages hardware binding

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const HardwareFingerprint = require('./hardware-fingerprint');

class LicenseValidator {
  constructor() {
    this.licenseFile = path.join(process.cwd(), 'license.key');
    this.bindingFile = path.join(process.cwd(), 'hardware.bind');
    this.hardwareFingerprint = new HardwareFingerprint();
    this.isValidated = false;
  }

  // Validate license key format
  validateLicenseFormat(licenseKey) {
    if (!licenseKey || typeof licenseKey !== 'string') {
      return false;
    }

    // License key should be at least 32 characters
    if (licenseKey.length < 32) {
      return false;
    }

    // Should contain alphanumeric characters and dashes
    const licensePattern = /^[A-Za-z0-9\-]+$/;
    return licensePattern.test(licenseKey);
  }

  // Save license key to file
  saveLicenseKey(licenseKey) {
    try {
      fs.writeFileSync(this.licenseFile, licenseKey, 'utf8');
      console.log('✅ License key saved successfully');
      return true;
    } catch (error) {
      console.error('❌ Error saving license key:', error);
      return false;
    }
  }

  // Load license key from file
  loadLicenseKey() {
    try {
      if (fs.existsSync(this.licenseFile)) {
        const licenseKey = fs.readFileSync(this.licenseFile, 'utf8').trim();
        return licenseKey;
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading license key:', error);
      return null;
    }
  }

  // Bind license to hardware
  async bindToHardware(licenseKey) {
    try {
      const fingerprint = await this.hardwareFingerprint.generateFingerprint();
      
      const bindingData = {
        licenseKey: licenseKey,
        hardwareFingerprint: fingerprint,
        bindingDate: new Date().toISOString(),
        machineInfo: {
          hostname: require('os').hostname(),
          platform: require('os').platform(),
          arch: require('os').arch()
        }
      };

      // Encrypt binding data
      const encryptedBinding = this.encryptBindingData(JSON.stringify(bindingData));
      fs.writeFileSync(this.bindingFile, encryptedBinding);
      
      console.log('🔒 License bound to hardware successfully');
      return true;
    } catch (error) {
      console.error('❌ Error binding license to hardware:', error);
      return false;
    }
  }

  // Verify hardware binding
  async verifyHardwareBinding() {
    try {
      if (!fs.existsSync(this.bindingFile)) {
        console.log('⚠️ No hardware binding found');
        return false;
      }

      const encryptedBinding = fs.readFileSync(this.bindingFile, 'utf8');
      const bindingData = JSON.parse(this.decryptBindingData(encryptedBinding));
      
      const currentFingerprint = await this.hardwareFingerprint.generateFingerprint();
      
      if (bindingData.hardwareFingerprint === currentFingerprint) {
        console.log('✅ Hardware binding verified');
        return true;
      } else {
        console.log('❌ Hardware binding verification failed - machine mismatch');
        return false;
      }
    } catch (error) {
      console.error('❌ Error verifying hardware binding:', error);
      return false;
    }
  }

  // Encrypt binding data
  encryptBindingData(data) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync('invisible-assessment-tool-2024', 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return iv.toString('hex') + ':' + encrypted;
  }

  // Decrypt binding data
  decryptBindingData(encryptedData) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync('invisible-assessment-tool-2024', 'salt', 32);
    
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    
    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  // Complete license validation process
  async validateLicense(licenseKey = null) {
    try {
      // Use provided license key or load from file
      const keyToValidate = licenseKey || this.loadLicenseKey();
      
      if (!keyToValidate) {
        console.log('❌ No license key found');
        return { valid: false, reason: 'NO_LICENSE_KEY' };
      }

      // Validate license format
      if (!this.validateLicenseFormat(keyToValidate)) {
        console.log('❌ Invalid license key format');
        return { valid: false, reason: 'INVALID_FORMAT' };
      }

      // Verify hardware binding
      const hardwareValid = await this.verifyHardwareBinding();
      if (!hardwareValid) {
        console.log('❌ Hardware binding verification failed');
        return { valid: false, reason: 'HARDWARE_MISMATCH' };
      }

      console.log('✅ License validation successful');
      this.isValidated = true;
      return { valid: true, licenseKey: keyToValidate };
    } catch (error) {
      console.error('❌ License validation error:', error);
      return { valid: false, reason: 'VALIDATION_ERROR' };
    }
  }

  // Initialize license (for first-time setup)
  async initializeLicense(licenseKey) {
    try {
      // Validate format
      if (!this.validateLicenseFormat(licenseKey)) {
        return { success: false, reason: 'INVALID_FORMAT' };
      }

      // Save license key
      if (!this.saveLicenseKey(licenseKey)) {
        return { success: false, reason: 'SAVE_FAILED' };
      }

      // Bind to hardware
      if (!await this.bindToHardware(licenseKey)) {
        return { success: false, reason: 'BINDING_FAILED' };
      }

      console.log('🎉 License initialized successfully');
      this.isValidated = true;
      return { success: true };
    } catch (error) {
      console.error('❌ License initialization error:', error);
      return { success: false, reason: 'INITIALIZATION_ERROR' };
    }
  }

  // Check if license is validated
  isLicenseValid() {
    return this.isValidated;
  }

  // Get license info
  getLicenseInfo() {
    try {
      const licenseKey = this.loadLicenseKey();
      if (!licenseKey) return null;

      if (fs.existsSync(this.bindingFile)) {
        const encryptedBinding = fs.readFileSync(this.bindingFile, 'utf8');
        const bindingData = JSON.parse(this.decryptBindingData(encryptedBinding));
        
        return {
          licenseKey: licenseKey,
          bindingDate: bindingData.bindingDate,
          machineInfo: bindingData.machineInfo
        };
      }
      
      return { licenseKey: licenseKey };
    } catch (error) {
      console.error('❌ Error getting license info:', error);
      return null;
    }
  }
}

module.exports = LicenseValidator;
