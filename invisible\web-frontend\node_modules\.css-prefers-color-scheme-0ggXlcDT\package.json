{"name": "css-prefers-color-scheme", "version": "6.0.3", "description": "Use light and dark color schemes in all browsers", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/css-prefers-color-scheme#readme", "bugs": "https://github.com/csstools/postcss-plugins/issues", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}, "./browser": {"import": "./dist/browser.mjs", "require": "./dist/browser.cjs", "default": "./dist/browser.mjs"}, "./browser-global": {"default": "./dist/browser-global.js"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist", "browser.js", "browser.min.js"], "bin": {"css-prefers-color-scheme": "dist/cli.cjs"}, "scripts": {"build": "rollup -c ../../rollup/default.js && npm run copy-browser-scripts-to-old-location", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "copy-browser-scripts-to-old-location": "node -e \"fs.copyFileSync('./dist/browser-global.js', './browser.js'); fs.copyFileSync('./dist/browser-global.js', './browser.min.js')\"", "lint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "prepublishOnly": "npm run clean && npm run build && npm run test", "stryker": "stryker run --logLevel error", "test": "postcss-tape --ci && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "devDependencies": {"postcss": "^8.3.6", "postcss-tape": "^6.0.1"}, "peerDependencies": {"postcss": "^8.4"}, "keywords": ["postcss", "css", "postcss-plugin", "media", "query", "prefers", "color", "scheme", "dark", "light", "no-preference", "mode", "queries", "interface"], "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/css-prefers-color-scheme"}, "volta": {"extends": "../../package.json"}}