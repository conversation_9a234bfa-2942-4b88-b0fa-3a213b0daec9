<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Key Required</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 450px;
            text-align: center;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #4CAF50;
        }

        h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            color: #4CAF50;
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4CAF50;
        }

        input[type="text"] {
            width: 100%;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            font-family: 'Courier New', monospace;
            text-align: center;
            letter-spacing: 1px;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        input[type="text"]::placeholder {
            color: rgba(255, 255, 255, 0.5);
            letter-spacing: normal;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        button {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .error-message {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #ff6b6b;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .help-text {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 10px;
            line-height: 1.4;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔐</div>
        <h1>License Key Required</h1>
        <p class="subtitle">
            Please enter your license key to activate the Invisible Assessment Tool.
            You should have received this key after your purchase.
        </p>

        <div class="error-message" id="errorMessage"></div>

        <form id="licenseForm">
            <div class="form-group">
                <label for="licenseKey">License Key:</label>
                <input 
                    type="text" 
                    id="licenseKey" 
                    name="licenseKey" 
                    placeholder="Enter your license key here..."
                    autocomplete="off"
                    spellcheck="false"
                    required
                >
                <div class="help-text">
                    The license key should be at least 10 characters long and contain letters, numbers, and dashes.
                </div>
            </div>

            <div class="button-group">
                <button type="button" class="btn-secondary" id="cancelBtn">Cancel</button>
                <button type="submit" class="btn-primary" id="submitBtn">Activate License</button>
            </div>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="margin-top: 10px;">Validating license...</p>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        const form = document.getElementById('licenseForm');
        const licenseKeyInput = document.getElementById('licenseKey');
        const errorMessage = document.getElementById('errorMessage');
        const submitBtn = document.getElementById('submitBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const loading = document.getElementById('loading');

        // Focus on license key input
        licenseKeyInput.focus();

        // Handle form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const licenseKey = licenseKeyInput.value.trim();
            
            if (!licenseKey) {
                showError('Please enter a license key');
                return;
            }

            if (licenseKey.length < 10) {
                showError('License key must be at least 10 characters long');
                return;
            }

            // Show loading
            showLoading(true);
            
            // Send license key to main process
            ipcRenderer.send('license-submitted', { licenseKey });
        });

        // Handle cancel button
        cancelBtn.addEventListener('click', () => {
            ipcRenderer.send('license-cancelled');
        });

        // Handle Enter key
        licenseKeyInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                form.dispatchEvent(new Event('submit'));
            }
        });

        // Handle Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                cancelBtn.click();
            }
        });

        // Listen for error messages from main process
        ipcRenderer.on('show-error', (event, message) => {
            showError(message);
            showLoading(false);
        });

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            licenseKeyInput.focus();
        }

        // Hide error message
        function hideError() {
            errorMessage.style.display = 'none';
        }

        // Show/hide loading state
        function showLoading(show) {
            if (show) {
                form.style.display = 'none';
                loading.style.display = 'block';
            } else {
                form.style.display = 'block';
                loading.style.display = 'none';
            }
        }

        // Clear error when typing
        licenseKeyInput.addEventListener('input', () => {
            hideError();
        });

        // Auto-format license key (optional)
        licenseKeyInput.addEventListener('input', (e) => {
            let value = e.target.value.toUpperCase();
            // Remove any characters that aren't alphanumeric or dashes
            value = value.replace(/[^A-Z0-9\-]/g, '');
            e.target.value = value;
        });
    </script>
</body>
</html>
