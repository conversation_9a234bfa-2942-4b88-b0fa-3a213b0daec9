{"version": 3, "file": "mdns-server.js", "sourceRoot": "", "sources": ["../../src/lib/mdns-server.ts"], "names": [], "mappings": ";;;;;;AACA,kEAAgE;AAEhE,8DAAsE;AACtE,kEAAoE;AAEpE,MAAa,MAAM;IAMf,YAAY,IAA4B,EAAE,aAAoC;QAHtE,aAAQ,GAAqB,EAAE,CAAA;QAInC,IAAI,CAAC,IAAI,GAAG,IAAA,uBAAY,EAAC,IAAI,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACrD,IAAI,CAAC,aAAa,GAAG,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,UAAS,GAAQ,IAAI,MAAM,GAAG,CAAA,CAAC,CAAC,CAAA;IAC1E,CAAC;IAEM,QAAQ,CAAC,OAA6C;QAEzD,MAAM,cAAc,GAAG,CAAC,MAAqB,EAAE,EAAE;YAC7C,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;YACjD,CAAC;iBAAM,IAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACzD,OAAM;YACV,CAAC;YACD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC5B,CAAC,CAAA;QAED,IAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAExB,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;QACnC,CAAC;aAAM,CAAC;YAEJ,cAAc,CAAC,OAAwB,CAAC,CAAA;QAC5C,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,OAA6C;QAE3D,MAAM,gBAAgB,GAAG,CAAC,MAAqB,EAAE,EAAE;YAC/C,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;YACtB,IAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1B,OAAM;YACV,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA;QAClG,CAAC,CAAA;QAED,IAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAExB,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;QACrC,CAAC;aAAM,CAAC;YAEJ,gBAAgB,CAAC,OAAwB,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,KAAe;QAClC,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAa,EAAE,EAAE;YACtC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;YACxB,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;YAGxB,IAAI,OAAO,GAAG,IAAI,KAAK,KAAK;gBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC1E,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAE/B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAM;YAGhC,IAAI,WAAW,GAAe,EAAE,CAAA;YAChC,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;gBACnB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBAC9B,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK;wBAAE,OAAM;oBACjC,WAAW,GAAG,WAAW;yBACtB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;yBAC3C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;gBAChD,CAAC,CAAC,CAAA;gBAIF,WAAW;qBACR,MAAM,CAAC,UAAU,MAAM;oBACtB,OAAO,MAAM,CAAC,IAAI,KAAK,KAAK,CAAA;gBAC9B,CAAC,CAAC;qBACD,GAAG,CAAC,UAAU,MAAM;oBACnB,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAA;gBAC3B,CAAC,CAAC;qBACD,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;qBACrB,OAAO,CAAC,UAAU,MAAM;oBACvB,WAAW,GAAG,WAAW;yBACtB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;yBACpC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;gBAC5C,CAAC,CAAC,CAAA;YACN,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,EAAE,CAAC,GAAQ,EAAE,EAAE;gBAC7E,IAAI,GAAG,EAAE,CAAC;oBACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,UAAU,CAAC,IAAY,EAAE,IAAY;QACzC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAA;QACb,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,MAAqB,EAAE,EAAE;YAC1D,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACxE,OAAO,IAAA,mBAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,iBAAiB,CAAE,CAAgB;QACvC,OAAO,CAAC,CAAgB,EAAE,EAAE;YACxB,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;gBACpB,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;gBACjB,IAAA,aAAS,EAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC,CAAA;IACL,CAAC;IAEO,MAAM;QACV,IAAI,GAAG,GAAe,EAAE,CAAA;QACxB,OAAO,CAAC,GAAQ,EAAE,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;gBAAE,OAAO,KAAK,CAAA;YACnC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,IAAI,CAAA;QACf,CAAC,CAAA;IACL,CAAC;CAEJ;AAjID,wBAiIC;AAED,kBAAe,MAAM,CAAA"}