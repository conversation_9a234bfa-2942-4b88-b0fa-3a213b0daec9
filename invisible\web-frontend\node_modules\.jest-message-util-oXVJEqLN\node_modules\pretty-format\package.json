{"name": "pretty-format", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/pretty-format"}, "license": "MIT", "description": "Stringify any JavaScript value.", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "devDependencies": {"@types/react": "^17.0.3", "@types/react-is": "^18.0.0", "@types/react-test-renderer": "17.0.2", "immutable": "^4.0.0", "jest-util": "^29.7.0", "react": "17.0.2", "react-dom": "^17.0.1", "react-test-renderer": "17.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}