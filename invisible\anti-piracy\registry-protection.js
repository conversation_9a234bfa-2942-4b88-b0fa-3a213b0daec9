// 🛡️ REGISTRY PROTECTION MODULE
// Stores anti-piracy data in Windows registry

const { execSync } = require('child_process');
const crypto = require('crypto');

class RegistryProtection {
  constructor() {
    this.registryPath = 'HKEY_CURRENT_USER\\Software\\InvisibleAssessmentTool';
    this.encryptionKey = 'invisible-tool-2024-registry-protection';
  }

  // Encrypt data before storing in registry
  encryptData(data) {
    try {
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      console.error('❌ Error encrypting data:', error);
      return null;
    }
  }

  // Decrypt data from registry
  decryptData(encryptedData) {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('❌ Error decrypting data:', error);
      return null;
    }
  }

  // Create registry key if it doesn't exist
  createRegistryKey() {
    try {
      if (process.platform !== 'win32') {
        console.log('⚠️ Registry protection only available on Windows');
        return false;
      }

      execSync(`reg add "${this.registryPath}" /f`, { stdio: 'ignore' });
      console.log('✅ Registry key created');
      return true;
    } catch (error) {
      console.error('❌ Error creating registry key:', error);
      return false;
    }
  }

  // Store license data in registry
  storeLicenseData(licenseKey, hardwareFingerprint) {
    try {
      if (process.platform !== 'win32') {
        console.log('⚠️ Registry protection only available on Windows');
        return false;
      }

      const licenseData = {
        licenseKey: licenseKey,
        hardwareFingerprint: hardwareFingerprint,
        installDate: new Date().toISOString(),
        lastValidation: new Date().toISOString(),
        validationCount: 1
      };

      const encryptedData = this.encryptData(licenseData);
      if (!encryptedData) return false;

      // Create registry key first
      this.createRegistryKey();

      // Store encrypted license data
      execSync(`reg add "${this.registryPath}" /v "LicenseData" /t REG_SZ /d "${encryptedData}" /f`, { stdio: 'ignore' });
      
      // Store installation timestamp
      const timestamp = Date.now().toString();
      execSync(`reg add "${this.registryPath}" /v "InstallTime" /t REG_SZ /d "${timestamp}" /f`, { stdio: 'ignore' });

      // Store validation hash
      const validationHash = crypto.createHash('sha256').update(licenseKey + hardwareFingerprint).digest('hex');
      execSync(`reg add "${this.registryPath}" /v "ValidationHash" /t REG_SZ /d "${validationHash}" /f`, { stdio: 'ignore' });

      console.log('🔒 License data stored in registry');
      return true;
    } catch (error) {
      console.error('❌ Error storing license data in registry:', error);
      return false;
    }
  }

  // Retrieve license data from registry
  retrieveLicenseData() {
    try {
      if (process.platform !== 'win32') {
        console.log('⚠️ Registry protection only available on Windows');
        return null;
      }

      const result = execSync(`reg query "${this.registryPath}" /v "LicenseData"`, { encoding: 'utf8' });
      const lines = result.split('\n');
      
      for (const line of lines) {
        if (line.includes('LicenseData')) {
          const parts = line.trim().split(/\s+/);
          const encryptedData = parts[parts.length - 1];
          return this.decryptData(encryptedData);
        }
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error retrieving license data from registry:', error);
      return null;
    }
  }

  // Update validation count
  updateValidationCount() {
    try {
      const licenseData = this.retrieveLicenseData();
      if (!licenseData) return false;

      licenseData.lastValidation = new Date().toISOString();
      licenseData.validationCount = (licenseData.validationCount || 0) + 1;

      const encryptedData = this.encryptData(licenseData);
      if (!encryptedData) return false;

      execSync(`reg add "${this.registryPath}" /v "LicenseData" /t REG_SZ /d "${encryptedData}" /f`, { stdio: 'ignore' });
      
      console.log(`🔄 Validation count updated: ${licenseData.validationCount}`);
      return true;
    } catch (error) {
      console.error('❌ Error updating validation count:', error);
      return false;
    }
  }

  // Verify registry integrity
  verifyRegistryIntegrity(licenseKey, hardwareFingerprint) {
    try {
      if (process.platform !== 'win32') {
        console.log('⚠️ Registry protection only available on Windows');
        return false;
      }

      // Check if registry key exists
      const result = execSync(`reg query "${this.registryPath}" /v "ValidationHash"`, { encoding: 'utf8' });
      const lines = result.split('\n');
      
      for (const line of lines) {
        if (line.includes('ValidationHash')) {
          const parts = line.trim().split(/\s+/);
          const storedHash = parts[parts.length - 1];
          
          const expectedHash = crypto.createHash('sha256').update(licenseKey + hardwareFingerprint).digest('hex');
          
          if (storedHash === expectedHash) {
            console.log('✅ Registry integrity verified');
            return true;
          } else {
            console.log('❌ Registry integrity check failed');
            return false;
          }
        }
      }
      
      return false;
    } catch (error) {
      console.error('❌ Error verifying registry integrity:', error);
      return false;
    }
  }

  // Check if license is registered
  isLicenseRegistered() {
    try {
      if (process.platform !== 'win32') {
        return false;
      }

      execSync(`reg query "${this.registryPath}" /v "LicenseData"`, { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get installation time
  getInstallationTime() {
    try {
      if (process.platform !== 'win32') {
        return null;
      }

      const result = execSync(`reg query "${this.registryPath}" /v "InstallTime"`, { encoding: 'utf8' });
      const lines = result.split('\n');
      
      for (const line of lines) {
        if (line.includes('InstallTime')) {
          const parts = line.trim().split(/\s+/);
          const timestamp = parts[parts.length - 1];
          return new Date(parseInt(timestamp));
        }
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting installation time:', error);
      return null;
    }
  }

  // Clean registry data (for uninstall)
  cleanRegistryData() {
    try {
      if (process.platform !== 'win32') {
        return true;
      }

      execSync(`reg delete "${this.registryPath}" /f`, { stdio: 'ignore' });
      console.log('🧹 Registry data cleaned');
      return true;
    } catch (error) {
      console.error('❌ Error cleaning registry data:', error);
      return false;
    }
  }

  // Store anti-tampering data
  storeAntiTamperingData() {
    try {
      if (process.platform !== 'win32') {
        return false;
      }

      const tamperingData = {
        checksum: crypto.createHash('sha256').update(process.execPath).digest('hex'),
        installPath: process.cwd(),
        processName: process.argv[0],
        timestamp: Date.now()
      };

      const encryptedData = this.encryptData(tamperingData);
      if (!encryptedData) return false;

      execSync(`reg add "${this.registryPath}" /v "AntiTampering" /t REG_SZ /d "${encryptedData}" /f`, { stdio: 'ignore' });
      
      console.log('🛡️ Anti-tampering data stored');
      return true;
    } catch (error) {
      console.error('❌ Error storing anti-tampering data:', error);
      return false;
    }
  }
}

module.exports = RegistryProtection;
