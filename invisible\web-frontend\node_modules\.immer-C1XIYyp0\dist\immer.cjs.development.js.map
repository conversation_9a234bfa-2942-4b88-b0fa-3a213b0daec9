{"version": 3, "file": "immer.cjs.development.js", "sources": ["../src/utils/env.ts", "../src/utils/errors.ts", "../src/utils/common.ts", "../src/utils/plugins.ts", "../src/core/scope.ts", "../src/core/finalize.ts", "../src/core/proxy.ts", "../src/core/immerClass.ts", "../src/core/current.ts", "../src/plugins/es5.ts", "../src/plugins/patches.ts", "../src/plugins/mapset.ts", "../src/plugins/all.ts", "../src/immer.ts"], "sourcesContent": ["// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n"], "names": ["hasSymbol", "Symbol", "hasMap", "Map", "hasSet", "Set", "hasProxies", "Proxy", "revocable", "Reflect", "NOTHING", "for", "DRAFTABLE", "DRAFT_STATE", "iteratorSymbol", "iterator", "errors", "data", "path", "op", "plugin", "thing", "die", "error", "args", "e", "msg", "apply", "Error", "isDraft", "value", "isDraftable", "isPlainObject", "Array", "isArray", "constructor", "isMap", "isSet", "objectCtorString", "Object", "prototype", "toString", "proto", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "Function", "original", "base_", "ownKeys", "getOwnPropertySymbols", "obj", "getOwnPropertyNames", "concat", "getOwnPropertyDescriptors", "target", "res", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "each", "iter", "enumerableOnly", "getArchtype", "keys", "entry", "index", "state", "type_", "has", "prop", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "latest", "copy_", "shallowCopy", "base", "slice", "descriptors", "i", "length", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "plugins", "getPlugin", "pluginKey", "loadPlugin", "implementation", "currentScope", "getCurrentScope", "createScope", "parent_", "immer_", "drafts_", "canAutoFreeze_", "unfinalizedDrafts_", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "revokeDraft", "enterScope", "immer", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "undefined", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "rootScope", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "assigned_", "autoFreeze_", "createProxyProxy", "parent", "isManual_", "traps", "objectTraps", "arrayTraps", "revoke", "proxy", "source", "readPropFromProto", "peek", "prepareCopy", "createProxy", "getDescriptorFromProto", "current", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "owner", "defineProperty", "setPrototypeOf", "fn", "arguments", "parseInt", "Immer", "config", "recipe", "defaultBase", "self", "curriedProduce", "produce", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "then", "p", "ip", "produceWithPatches", "patches", "inversePatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "patch", "applyPatchesImpl", "applyPatches_", "proxyMap_", "proxySet_", "createES5Proxy_", "push", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "mark<PERSON>hangesRecursively", "mark<PERSON><PERSON>esSweep", "createES5Draft", "proxyProperty", "assertUnrevoked", "drafts", "hasArrayChanges", "hasObjectChanges", "object", "min", "Math", "baseValue", "baseIsDraft", "descriptor", "JSON", "stringify", "enablePatches", "REPLACE", "ADD", "REMOVE", "basePath", "generatePatchesFromAssigned", "generateArrayPatches", "generateSetPatches", "clonePatchValueIfNeeded", "assignedValue", "origValue", "unshift", "replacement", "parentType", "join", "type", "deepClonePatchValue", "splice", "map", "entries", "k", "v", "cloned", "immerable", "enableMapSet", "extendStatics", "d", "b", "__proto__", "__extends", "__", "DraftMap", "_super", "size", "prepareMapCopy", "cb", "thisArg", "_value", "_map", "values", "next", "r", "done", "DraftSet", "prepareSetCopy", "enableAllPlugins", "bind", "castDraft", "castImmutable"], "mappings": ";;;;;;AAAA;AAEA;AAEA;AACA,IAAMA,SAAS,GACd,OAAOC,MAAP,KAAkB,WAAlB,IAAiC;AAAA;AAAOA,MAAM,CAAC,GAAD,CAAb,KAAuB,QADzD;AAEO,IAAMC,MAAM,GAAG,OAAOC,GAAP,KAAe,WAA9B;AACA,IAAMC,MAAM,GAAG,OAAOC,GAAP,KAAe,WAA9B;AACA,IAAMC,UAAU,GACtB,OAAOC,KAAP,KAAiB,WAAjB,IACA,OAAOA,KAAK,CAACC,SAAb,KAA2B,WAD3B,IAEA,OAAOC,OAAP,KAAmB,WAHb;AAKP;;;;IAGaC,OAAO,GAAYV,SAAS;AAAA;AACtCC,MAAM,CAACU,GAAP,CAAW,eAAX,CADsC,oBAEnC,eAFmC,IAEjB,IAFiB;AAIzC;;;;;;;;;IAQaC,SAAS,GAAkBZ,SAAS;AAAA;AAC9CC,MAAM,CAACU,GAAP,CAAW,iBAAX,CAD8C,GAE7C;AAEG,IAAME,WAAW,GAAkBb,SAAS;AAAA;AAChDC,MAAM,CAACU,GAAP,CAAW,aAAX,CADgD,GAE/C,gBAFG;;AAKA,IAAMG,cAAc,GACzB,OAAOb,MAAP,IAAiB,WAAjB,IAAgCA,MAAM,CAACc,QAAxC,IAAsD,YADhD;;ACtCP,IAAMC,MAAM,GAAG;AACd,KAAG,eADW;AAEd,KAAG,8CAFW;AAGd,KAAG,uDAHW;AAId,GAJc,aAIZC,IAJY;AAKb,WACC,yHACAA,IAFD;AAIA,GATa;AAUd,KAAG,mHAVW;AAWd,KAAG,mCAXW;AAYd,KAAG,8DAZW;AAad,KAAG,iEAbW;AAcd,KAAG,0FAdW;AAed,KAAG,2EAfW;AAgBd,MAAI,sCAhBU;AAiBd,MAAI,0DAjBU;AAkBd,MAAI,0DAlBU;AAmBd,MAAI,4CAnBU;AAoBd,MAAI,qEApBU;AAqBd,IArBc,aAqBXC,IArBW;AAsBb,WAAO,+CAA+CA,IAAtD;AACA,GAvBa;AAwBd,MAAI,qCAxBU;AAyBd,IAzBc,aAyBXC,EAzBW;AA0Bb,WAAO,kCAAkCA,EAAzC;AACA,GA3Ba;AA4Bd,IA5Bc,aA4BXC,MA5BW;AA6Bb,gCAA0BA,MAA1B,uFAAmHA,MAAnH;AACA,GA9Ba;AA+Bd,MAAI,2EA/BU;AAgCd,IAhCc,aAgCXC,KAhCW;AAiCb,mKAA6JA,KAA7J;AACA,GAlCa;AAmCd,IAnCc,aAmCXA,KAnCW;AAoCb,gDAA0CA,KAA1C;AACA,GArCa;AAsCd,IAtCc,aAsCXA,KAtCW;AAuCb,iDAA2CA,KAA3C;AACA,GAxCa;AAyCd,MAAI;AAzCU,CAAf;AA4CA,SAAgBC,IAAIC;oCAA+BC;AAAAA,IAAAA;;;AAClD,EAAa;AACZ,QAAMC,CAAC,GAAGT,MAAM,CAACO,KAAD,CAAhB;AACA,QAAMG,GAAG,GAAG,CAACD,CAAD,GACT,uBAAuBF,KADd,GAET,OAAOE,CAAP,KAAa,UAAb,GACAA,CAAC,CAACE,KAAF,CAAQ,IAAR,EAAcH,IAAd,CADA,GAEAC,CAJH;AAKA,UAAM,IAAIG,KAAJ,cAAqBF,GAArB,CAAN;AACA;AAMD;;AC5CD;;AACA;;AACA,SAAgBG,QAAQC;AACvB,SAAO,CAAC,CAACA,KAAF,IAAW,CAAC,CAACA,KAAK,CAACjB,WAAD,CAAzB;AACA;AAED;;AACA;;AACA,SAAgBkB,YAAYD;;;AAC3B,MAAI,CAACA,KAAL,EAAY,OAAO,KAAP;AACZ,SACCE,aAAa,CAACF,KAAD,CAAb,IACAG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CADA,IAEA,CAAC,CAACA,KAAK,CAAClB,SAAD,CAFP,IAGA,CAAC,wBAACkB,KAAK,CAACK,WAAP,uDAAC,mBAAoBvB,SAApB,CAAD,CAHD,IAIAwB,KAAK,CAACN,KAAD,CAJL,IAKAO,KAAK,CAACP,KAAD,CANN;AAQA;AAED,IAAMQ,gBAAgB;AAAA;AAAGC,MAAM,CAACC,SAAP,CAAiBL,WAAjB,CAA6BM,QAA7B,EAAzB;AACA;;AACA,SAAgBT,cAAcF;AAC7B,MAAI,CAACA,KAAD,IAAU,OAAOA,KAAP,KAAiB,QAA/B,EAAyC,OAAO,KAAP;AACzC,MAAMY,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBb,KAAtB,CAAd;;AACA,MAAIY,KAAK,KAAK,IAAd,EAAoB;AACnB,WAAO,IAAP;AACA;;AACD,MAAME,IAAI,GACTL,MAAM,CAACM,cAAP,CAAsBC,IAAtB,CAA2BJ,KAA3B,EAAkC,aAAlC,KAAoDA,KAAK,CAACP,WAD3D;AAGA,MAAIS,IAAI,KAAKL,MAAb,EAAqB,OAAO,IAAP;AAErB,SACC,OAAOK,IAAP,IAAe,UAAf,IACAG,QAAQ,CAACN,QAAT,CAAkBK,IAAlB,CAAuBF,IAAvB,MAAiCN,gBAFlC;AAIA;AAKD,SAAgBU,SAASlB;AACxB,MAAI,CAACD,OAAO,CAACC,KAAD,CAAZ,EAAqBR,GAAG,CAAC,EAAD,EAAKQ,KAAL,CAAH;AACrB,SAAOA,KAAK,CAACjB,WAAD,CAAL,CAAmBoC,KAA1B;AACA;AAED;;AACA,AAAO,IAAMC,OAAO,GACnB,OAAOzC,OAAP,KAAmB,WAAnB,IAAkCA,OAAO,CAACyC,OAA1C,GACGzC,OAAO,CAACyC,OADX,GAEG,OAAOX,MAAM,CAACY,qBAAd,KAAwC,WAAxC,GACA,UAAAC,GAAG;AAAA,SACHb,MAAM,CAACc,mBAAP,CAA2BD,GAA3B,EAAgCE,MAAhC,CACCf,MAAM,CAACY,qBAAP,CAA6BC,GAA7B,CADD,CADG;AAAA,CADH;AAKA;AAA2Bb,MAAM,CAACc,mBAR/B;AAUP,AAAO,IAAME,yBAAyB,GACrChB,MAAM,CAACgB,yBAAP,IACA,SAASA,yBAAT,CAAmCC,MAAnC;AACC;AACA,MAAMC,GAAG,GAAQ,EAAjB;AACAP,EAAAA,OAAO,CAACM,MAAD,CAAP,CAAgBE,OAAhB,CAAwB,UAAAC,GAAG;AAC1BF,IAAAA,GAAG,CAACE,GAAD,CAAH,GAAWpB,MAAM,CAACqB,wBAAP,CAAgCJ,MAAhC,EAAwCG,GAAxC,CAAX;AACA,GAFD;AAGA,SAAOF,GAAP;AACA,CATK;AAgBP,SAAgBI,KAAKT,KAAUU,MAAWC;MAAAA;AAAAA,IAAAA,iBAAiB;;;AAC1D,MAAIC,WAAW,CAACZ,GAAD,CAAX;;AAAJ,IAA0C;AACzC,AAAC,OAACW,cAAc,GAAGxB,MAAM,CAAC0B,IAAV,GAAiBf,OAAhC,EAAyCE,GAAzC,EAA8CM,OAA9C,CAAsD,UAAAC,GAAG;AACzD,YAAI,CAACI,cAAD,IAAmB,OAAOJ,GAAP,KAAe,QAAtC,EAAgDG,IAAI,CAACH,GAAD,EAAMP,GAAG,CAACO,GAAD,CAAT,EAAgBP,GAAhB,CAAJ;AAChD,OAFA;AAGD,KAJD,MAIO;AACNA,IAAAA,GAAG,CAACM,OAAJ,CAAY,UAACQ,KAAD,EAAaC,KAAb;AAAA,aAA4BL,IAAI,CAACK,KAAD,EAAQD,KAAR,EAAed,GAAf,CAAhC;AAAA,KAAZ;AACA;AACD;AAED;;AACA,SAAgBY,YAAY3C;AAC3B;AACA,MAAM+C,KAAK,GAA2B/C,KAAK,CAACR,WAAD,CAA3C;AACA,SAAOuD,KAAK,GACTA,KAAK,CAACC,KAAN,GAAc,CAAd,GACCD,KAAK,CAACC,KAAN,GAAc,CADf;AAAA,IAEED,KAAK,CAACC,KAHC;AAAA,IAITpC,KAAK,CAACC,OAAN,CAAcb,KAAd;;AAAA,IAEAe,KAAK,CAACf,KAAD,CAAL;;AAAA,IAEAgB,KAAK,CAAChB,KAAD,CAAL;;AAAA;;AARH;AAWA;AAED;;AACA,SAAgBiD,IAAIjD,OAAYkD;AAC/B,SAAOP,WAAW,CAAC3C,KAAD,CAAX;;AAAA,IACJA,KAAK,CAACiD,GAAN,CAAUC,IAAV,CADI,GAEJhC,MAAM,CAACC,SAAP,CAAiBK,cAAjB,CAAgCC,IAAhC,CAAqCzB,KAArC,EAA4CkD,IAA5C,CAFH;AAGA;AAED;;AACA,SAAgBC,IAAInD,OAA2BkD;AAC9C;AACA,SAAOP,WAAW,CAAC3C,KAAD,CAAX;;AAAA,IAAsCA,KAAK,CAACmD,GAAN,CAAUD,IAAV,CAAtC,GAAwDlD,KAAK,CAACkD,IAAD,CAApE;AACA;AAED;;AACA,SAAgBE,IAAIpD,OAAYqD,gBAA6B5C;AAC5D,MAAM6C,CAAC,GAAGX,WAAW,CAAC3C,KAAD,CAArB;AACA,MAAIsD,CAAC;;AAAL,IAAwBtD,KAAK,CAACoD,GAAN,CAAUC,cAAV,EAA0B5C,KAA1B,EAAxB,KACK,IAAI6C,CAAC;;AAAL,IAAwB;AAC5BtD,MAAAA,KAAK,CAACuD,GAAN,CAAU9C,KAAV;AACA,KAFI,MAEET,KAAK,CAACqD,cAAD,CAAL,GAAwB5C,KAAxB;AACP;AAED;;AACA,SAAgB+C,GAAGC,GAAQC;AAC1B;AACA,MAAID,CAAC,KAAKC,CAAV,EAAa;AACZ,WAAOD,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAIC,CAAhC;AACA,GAFD,MAEO;AACN,WAAOD,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CAAxB;AACA;AACD;AAED;;AACA,SAAgB3C,MAAMoB;AACrB,SAAOtD,MAAM,IAAIsD,MAAM,YAAYrD,GAAnC;AACA;AAED;;AACA,SAAgBkC,MAAMmB;AACrB,SAAOpD,MAAM,IAAIoD,MAAM,YAAYnD,GAAnC;AACA;AACD;;AACA,SAAgB2E,OAAOZ;AACtB,SAAOA,KAAK,CAACa,KAAN,IAAeb,KAAK,CAACnB,KAA5B;AACA;AAED;;AACA,SAAgBiC,YAAYC;AAC3B,MAAIlD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAJ,EAAyB,OAAOlD,KAAK,CAACO,SAAN,CAAgB4C,KAAhB,CAAsBtC,IAAtB,CAA2BqC,IAA3B,CAAP;AACzB,MAAME,WAAW,GAAG9B,yBAAyB,CAAC4B,IAAD,CAA7C;AACA,SAAOE,WAAW,CAACxE,WAAD,CAAlB;AACA,MAAIoD,IAAI,GAAGf,OAAO,CAACmC,WAAD,CAAlB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrB,IAAI,CAACsB,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;AACrC,QAAM3B,GAAG,GAAQM,IAAI,CAACqB,CAAD,CAArB;AACA,QAAME,IAAI,GAAGH,WAAW,CAAC1B,GAAD,CAAxB;;AACA,QAAI6B,IAAI,CAACC,QAAL,KAAkB,KAAtB,EAA6B;AAC5BD,MAAAA,IAAI,CAACC,QAAL,GAAgB,IAAhB;AACAD,MAAAA,IAAI,CAACE,YAAL,GAAoB,IAApB;AACA,KANoC;AAQrC;AACA;;;AACA,QAAIF,IAAI,CAAChB,GAAL,IAAYgB,IAAI,CAACf,GAArB,EACCY,WAAW,CAAC1B,GAAD,CAAX,GAAmB;AAClB+B,MAAAA,YAAY,EAAE,IADI;AAElBD,MAAAA,QAAQ,EAAE,IAFQ;AAGlBE,MAAAA,UAAU,EAAEH,IAAI,CAACG,UAHC;AAIlB7D,MAAAA,KAAK,EAAEqD,IAAI,CAACxB,GAAD;AAJO,KAAnB;AAMD;;AACD,SAAOpB,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBwC,IAAtB,CAAd,EAA2CE,WAA3C,CAAP;AACA;AAUD,SAAgBQ,OAAUzC,KAAU0C;MAAAA;AAAAA,IAAAA,OAAgB;;;AACnD,MAAIC,QAAQ,CAAC3C,GAAD,CAAR,IAAiBvB,OAAO,CAACuB,GAAD,CAAxB,IAAiC,CAACrB,WAAW,CAACqB,GAAD,CAAjD,EAAwD,OAAOA,GAAP;;AACxD,MAAIY,WAAW,CAACZ,GAAD,CAAX,GAAmB;AAAE;AAAzB,IAA2C;AAC1CA,MAAAA,GAAG,CAACqB,GAAJ,GAAUrB,GAAG,CAACwB,GAAJ,GAAUxB,GAAG,CAAC4C,KAAJ,GAAY5C,GAAG,CAAC6C,MAAJ,GAAaC,2BAA7C;AACA;;AACD3D,EAAAA,MAAM,CAACsD,MAAP,CAAczC,GAAd;AACA,MAAI0C,IAAJ,EAAUjC,IAAI,CAACT,GAAD,EAAM,UAACO,GAAD,EAAM7B,KAAN;AAAA,WAAgB+D,MAAM,CAAC/D,KAAD,EAAQ,IAAR,CAAtB;AAAA,GAAN,EAA2C,IAA3C,CAAJ;AACV,SAAOsB,GAAP;AACA;;AAED,SAAS8C,2BAAT;AACC5E,EAAAA,GAAG,CAAC,CAAD,CAAH;AACA;;AAED,SAAgByE,SAAS3C;AACxB,MAAIA,GAAG,IAAI,IAAP,IAAe,OAAOA,GAAP,KAAe,QAAlC,EAA4C,OAAO,IAAP;;AAE5C,SAAOb,MAAM,CAACwD,QAAP,CAAgB3C,GAAhB,CAAP;AACA;;AC1MD;;AACA,IAAM+C,OAAO,GA4BT,EA5BJ;AAgCA,SAAgBC,UACfC;AAEA,MAAMjF,MAAM,GAAG+E,OAAO,CAACE,SAAD,CAAtB;;AACA,MAAI,CAACjF,MAAL,EAAa;AACZE,IAAAA,GAAG,CAAC,EAAD,EAAK+E,SAAL,CAAH;AACA;;;AAED,SAAOjF,MAAP;AACA;AAED,SAAgBkF,WACfD,WACAE;AAEA,MAAI,CAACJ,OAAO,CAACE,SAAD,CAAZ,EAAyBF,OAAO,CAACE,SAAD,CAAP,GAAqBE,cAArB;AACzB;;ACrCD,IAAIC,YAAJ;AAEA,SAAgBC;AACf,MAAI,CAAW,CAACD,YAAhB,EAA8BlF,GAAG,CAAC,CAAD,CAAH;AAC9B,SAAOkF,YAAP;AACA;;AAED,SAASE,WAAT,CACCC,OADD,EAECC,MAFD;AAIC,SAAO;AACNC,IAAAA,OAAO,EAAE,EADH;AAENF,IAAAA,OAAO,EAAPA,OAFM;AAGNC,IAAAA,MAAM,EAANA,MAHM;AAIN;AACA;AACAE,IAAAA,cAAc,EAAE,IANV;AAONC,IAAAA,kBAAkB,EAAE;AAPd,GAAP;AASA;;AAED,SAAgBC,kBACfC,OACAC;AAEA,MAAIA,aAAJ,EAAmB;AAClBd,IAAAA,SAAS,CAAC,SAAD,CAAT,CADkB;;AAElBa,IAAAA,KAAK,CAACE,QAAN,GAAiB,EAAjB;AACAF,IAAAA,KAAK,CAACG,eAAN,GAAwB,EAAxB;AACAH,IAAAA,KAAK,CAACI,cAAN,GAAuBH,aAAvB;AACA;AACD;AAED,SAAgBI,YAAYL;AAC3BM,EAAAA,UAAU,CAACN,KAAD,CAAV;AACAA,EAAAA,KAAK,CAACJ,OAAN,CAAcnD,OAAd,CAAsB8D,WAAtB;;AAEAP,EAAAA,KAAK,CAACJ,OAAN,GAAgB,IAAhB;AACA;AAED,SAAgBU,WAAWN;AAC1B,MAAIA,KAAK,KAAKT,YAAd,EAA4B;AAC3BA,IAAAA,YAAY,GAAGS,KAAK,CAACN,OAArB;AACA;AACD;AAED,SAAgBc,WAAWC;AAC1B,SAAQlB,YAAY,GAAGE,WAAW,CAACF,YAAD,EAAekB,KAAf,CAAlC;AACA;;AAED,SAASF,WAAT,CAAqBG,KAArB;AACC,MAAMvD,KAAK,GAAeuD,KAAK,CAAC9G,WAAD,CAA/B;AACA,MACCuD,KAAK,CAACC,KAAN;;AAAA,KACAD,KAAK,CAACC,KAAN;;AAFD,IAICD,KAAK,CAACwD,OAAN,GAJD,KAKKxD,KAAK,CAACyD,QAAN,GAAiB,IAAjB;AACL;;SC/DeC,cAAcC,QAAad;AAC1CA,EAAAA,KAAK,CAACF,kBAAN,GAA2BE,KAAK,CAACJ,OAAN,CAActB,MAAzC;AACA,MAAMyC,SAAS,GAAGf,KAAK,CAACJ,OAAN,CAAe,CAAf,CAAlB;AACA,MAAMoB,UAAU,GAAGF,MAAM,KAAKG,SAAX,IAAwBH,MAAM,KAAKC,SAAtD;AACA,MAAI,CAACf,KAAK,CAACL,MAAN,CAAauB,WAAlB,EACC/B,SAAS,CAAC,KAAD,CAAT,CAAiBgC,gBAAjB,CAAkCnB,KAAlC,EAAyCc,MAAzC,EAAiDE,UAAjD;;AACD,MAAIA,UAAJ,EAAgB;AACf,QAAID,SAAS,CAACnH,WAAD,CAAT,CAAuBwH,SAA3B,EAAsC;AACrCf,MAAAA,WAAW,CAACL,KAAD,CAAX;AACA3F,MAAAA,GAAG,CAAC,CAAD,CAAH;AACA;;AACD,QAAIS,WAAW,CAACgG,MAAD,CAAf,EAAyB;AACxB;AACAA,MAAAA,MAAM,GAAGO,QAAQ,CAACrB,KAAD,EAAQc,MAAR,CAAjB;AACA,UAAI,CAACd,KAAK,CAACN,OAAX,EAAoB4B,WAAW,CAACtB,KAAD,EAAQc,MAAR,CAAX;AACpB;;AACD,QAAId,KAAK,CAACE,QAAV,EAAoB;AACnBf,MAAAA,SAAS,CAAC,SAAD,CAAT,CAAqBoC,2BAArB,CACCR,SAAS,CAACnH,WAAD,CAAT,CAAuBoC,KADxB,EAEC8E,MAFD,EAGCd,KAAK,CAACE,QAHP,EAICF,KAAK,CAACG,eAJP;AAMA;AACD,GAlBD,MAkBO;AACN;AACAW,IAAAA,MAAM,GAAGO,QAAQ,CAACrB,KAAD,EAAQe,SAAR,EAAmB,EAAnB,CAAjB;AACA;;AACDV,EAAAA,WAAW,CAACL,KAAD,CAAX;;AACA,MAAIA,KAAK,CAACE,QAAV,EAAoB;AACnBF,IAAAA,KAAK,CAACI,cAAN,CAAsBJ,KAAK,CAACE,QAA5B,EAAsCF,KAAK,CAACG,eAA5C;AACA;;AACD,SAAOW,MAAM,KAAKrH,OAAX,GAAqBqH,MAArB,GAA8BG,SAArC;AACA;;AAED,SAASI,QAAT,CAAkBG,SAAlB,EAAyC3G,KAAzC,EAAqDZ,IAArD;AACC;AACA,MAAI6E,QAAQ,CAACjE,KAAD,CAAZ,EAAqB,OAAOA,KAAP;AAErB,MAAMsC,KAAK,GAAetC,KAAK,CAACjB,WAAD,CAA/B;;AAEA,MAAI,CAACuD,KAAL,EAAY;AACXP,IAAAA,IAAI,CACH/B,KADG,EAEH,UAAC6B,GAAD,EAAM+E,UAAN;AAAA,aACCC,gBAAgB,CAACF,SAAD,EAAYrE,KAAZ,EAAmBtC,KAAnB,EAA0B6B,GAA1B,EAA+B+E,UAA/B,EAA2CxH,IAA3C,CADjB;AAAA,KAFG,EAIH,IAJG;AAAA,KAAJ;AAMA,WAAOY,KAAP;AACA;;;AAED,MAAIsC,KAAK,CAACwE,MAAN,KAAiBH,SAArB,EAAgC,OAAO3G,KAAP;;AAEhC,MAAI,CAACsC,KAAK,CAACiE,SAAX,EAAsB;AACrBE,IAAAA,WAAW,CAACE,SAAD,EAAYrE,KAAK,CAACnB,KAAlB,EAAyB,IAAzB,CAAX;AACA,WAAOmB,KAAK,CAACnB,KAAb;AACA;;;AAED,MAAI,CAACmB,KAAK,CAACyE,UAAX,EAAuB;AACtBzE,IAAAA,KAAK,CAACyE,UAAN,GAAmB,IAAnB;AACAzE,IAAAA,KAAK,CAACwE,MAAN,CAAa7B,kBAAb;AACA,QAAMgB,MAAM;AAEX3D,IAAAA,KAAK,CAACC,KAAN;;AAAA,OAAuCD,KAAK,CAACC,KAAN;;AAAvC,MACID,KAAK,CAACa,KAAN,GAAcC,WAAW,CAACd,KAAK,CAAC0E,MAAP,CAD7B,GAEG1E,KAAK,CAACa,KAJV,CAHsB;AAStB;AACA;AACA;;AACA,QAAI8D,UAAU,GAAGhB,MAAjB;AACA,QAAI1F,KAAK,GAAG,KAAZ;;AACA,QAAI+B,KAAK,CAACC,KAAN;;AAAJ,MAAmC;AAClC0E,QAAAA,UAAU,GAAG,IAAI1I,GAAJ,CAAQ0H,MAAR,CAAb;AACAA,QAAAA,MAAM,CAAC/B,KAAP;AACA3D,QAAAA,KAAK,GAAG,IAAR;AACA;;AACDwB,IAAAA,IAAI,CAACkF,UAAD,EAAa,UAACpF,GAAD,EAAM+E,UAAN;AAAA,aAChBC,gBAAgB,CAACF,SAAD,EAAYrE,KAAZ,EAAmB2D,MAAnB,EAA2BpE,GAA3B,EAAgC+E,UAAhC,EAA4CxH,IAA5C,EAAkDmB,KAAlD,CADA;AAAA,KAAb,CAAJ,CAnBsB;;AAuBtBkG,IAAAA,WAAW,CAACE,SAAD,EAAYV,MAAZ,EAAoB,KAApB,CAAX,CAvBsB;;AAyBtB,QAAI7G,IAAI,IAAIuH,SAAS,CAACtB,QAAtB,EAAgC;AAC/Bf,MAAAA,SAAS,CAAC,SAAD,CAAT,CAAqB4C,gBAArB,CACC5E,KADD,EAEClD,IAFD,EAGCuH,SAAS,CAACtB,QAHX,EAICsB,SAAS,CAACrB,eAJX;AAMA;AACD;;AACD,SAAOhD,KAAK,CAACa,KAAb;AACA;;AAED,SAAS0D,gBAAT,CACCF,SADD,EAECQ,WAFD,EAGCC,YAHD,EAIC3E,IAJD,EAKCmE,UALD,EAMCS,QAND,EAOCC,WAPD;AASC,MAAI,CAAWV,UAAU,KAAKQ,YAA9B,EAA4C5H,GAAG,CAAC,CAAD,CAAH;;AAC5C,MAAIO,OAAO,CAAC6G,UAAD,CAAX,EAAyB;AACxB,QAAMxH,IAAI,GACTiI,QAAQ,IACRF,WADA,IAEAA,WAAY,CAAC5E,KAAb;;AAFA;AAGA,KAACC,GAAG,CAAE2E,WAA6C,CAACI,SAAhD,EAA4D9E,IAA5D,CAHJ;AAAA,MAIG4E,QAAS,CAAC7F,MAAV,CAAiBiB,IAAjB,CAJH,GAKG2D,SANJ,CADwB;;AASxB,QAAMzE,GAAG,GAAG6E,QAAQ,CAACG,SAAD,EAAYC,UAAZ,EAAwBxH,IAAxB,CAApB;AACAuD,IAAAA,GAAG,CAACyE,YAAD,EAAe3E,IAAf,EAAqBd,GAArB,CAAH,CAVwB;AAYxB;;AACA,QAAI5B,OAAO,CAAC4B,GAAD,CAAX,EAAkB;AACjBgF,MAAAA,SAAS,CAAC3B,cAAV,GAA2B,KAA3B;AACA,KAFD,MAEO;AACP,GAhBD,MAgBO,IAAIsC,WAAJ,EAAiB;AACvBF,IAAAA,YAAY,CAACtE,GAAb,CAAiB8D,UAAjB;AACA;;;AAED,MAAI3G,WAAW,CAAC2G,UAAD,CAAX,IAA2B,CAAC3C,QAAQ,CAAC2C,UAAD,CAAxC,EAAsD;AACrD,QAAI,CAACD,SAAS,CAAC7B,MAAV,CAAiB0C,WAAlB,IAAiCb,SAAS,CAAC1B,kBAAV,GAA+B,CAApE,EAAuE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;;AACDuB,IAAAA,QAAQ,CAACG,SAAD,EAAYC,UAAZ,CAAR,CATqD;;AAWrD,QAAI,CAACO,WAAD,IAAgB,CAACA,WAAW,CAACL,MAAZ,CAAmBjC,OAAxC,EACC4B,WAAW,CAACE,SAAD,EAAYC,UAAZ,CAAX;AACD;AACD;;AAED,SAASH,WAAT,CAAqBtB,KAArB,EAAwCnF,KAAxC,EAAoDgE,IAApD;MAAoDA;AAAAA,IAAAA,OAAO;;;AAC1D;AACA,MAAI,CAACmB,KAAK,CAACN,OAAP,IAAkBM,KAAK,CAACL,MAAN,CAAa0C,WAA/B,IAA8CrC,KAAK,CAACH,cAAxD,EAAwE;AACvEjB,IAAAA,MAAM,CAAC/D,KAAD,EAAQgE,IAAR,CAAN;AACA;AACD;;AC3HD;;;;;;AAKA,SAAgByD,iBACfpE,MACAqE;AAEA,MAAMtH,OAAO,GAAGD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAhB;AACA,MAAMf,KAAK,GAAe;AACzBC,IAAAA,KAAK,EAAEnC,OAAO;;AAAA,MAA2B;;AADhB;AAEzB;AACA0G,IAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAHvB;AAIzB;AACA4B,IAAAA,SAAS,EAAE,KALc;AAMzB;AACAQ,IAAAA,UAAU,EAAE,KAPa;AAQzB;AACAQ,IAAAA,SAAS,EAAE,EATc;AAUzB;AACA1C,IAAAA,OAAO,EAAE6C,MAXgB;AAYzB;AACAvG,IAAAA,KAAK,EAAEkC,IAbkB;AAczB;AACA2D,IAAAA,MAAM,EAAE,IAfiB;AAgBzB;AACA7D,IAAAA,KAAK,EAAE,IAjBkB;AAkBzB;AACA2C,IAAAA,OAAO,EAAE,IAnBgB;AAoBzB6B,IAAAA,SAAS,EAAE;AApBc,GAA1B;AAwBA;AACA;AACA;AACA;AACA;;AACA,MAAIjG,MAAM,GAAMY,KAAhB;AACA,MAAIsF,KAAK,GAAsCC,WAA/C;;AACA,MAAIzH,OAAJ,EAAa;AACZsB,IAAAA,MAAM,GAAG,CAACY,KAAD,CAAT;AACAsF,IAAAA,KAAK,GAAGE,UAAR;AACA;;yBAEuBrJ,KAAK,CAACC,SAAN,CAAgBgD,MAAhB,EAAwBkG,KAAxB;MAAjBG,0BAAAA;MAAQC,yBAAAA;;AACf1F,EAAAA,KAAK,CAAC0E,MAAN,GAAegB,KAAf;AACA1F,EAAAA,KAAK,CAACwD,OAAN,GAAgBiC,MAAhB;AACA,SAAOC,KAAP;AACA;AAED;;;;AAGA,AAAO,IAAMH,WAAW,GAA6B;AACpDnF,EAAAA,GADoD,eAChDJ,KADgD,EACzCG,IADyC;AAEnD,QAAIA,IAAI,KAAK1D,WAAb,EAA0B,OAAOuD,KAAP;AAE1B,QAAM2F,MAAM,GAAG/E,MAAM,CAACZ,KAAD,CAArB;;AACA,QAAI,CAACE,GAAG,CAACyF,MAAD,EAASxF,IAAT,CAAR,EAAwB;AACvB;AACA,aAAOyF,iBAAiB,CAAC5F,KAAD,EAAQ2F,MAAR,EAAgBxF,IAAhB,CAAxB;AACA;;AACD,QAAMzC,KAAK,GAAGiI,MAAM,CAACxF,IAAD,CAApB;;AACA,QAAIH,KAAK,CAACyE,UAAN,IAAoB,CAAC9G,WAAW,CAACD,KAAD,CAApC,EAA6C;AAC5C,aAAOA,KAAP;AACA;AAED;;;AACA,QAAIA,KAAK,KAAKmI,IAAI,CAAC7F,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAlB,EAAuC;AACtC2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;AACA,aAAQA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAA4B4F,WAAW,CAC9C/F,KAAK,CAACwE,MAAN,CAAahC,MADiC,EAE9C9E,KAF8C,EAG9CsC,KAH8C,CAA/C;AAKA;;AACD,WAAOtC,KAAP;AACA,GAxBmD;AAyBpDwC,EAAAA,GAzBoD,eAyBhDF,KAzBgD,EAyBzCG,IAzByC;AA0BnD,WAAOA,IAAI,IAAIS,MAAM,CAACZ,KAAD,CAArB;AACA,GA3BmD;AA4BpDlB,EAAAA,OA5BoD,mBA4B5CkB,KA5B4C;AA6BnD,WAAO3D,OAAO,CAACyC,OAAR,CAAgB8B,MAAM,CAACZ,KAAD,CAAtB,CAAP;AACA,GA9BmD;AA+BpDK,EAAAA,GA/BoD,eAgCnDL,KAhCmD,EAiCnDG;AAAa;AAjCsC,IAkCnDzC,KAlCmD;AAoCnD,QAAM0D,IAAI,GAAG4E,sBAAsB,CAACpF,MAAM,CAACZ,KAAD,CAAP,EAAgBG,IAAhB,CAAnC;;AACA,QAAIiB,IAAJ,aAAIA,IAAJ,uBAAIA,IAAI,CAAEf,GAAV,EAAe;AACd;AACA;AACAe,MAAAA,IAAI,CAACf,GAAL,CAAS3B,IAAT,CAAcsB,KAAK,CAAC0E,MAApB,EAA4BhH,KAA5B;AACA,aAAO,IAAP;AACA;;AACD,QAAI,CAACsC,KAAK,CAACiE,SAAX,EAAsB;AACrB;AACA;AACA,UAAMgC,OAAO,GAAGJ,IAAI,CAACjF,MAAM,CAACZ,KAAD,CAAP,EAAgBG,IAAhB,CAApB,CAHqB;;AAKrB,UAAM+F,YAAY,GAAqBD,OAArB,aAAqBA,OAArB,uBAAqBA,OAAO,CAAGxJ,WAAH,CAA9C;;AACA,UAAIyJ,YAAY,IAAIA,YAAY,CAACrH,KAAb,KAAuBnB,KAA3C,EAAkD;AACjDsC,QAAAA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAAqBzC,KAArB;AACAsC,QAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,KAAxB;AACA,eAAO,IAAP;AACA;;AACD,UAAIM,EAAE,CAAC/C,KAAD,EAAQuI,OAAR,CAAF,KAAuBvI,KAAK,KAAKoG,SAAV,IAAuB5D,GAAG,CAACF,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAjD,CAAJ,EACC,OAAO,IAAP;AACD2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;AACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;AACA;;AAED,QACEA,KAAK,CAACa,KAAN,CAAaV,IAAb,MAAuBzC,KAAvB;AAECA,IAAAA,KAAK,KAAKoG,SAAV,IAAuB3D,IAAI,IAAIH,KAAK,CAACa,KAFtC,CAAD;AAICuF,IAAAA,MAAM,CAACC,KAAP,CAAa3I,KAAb,KAAuB0I,MAAM,CAACC,KAAP,CAAarG,KAAK,CAACa,KAAN,CAAaV,IAAb,CAAb,CALzB,EAOC,OAAO,IAAP;;AAGDH,IAAAA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAAqBzC,KAArB;AACAsC,IAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,IAAxB;AACA,WAAO,IAAP;AACA,GAzEmD;AA0EpDmG,EAAAA,cA1EoD,0BA0ErCtG,KA1EqC,EA0E9BG,IA1E8B;AA2EnD;AACA,QAAI0F,IAAI,CAAC7F,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAJ,KAA4B2D,SAA5B,IAAyC3D,IAAI,IAAIH,KAAK,CAACnB,KAA3D,EAAkE;AACjEmB,MAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,KAAxB;AACA2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;AACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;AACA,KAJD,MAIO;AACN;AACA,aAAOA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,CAAP;AACA;;;AAED,QAAIH,KAAK,CAACa,KAAV,EAAiB,OAAOb,KAAK,CAACa,KAAN,CAAYV,IAAZ,CAAP;AACjB,WAAO,IAAP;AACA,GAvFmD;AAwFpD;AACA;AACAX,EAAAA,wBA1FoD,oCA0F3BQ,KA1F2B,EA0FpBG,IA1FoB;AA2FnD,QAAMoG,KAAK,GAAG3F,MAAM,CAACZ,KAAD,CAApB;AACA,QAAMoB,IAAI,GAAG/E,OAAO,CAACmD,wBAAR,CAAiC+G,KAAjC,EAAwCpG,IAAxC,CAAb;AACA,QAAI,CAACiB,IAAL,EAAW,OAAOA,IAAP;AACX,WAAO;AACNC,MAAAA,QAAQ,EAAE,IADJ;AAENC,MAAAA,YAAY,EAAEtB,KAAK,CAACC,KAAN;;AAAA,SAAwCE,IAAI,KAAK,QAFzD;AAGNoB,MAAAA,UAAU,EAAEH,IAAI,CAACG,UAHX;AAIN7D,MAAAA,KAAK,EAAE6I,KAAK,CAACpG,IAAD;AAJN,KAAP;AAMA,GApGmD;AAqGpDqG,EAAAA,cArGoD;AAsGnDtJ,IAAAA,GAAG,CAAC,EAAD,CAAH;AACA,GAvGmD;AAwGpDqB,EAAAA,cAxGoD,0BAwGrCyB,KAxGqC;AAyGnD,WAAO7B,MAAM,CAACI,cAAP,CAAsByB,KAAK,CAACnB,KAA5B,CAAP;AACA,GA1GmD;AA2GpD4H,EAAAA,cA3GoD;AA4GnDvJ,IAAAA,GAAG,CAAC,EAAD,CAAH;AACA;AA7GmD,CAA9C;AAgHP;;;;AAIA,IAAMsI,UAAU,GAAoC,EAApD;AACA/F,IAAI,CAAC8F,WAAD,EAAc,UAAChG,GAAD,EAAMmH,EAAN;AACjB;AACAlB,EAAAA,UAAU,CAACjG,GAAD,CAAV,GAAkB;AACjBoH,IAAAA,SAAS,CAAC,CAAD,CAAT,GAAeA,SAAS,CAAC,CAAD,CAAT,CAAa,CAAb,CAAf;AACA,WAAOD,EAAE,CAACnJ,KAAH,CAAS,IAAT,EAAeoJ,SAAf,CAAP;AACA,GAHD;AAIA,CANG,CAAJ;;AAOAnB,UAAU,CAACc,cAAX,GAA4B,UAAStG,KAAT,EAAgBG,IAAhB;AAC3B,MAAI,CAAWkG,KAAK,CAACO,QAAQ,CAACzG,IAAD,CAAT,CAApB,EAA6CjD,GAAG,CAAC,EAAD,CAAH;;AAE7C,SAAOsI,UAAU,CAACnF,GAAX,CAAgB3B,IAAhB,CAAqB,IAArB,EAA2BsB,KAA3B,EAAkCG,IAAlC,EAAwC2D,SAAxC,CAAP;AACA,CAJD;;AAKA0B,UAAU,CAACnF,GAAX,GAAiB,UAASL,KAAT,EAAgBG,IAAhB,EAAsBzC,KAAtB;AAChB,MAAI,CAAWyC,IAAI,KAAK,QAApB,IAAgCkG,KAAK,CAACO,QAAQ,CAACzG,IAAD,CAAT,CAAzC,EAAkEjD,GAAG,CAAC,EAAD,CAAH;AAClE,SAAOqI,WAAW,CAAClF,GAAZ,CAAiB3B,IAAjB,CAAsB,IAAtB,EAA4BsB,KAAK,CAAC,CAAD,CAAjC,EAAsCG,IAAtC,EAA4CzC,KAA5C,EAAmDsC,KAAK,CAAC,CAAD,CAAxD,CAAP;AACA,CAHD;;;AAMA,SAAS6F,IAAT,CAActC,KAAd,EAA8BpD,IAA9B;AACC,MAAMH,KAAK,GAAGuD,KAAK,CAAC9G,WAAD,CAAnB;AACA,MAAMkJ,MAAM,GAAG3F,KAAK,GAAGY,MAAM,CAACZ,KAAD,CAAT,GAAmBuD,KAAvC;AACA,SAAOoC,MAAM,CAACxF,IAAD,CAAb;AACA;;AAED,SAASyF,iBAAT,CAA2B5F,KAA3B,EAA8C2F,MAA9C,EAA2DxF,IAA3D;;;AACC,MAAMiB,IAAI,GAAG4E,sBAAsB,CAACL,MAAD,EAASxF,IAAT,CAAnC;AACA,SAAOiB,IAAI,GACR,WAAWA,IAAX,GACCA,IAAI,CAAC1D,KADN;AAGC;AAHD,eAIC0D,IAAI,CAAChB,GAJN,8CAIC,UAAU1B,IAAV,CAAesB,KAAK,CAAC0E,MAArB,CALO,GAMRZ,SANH;AAOA;;AAED,SAASkC,sBAAT,CACCL,MADD,EAECxF,IAFD;AAIC;AACA,MAAI,EAAEA,IAAI,IAAIwF,MAAV,CAAJ,EAAuB,OAAO7B,SAAP;AACvB,MAAIxF,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBoH,MAAtB,CAAZ;;AACA,SAAOrH,KAAP,EAAc;AACb,QAAM8C,IAAI,GAAGjD,MAAM,CAACqB,wBAAP,CAAgClB,KAAhC,EAAuC6B,IAAvC,CAAb;AACA,QAAIiB,IAAJ,EAAU,OAAOA,IAAP;AACV9C,IAAAA,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBD,KAAtB,CAAR;AACA;;AACD,SAAOwF,SAAP;AACA;;AAED,SAAgBqC,YAAYnG;AAC3B,MAAI,CAACA,KAAK,CAACiE,SAAX,EAAsB;AACrBjE,IAAAA,KAAK,CAACiE,SAAN,GAAkB,IAAlB;;AACA,QAAIjE,KAAK,CAACuC,OAAV,EAAmB;AAClB4D,MAAAA,WAAW,CAACnG,KAAK,CAACuC,OAAP,CAAX;AACA;AACD;AACD;AAED,SAAgBuD,YAAY9F;AAC3B,MAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;AACjBb,IAAAA,KAAK,CAACa,KAAN,GAAcC,WAAW,CAACd,KAAK,CAACnB,KAAP,CAAzB;AACA;AACD;;ICrPYgI,KAAb;AAAA;AAAA;AAKC,iBAAYC,MAAZ;;;AAJA,oBAAA,GAAuB5K,UAAvB;AAEA,oBAAA,GAAuB,IAAvB;AASA;;;;;;;;;;;;;;;;;;;;AAmBA,gBAAA,GAAoB,UAAC6E,IAAD,EAAYgG,MAAZ,EAA0BjE,aAA1B;AACnB;AACA,UAAI,OAAO/B,IAAP,KAAgB,UAAhB,IAA8B,OAAOgG,MAAP,KAAkB,UAApD,EAAgE;AAC/D,YAAMC,WAAW,GAAGD,MAApB;AACAA,QAAAA,MAAM,GAAGhG,IAAT;AAEA,YAAMkG,IAAI,GAAG,KAAb;AACA,eAAO,SAASC,cAAT,CAENnG,IAFM;;;cAENA;AAAAA,YAAAA,OAAOiG;;;4CACJ5J;AAAAA,YAAAA;;;AAEH,iBAAO6J,IAAI,CAACE,OAAL,CAAapG,IAAb,EAAmB,UAACwC,KAAD;AAAA;;AAAA,mBAAoB,WAAAwD,MAAM,EAACrI,IAAP,iBAAY,MAAZ,EAAkB6E,KAAlB,SAA4BnG,IAA5B,EAApB;AAAA,WAAnB,CAAP;AACA,SAND;AAOA;;AAED,UAAI,OAAO2J,MAAP,KAAkB,UAAtB,EAAkC7J,GAAG,CAAC,CAAD,CAAH;AAClC,UAAI4F,aAAa,KAAKgB,SAAlB,IAA+B,OAAOhB,aAAP,KAAyB,UAA5D,EACC5F,GAAG,CAAC,CAAD,CAAH;AAED,UAAIyG,MAAJ;;AAGA,UAAIhG,WAAW,CAACoD,IAAD,CAAf,EAAuB;AACtB,YAAM8B,KAAK,GAAGQ,UAAU,CAAC,KAAD,CAAxB;AACA,YAAMqC,KAAK,GAAGK,WAAW,CAAC,KAAD,EAAOhF,IAAP,EAAa+C,SAAb,CAAzB;AACA,YAAIsD,QAAQ,GAAG,IAAf;;AACA,YAAI;AACHzD,UAAAA,MAAM,GAAGoD,MAAM,CAACrB,KAAD,CAAf;AACA0B,UAAAA,QAAQ,GAAG,KAAX;AACA,SAHD,SAGU;AACT;AACA,cAAIA,QAAJ,EAAclE,WAAW,CAACL,KAAD,CAAX,CAAd,KACKM,UAAU,CAACN,KAAD,CAAV;AACL;;AACD,YAAI,OAAOwE,OAAP,KAAmB,WAAnB,IAAkC1D,MAAM,YAAY0D,OAAxD,EAAiE;AAChE,iBAAO1D,MAAM,CAAC2D,IAAP,CACN,UAAA3D,MAAM;AACLf,YAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;AACA,mBAAOY,aAAa,CAACC,MAAD,EAASd,KAAT,CAApB;AACA,WAJK,EAKN,UAAA1F,KAAK;AACJ+F,YAAAA,WAAW,CAACL,KAAD,CAAX;AACA,kBAAM1F,KAAN;AACA,WARK,CAAP;AAUA;;AACDyF,QAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;AACA,eAAOY,aAAa,CAACC,MAAD,EAASd,KAAT,CAApB;AACA,OA1BD,MA0BO,IAAI,CAAC9B,IAAD,IAAS,OAAOA,IAAP,KAAgB,QAA7B,EAAuC;AAC7C4C,QAAAA,MAAM,GAAGoD,MAAM,CAAChG,IAAD,CAAf;AACA,YAAI4C,MAAM,KAAKG,SAAf,EAA0BH,MAAM,GAAG5C,IAAT;AAC1B,YAAI4C,MAAM,KAAKrH,OAAf,EAAwBqH,MAAM,GAAGG,SAAT;AACxB,YAAI,KAAI,CAACoB,WAAT,EAAsBzD,MAAM,CAACkC,MAAD,EAAS,IAAT,CAAN;;AACtB,YAAIb,aAAJ,EAAmB;AAClB,cAAMyE,CAAC,GAAY,EAAnB;AACA,cAAMC,EAAE,GAAY,EAApB;AACAxF,UAAAA,SAAS,CAAC,SAAD,CAAT,CAAqBoC,2BAArB,CAAiDrD,IAAjD,EAAuD4C,MAAvD,EAA+D4D,CAA/D,EAAkEC,EAAlE;AACA1E,UAAAA,aAAa,CAACyE,CAAD,EAAIC,EAAJ,CAAb;AACA;;AACD,eAAO7D,MAAP;AACA,OAZM,MAYAzG,GAAG,CAAC,EAAD,EAAK6D,IAAL,CAAH;AACP,KA9DD;;AAgEA,2BAAA,GAA0C,UAACA,IAAD,EAAYgG,MAAZ;AACzC;AACA,UAAI,OAAOhG,IAAP,KAAgB,UAApB,EAAgC;AAC/B,eAAO,UAACf,KAAD;AAAA,6CAAgB5C,IAAhB;AAAgBA,YAAAA,IAAhB;AAAA;;AAAA,iBACN,KAAI,CAACqK,kBAAL,CAAwBzH,KAAxB,EAA+B,UAACuD,KAAD;AAAA,mBAAgBxC,IAAI,MAAJ,UAAKwC,KAAL,SAAenG,IAAf,EAAhB;AAAA,WAA/B,CADM;AAAA,SAAP;AAEA;;AAED,UAAIsK,OAAJ,EAAsBC,cAAtB;;AACA,UAAMhE,MAAM,GAAG,KAAI,CAACwD,OAAL,CAAapG,IAAb,EAAmBgG,MAAnB,EAA2B,UAACQ,CAAD,EAAaC,EAAb;AACzCE,QAAAA,OAAO,GAAGH,CAAV;AACAI,QAAAA,cAAc,GAAGH,EAAjB;AACA,OAHc,CAAf;;AAKA,UAAI,OAAOH,OAAP,KAAmB,WAAnB,IAAkC1D,MAAM,YAAY0D,OAAxD,EAAiE;AAChE,eAAO1D,MAAM,CAAC2D,IAAP,CAAY,UAAAM,SAAS;AAAA,iBAAI,CAACA,SAAD,EAAYF,OAAZ,EAAsBC,cAAtB,CAAJ;AAAA,SAArB,CAAP;AACA;;AACD,aAAO,CAAChE,MAAD,EAAS+D,OAAT,EAAmBC,cAAnB,CAAP;AACA,KAjBD;;AAzFC,QAAI,QAAOb,MAAP,aAAOA,MAAP,uBAAOA,MAAM,CAAEe,UAAf,MAA8B,SAAlC,EACC,KAAKC,aAAL,CAAmBhB,MAAO,CAACe,UAA3B;AACD,QAAI,QAAOf,MAAP,aAAOA,MAAP,uBAAOA,MAAM,CAAEiB,UAAf,MAA8B,SAAlC,EACC,KAAKC,aAAL,CAAmBlB,MAAO,CAACiB,UAA3B;AACD;;AAVF;;AAAA,SAkHCE,WAlHD,GAkHC,qBAAiClH,IAAjC;AACC,QAAI,CAACpD,WAAW,CAACoD,IAAD,CAAhB,EAAwB7D,GAAG,CAAC,CAAD,CAAH;AACxB,QAAIO,OAAO,CAACsD,IAAD,CAAX,EAAmBA,IAAI,GAAGkF,OAAO,CAAClF,IAAD,CAAd;AACnB,QAAM8B,KAAK,GAAGQ,UAAU,CAAC,IAAD,CAAxB;AACA,QAAMqC,KAAK,GAAGK,WAAW,CAAC,IAAD,EAAOhF,IAAP,EAAa+C,SAAb,CAAzB;AACA4B,IAAAA,KAAK,CAACjJ,WAAD,CAAL,CAAmB4I,SAAnB,GAA+B,IAA/B;AACAlC,IAAAA,UAAU,CAACN,KAAD,CAAV;AACA,WAAO6C,KAAP;AACA,GA1HF;;AAAA,SA4HCwC,WA5HD,GA4HC,qBACC3E,KADD,EAECT,aAFD;AAIC,QAAM9C,KAAK,GAAeuD,KAAK,IAAKA,KAAa,CAAC9G,WAAD,CAAjD;;AACA,IAAa;AACZ,UAAI,CAACuD,KAAD,IAAU,CAACA,KAAK,CAACqF,SAArB,EAAgCnI,GAAG,CAAC,CAAD,CAAH;AAChC,UAAI8C,KAAK,CAACyE,UAAV,EAAsBvH,GAAG,CAAC,EAAD,CAAH;AACtB;;QACc2F,QAAS7C,MAAjBwE;AACP5B,IAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;AACA,WAAOY,aAAa,CAACI,SAAD,EAAYjB,KAAZ,CAApB;AACA;AAED;;;;;AA1ID;;AAAA,SA+ICmF,aA/ID,GA+IC,uBAActK,KAAd;AACC,SAAKwH,WAAL,GAAmBxH,KAAnB;AACA;AAED;;;;;;AAnJD;;AAAA,SAyJCoK,aAzJD,GAyJC,uBAAcpK,KAAd;AACC,QAAIA,KAAK,IAAI,CAACxB,UAAd,EAA0B;AACzBgB,MAAAA,GAAG,CAAC,EAAD,CAAH;AACA;;AACD,SAAK6G,WAAL,GAAmBrG,KAAnB;AACA,GA9JF;;AAAA,SAgKCyK,YAhKD,GAgKC,sBAAkCpH,IAAlC,EAA2C2G,OAA3C;AACC;AACA;AACA,QAAIxG,CAAJ;;AACA,SAAKA,CAAC,GAAGwG,OAAO,CAACvG,MAAR,GAAiB,CAA1B,EAA6BD,CAAC,IAAI,CAAlC,EAAqCA,CAAC,EAAtC,EAA0C;AACzC,UAAMkH,KAAK,GAAGV,OAAO,CAACxG,CAAD,CAArB;;AACA,UAAIkH,KAAK,CAACtL,IAAN,CAAWqE,MAAX,KAAsB,CAAtB,IAA2BiH,KAAK,CAACrL,EAAN,KAAa,SAA5C,EAAuD;AACtDgE,QAAAA,IAAI,GAAGqH,KAAK,CAAC1K,KAAb;AACA;AACA;AACD;AAED;;;AACA,QAAIwD,CAAC,GAAG,CAAC,CAAT,EAAY;AACXwG,MAAAA,OAAO,GAAGA,OAAO,CAAC1G,KAAR,CAAcE,CAAC,GAAG,CAAlB,CAAV;AACA;;AAED,QAAMmH,gBAAgB,GAAGrG,SAAS,CAAC,SAAD,CAAT,CAAqBsG,aAA9C;;AACA,QAAI7K,OAAO,CAACsD,IAAD,CAAX,EAAmB;AAClB;AACA,aAAOsH,gBAAgB,CAACtH,IAAD,EAAO2G,OAAP,CAAvB;AACA;;;AAED,WAAO,KAAKP,OAAL,CAAapG,IAAb,EAAmB,UAACwC,KAAD;AAAA,aACzB8E,gBAAgB,CAAC9E,KAAD,EAAQmE,OAAR,CADS;AAAA,KAAnB,CAAP;AAGA,GA1LF;;AAAA;AAAA;AA6LA,SAAgB3B,YACfzC,OACA5F,OACA0H;AAEA;AACA,MAAM7B,KAAK,GAAYvF,KAAK,CAACN,KAAD,CAAL,GACpBsE,SAAS,CAAC,QAAD,CAAT,CAAoBuG,SAApB,CAA8B7K,KAA9B,EAAqC0H,MAArC,CADoB,GAEpBnH,KAAK,CAACP,KAAD,CAAL,GACAsE,SAAS,CAAC,QAAD,CAAT,CAAoBwG,SAApB,CAA8B9K,KAA9B,EAAqC0H,MAArC,CADA,GAEA9B,KAAK,CAACS,WAAN,GACAoB,gBAAgB,CAACzH,KAAD,EAAQ0H,MAAR,CADhB,GAEApD,SAAS,CAAC,KAAD,CAAT,CAAiByG,eAAjB,CAAiC/K,KAAjC,EAAwC0H,MAAxC,CANH;AAQA,MAAMvC,KAAK,GAAGuC,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAAtD;AACAQ,EAAAA,KAAK,CAACJ,OAAN,CAAciG,IAAd,CAAmBnF,KAAnB;AACA,SAAOA,KAAP;AACA;;SC/Ne0C,QAAQvI;AACvB,MAAI,CAACD,OAAO,CAACC,KAAD,CAAZ,EAAqBR,GAAG,CAAC,EAAD,EAAKQ,KAAL,CAAH;AACrB,SAAOiL,WAAW,CAACjL,KAAD,CAAlB;AACA;;AAED,SAASiL,WAAT,CAAqBjL,KAArB;AACC,MAAI,CAACC,WAAW,CAACD,KAAD,CAAhB,EAAyB,OAAOA,KAAP;AACzB,MAAMsC,KAAK,GAA2BtC,KAAK,CAACjB,WAAD,CAA3C;AACA,MAAImM,IAAJ;AACA,MAAMC,QAAQ,GAAGjJ,WAAW,CAAClC,KAAD,CAA5B;;AACA,MAAIsC,KAAJ,EAAW;AACV,QACC,CAACA,KAAK,CAACiE,SAAP,KACCjE,KAAK,CAACC,KAAN,GAAc,CAAd,IAAmB,CAAC+B,SAAS,CAAC,KAAD,CAAT,CAAiB8G,WAAjB,CAA6B9I,KAA7B,CADrB,CADD,EAIC,OAAOA,KAAK,CAACnB,KAAb,CALS;;AAOVmB,IAAAA,KAAK,CAACyE,UAAN,GAAmB,IAAnB;AACAmE,IAAAA,IAAI,GAAGG,UAAU,CAACrL,KAAD,EAAQmL,QAAR,CAAjB;AACA7I,IAAAA,KAAK,CAACyE,UAAN,GAAmB,KAAnB;AACA,GAVD,MAUO;AACNmE,IAAAA,IAAI,GAAGG,UAAU,CAACrL,KAAD,EAAQmL,QAAR,CAAjB;AACA;;AAEDpJ,EAAAA,IAAI,CAACmJ,IAAD,EAAO,UAACrJ,GAAD,EAAM+E,UAAN;AACV,QAAItE,KAAK,IAAII,GAAG,CAACJ,KAAK,CAACnB,KAAP,EAAcU,GAAd,CAAH,KAA0B+E,UAAvC,EAAmD;;AACnDjE,IAAAA,GAAG,CAACuI,IAAD,EAAOrJ,GAAP,EAAYoJ,WAAW,CAACrE,UAAD,CAAvB,CAAH;AACA,GAHG,CAAJ;;AAKA,SAAOuE,QAAQ;;AAAR,IAA4B,IAAI5M,GAAJ,CAAQ2M,IAAR,CAA5B,GAA4CA,IAAnD;AACA;;AAED,SAASG,UAAT,CAAoBrL,KAApB,EAAgCmL,QAAhC;AACC;AACA,UAAQA,QAAR;AACC;;AAAA;AACC,aAAO,IAAI9M,GAAJ,CAAQ2B,KAAR,CAAP;;AACD;;AAAA;AACC;AACA,aAAOG,KAAK,CAACmL,IAAN,CAAWtL,KAAX,CAAP;AALF;;AAOA,SAAOoD,WAAW,CAACpD,KAAD,CAAlB;AACA;;SCnCeuL;AACf,WAASjF,gBAAT,CACCnB,KADD,EAECc,MAFD,EAGCE,UAHD;AAKC,QAAI,CAACA,UAAL,EAAiB;AAChB,UAAIhB,KAAK,CAACE,QAAV,EAAoB;AACnBmG,QAAAA,sBAAsB,CAACrG,KAAK,CAACJ,OAAN,CAAe,CAAf,CAAD,CAAtB;AACA,OAHe;;;AAKhB0G,MAAAA,gBAAgB,CAACtG,KAAK,CAACJ,OAAP,CAAhB;AACA,KAND;AAAA,SAQK,IACJhF,OAAO,CAACkG,MAAD,CAAP,IACCA,MAAM,CAAClH,WAAD,CAAN,CAAiC+H,MAAjC,KAA4C3B,KAFzC,EAGH;AACDsG,QAAAA,gBAAgB,CAACtG,KAAK,CAACJ,OAAP,CAAhB;AACA;AACD;;AAED,WAAS2G,cAAT,CAAwBtL,OAAxB,EAA0CiD,IAA1C;AACC,QAAIjD,OAAJ,EAAa;AACZ,UAAMyF,KAAK,GAAG,IAAI1F,KAAJ,CAAUkD,IAAI,CAACI,MAAf,CAAd;;AACA,WAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,IAAI,CAACI,MAAzB,EAAiCD,CAAC,EAAlC;AACC/C,QAAAA,MAAM,CAACqI,cAAP,CAAsBjD,KAAtB,EAA6B,KAAKrC,CAAlC,EAAqCmI,aAAa,CAACnI,CAAD,EAAI,IAAJ,CAAlD;AADD;;AAEA,aAAOqC,KAAP;AACA,KALD,MAKO;AACN,UAAMtC,YAAW,GAAG9B,yBAAyB,CAAC4B,IAAD,CAA7C;;AACA,aAAOE,YAAW,CAACxE,WAAD,CAAlB;AACA,UAAMoD,IAAI,GAAGf,OAAO,CAACmC,YAAD,CAApB;;AACA,WAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGrB,IAAI,CAACsB,MAAzB,EAAiCD,EAAC,EAAlC,EAAsC;AACrC,YAAM3B,GAAG,GAAQM,IAAI,CAACqB,EAAD,CAArB;AACAD,QAAAA,YAAW,CAAC1B,GAAD,CAAX,GAAmB8J,aAAa,CAC/B9J,GAD+B,EAE/BzB,OAAO,IAAI,CAAC,CAACmD,YAAW,CAAC1B,GAAD,CAAX,CAAiBgC,UAFC,CAAhC;AAIA;;AACD,aAAOpD,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBwC,IAAtB,CAAd,EAA2CE,YAA3C,CAAP;AACA;AACD;;AAED,WAASwH,eAAT,CACC1H,IADD,EAECqE,MAFD;AAIC,QAAMtH,OAAO,GAAGD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAhB;AACA,QAAMwC,KAAK,GAAG6F,cAAc,CAACtL,OAAD,EAAUiD,IAAV,CAA5B;AAEA,QAAMf,KAAK,GAAmC;AAC7CC,MAAAA,KAAK,EAAEnC,OAAO;;AAAA,QAAyB;;AADM;AAE7C0G,MAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAFH;AAG7C4B,MAAAA,SAAS,EAAE,KAHkC;AAI7CQ,MAAAA,UAAU,EAAE,KAJiC;AAK7CQ,MAAAA,SAAS,EAAE,EALkC;AAM7C1C,MAAAA,OAAO,EAAE6C,MANoC;AAO7C;AACAvG,MAAAA,KAAK,EAAEkC,IARsC;AAS7C;AACA2D,MAAAA,MAAM,EAAEnB,KAVqC;AAW7C1C,MAAAA,KAAK,EAAE,IAXsC;AAY7C4C,MAAAA,QAAQ,EAAE,KAZmC;AAa7C4B,MAAAA,SAAS,EAAE;AAbkC,KAA9C;AAgBAlH,IAAAA,MAAM,CAACqI,cAAP,CAAsBjD,KAAtB,EAA6B9G,WAA7B,EAA0C;AACzCiB,MAAAA,KAAK,EAAEsC,KADkC;AAEzC;AACAqB,MAAAA,QAAQ,EAAE;AAH+B,KAA1C;AAKA,WAAOkC,KAAP;AACA;AAGD;;;AACA,MAAMtC,WAAW,GAAyC,EAA1D;;AAEA,WAASoI,aAAT,CACClJ,IADD,EAECoB,UAFD;AAIC,QAAIH,IAAI,GAAGH,WAAW,CAACd,IAAD,CAAtB;;AACA,QAAIiB,IAAJ,EAAU;AACTA,MAAAA,IAAI,CAACG,UAAL,GAAkBA,UAAlB;AACA,KAFD,MAEO;AACNN,MAAAA,WAAW,CAACd,IAAD,CAAX,GAAoBiB,IAAI,GAAG;AAC1BE,QAAAA,YAAY,EAAE,IADY;AAE1BC,QAAAA,UAAU,EAAVA,UAF0B;AAG1BnB,QAAAA,GAH0B;AAIzB,cAAMJ,KAAK,GAAG,KAAKvD,WAAL,CAAd;AACA,UAAa6M,eAAe,CAACtJ,KAAD,CAAf;;AAEb,iBAAOuF,WAAW,CAACnF,GAAZ,CAAgBJ,KAAhB,EAAuBG,IAAvB,CAAP;AACA,SARyB;AAS1BE,QAAAA,GAT0B,eASX3C,KATW;AAUzB,cAAMsC,KAAK,GAAG,KAAKvD,WAAL,CAAd;AACA,UAAa6M,eAAe,CAACtJ,KAAD,CAAf;;AAEbuF,UAAAA,WAAW,CAAClF,GAAZ,CAAgBL,KAAhB,EAAuBG,IAAvB,EAA6BzC,KAA7B;AACA;AAdyB,OAA3B;AAgBA;;AACD,WAAO0D,IAAP;AACA;;;AAGD,WAAS+H,gBAAT,CAA0BI,MAA1B;AACC;AACA;AACA;AACA;AACA,SAAK,IAAIrI,CAAC,GAAGqI,MAAM,CAACpI,MAAP,GAAgB,CAA7B,EAAgCD,CAAC,IAAI,CAArC,EAAwCA,CAAC,EAAzC,EAA6C;AAC5C,UAAMlB,KAAK,GAAauJ,MAAM,CAACrI,CAAD,CAAN,CAAUzE,WAAV,CAAxB;;AACA,UAAI,CAACuD,KAAK,CAACiE,SAAX,EAAsB;AACrB,gBAAQjE,KAAK,CAACC,KAAd;AACC;;AAAA;AACC,gBAAIuJ,eAAe,CAACxJ,KAAD,CAAnB,EAA4BmG,WAAW,CAACnG,KAAD,CAAX;AAC5B;;AACD;;AAAA;AACC,gBAAIyJ,gBAAgB,CAACzJ,KAAD,CAApB,EAA6BmG,WAAW,CAACnG,KAAD,CAAX;AAC7B;AANF;AAQA;AACD;AACD;;AAED,WAASkJ,sBAAT,CAAgCQ,MAAhC;AACC,QAAI,CAACA,MAAD,IAAW,OAAOA,MAAP,KAAkB,QAAjC,EAA2C;AAC3C,QAAM1J,KAAK,GAAyB0J,MAAM,CAACjN,WAAD,CAA1C;AACA,QAAI,CAACuD,KAAL,EAAY;QACLnB,QAAmCmB,MAAnCnB;QAAO6F,SAA4B1E,MAA5B0E;QAAQO,YAAoBjF,MAApBiF;QAAWhF,QAASD,MAATC;;AACjC,QAAIA,KAAK;;AAAT,MAAmC;AAClC;AACA;AACA;AACA;AACAR,QAAAA,IAAI,CAACiF,MAAD,EAAS,UAAAnF,GAAG;AACf,cAAKA,GAAW,KAAK9C,WAArB,EAAkC;;AAElC,cAAKoC,KAAa,CAACU,GAAD,CAAb,KAAuBuE,SAAvB,IAAoC,CAAC5D,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAA7C,EAA2D;AAC1D0F,YAAAA,SAAS,CAAC1F,GAAD,CAAT,GAAiB,IAAjB;AACA4G,YAAAA,WAAW,CAACnG,KAAD,CAAX;AACA,WAHD,MAGO,IAAI,CAACiF,SAAS,CAAC1F,GAAD,CAAd,EAAqB;AAC3B;AACA2J,YAAAA,sBAAsB,CAACxE,MAAM,CAACnF,GAAD,CAAP,CAAtB;AACA;AACD,SAVG,CAAJ,CALkC;;AAiBlCE,QAAAA,IAAI,CAACZ,KAAD,EAAQ,UAAAU,GAAG;AACd;AACA,cAAImF,MAAM,CAACnF,GAAD,CAAN,KAAgBuE,SAAhB,IAA6B,CAAC5D,GAAG,CAACwE,MAAD,EAASnF,GAAT,CAArC,EAAoD;AACnD0F,YAAAA,SAAS,CAAC1F,GAAD,CAAT,GAAiB,KAAjB;AACA4G,YAAAA,WAAW,CAACnG,KAAD,CAAX;AACA;AACD,SANG,CAAJ;AAOA,OAxBD,MAwBO,IAAIC,KAAK;;AAAT,MAAkC;AACxC,YAAIuJ,eAAe,CAACxJ,KAAD,CAAnB,EAA6C;AAC5CmG,UAAAA,WAAW,CAACnG,KAAD,CAAX;AACAiF,UAAAA,SAAS,CAAC9D,MAAV,GAAmB,IAAnB;AACA;;AAED,YAAIuD,MAAM,CAACvD,MAAP,GAAgBtC,KAAK,CAACsC,MAA1B,EAAkC;AACjC,eAAK,IAAID,CAAC,GAAGwD,MAAM,CAACvD,MAApB,EAA4BD,CAAC,GAAGrC,KAAK,CAACsC,MAAtC,EAA8CD,CAAC,EAA/C;AAAmD+D,YAAAA,SAAS,CAAC/D,CAAD,CAAT,GAAe,KAAf;AAAnD;AACA,SAFD,MAEO;AACN,eAAK,IAAIA,GAAC,GAAGrC,KAAK,CAACsC,MAAnB,EAA2BD,GAAC,GAAGwD,MAAM,CAACvD,MAAtC,EAA8CD,GAAC,EAA/C;AAAmD+D,YAAAA,SAAS,CAAC/D,GAAD,CAAT,GAAe,IAAf;AAAnD;AACA,SAVuC;;;AAaxC,YAAMyI,GAAG,GAAGC,IAAI,CAACD,GAAL,CAASjF,MAAM,CAACvD,MAAhB,EAAwBtC,KAAK,CAACsC,MAA9B,CAAZ;;AAEA,aAAK,IAAID,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGyI,GAApB,EAAyBzI,GAAC,EAA1B,EAA8B;AAC7B;AACA,cAAI,CAACwD,MAAM,CAACjG,cAAP,CAAsByC,GAAtB,CAAL,EAA+B;AAC9B+D,YAAAA,SAAS,CAAC/D,GAAD,CAAT,GAAe,IAAf;AACA;;AACD,cAAI+D,SAAS,CAAC/D,GAAD,CAAT,KAAiB4C,SAArB,EAAgCoF,sBAAsB,CAACxE,MAAM,CAACxD,GAAD,CAAP,CAAtB;AAChC;AACD;AACD;;AAED,WAASuI,gBAAT,CAA0BzJ,KAA1B;QACQnB,QAAiBmB,MAAjBnB;QAAO6F,SAAU1E,MAAV0E;AAGd;;AACA,QAAM7E,IAAI,GAAGf,OAAO,CAAC4F,MAAD,CAApB;;AACA,SAAK,IAAIxD,CAAC,GAAGrB,IAAI,CAACsB,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;AAC1C,UAAM3B,GAAG,GAAQM,IAAI,CAACqB,CAAD,CAArB;AACA,UAAI3B,GAAG,KAAK9C,WAAZ,EAAyB;AACzB,UAAMoN,SAAS,GAAGhL,KAAK,CAACU,GAAD,CAAvB,CAH0C;;AAK1C,UAAIsK,SAAS,KAAK/F,SAAd,IAA2B,CAAC5D,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAAnC,EAAiD;AAChD,eAAO,IAAP;AACA,OAFD;AAIA;AAJA,WAKK;AACJ,cAAM7B,KAAK,GAAGgH,MAAM,CAACnF,GAAD,CAApB;;AACA,cAAMS,MAAK,GAAetC,KAAK,IAAIA,KAAK,CAACjB,WAAD,CAAxC;;AACA,cAAIuD,MAAK,GAAGA,MAAK,CAACnB,KAAN,KAAgBgL,SAAnB,GAA+B,CAACpJ,EAAE,CAAC/C,KAAD,EAAQmM,SAAR,CAA3C,EAA+D;AAC9D,mBAAO,IAAP;AACA;AACD;AACD;AAGD;;;AACA,QAAMC,WAAW,GAAG,CAAC,CAACjL,KAAK,CAACpC,WAAD,CAA3B;AACA,WAAOoD,IAAI,CAACsB,MAAL,KAAgBrC,OAAO,CAACD,KAAD,CAAP,CAAesC,MAAf,IAAyB2I,WAAW,GAAG,CAAH,GAAO,CAA3C,CAAvB;AACA;;AAED,WAASN,eAAT,CAAyBxJ,KAAzB;QACQ0E,SAAU1E,MAAV0E;AACP,QAAIA,MAAM,CAACvD,MAAP,KAAkBnB,KAAK,CAACnB,KAAN,CAAYsC,MAAlC,EAA0C,OAAO,IAAP;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,QAAM4I,UAAU,GAAG5L,MAAM,CAACqB,wBAAP,CAClBkF,MADkB,EAElBA,MAAM,CAACvD,MAAP,GAAgB,CAFE,CAAnB;;AAKA,QAAI4I,UAAU,IAAI,CAACA,UAAU,CAAC3J,GAA9B,EAAmC,OAAO,IAAP;;AAEnC,SAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwD,MAAM,CAACvD,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACvC,UAAI,CAACwD,MAAM,CAACjG,cAAP,CAAsByC,CAAtB,CAAL,EAA+B,OAAO,IAAP;AAC/B;;;AAED,WAAO,KAAP;AACA;;AAED,WAAS4H,WAAT,CAAqB9I,KAArB;AACC,WAAOA,KAAK,CAACC,KAAN;;AAAA,MACJwJ,gBAAgB,CAACzJ,KAAD,CADZ,GAEJwJ,eAAe,CAACxJ,KAAD,CAFlB;AAGA;;AAED,WAASsJ,eAAT,CAAyBtJ;AAAW;AAApC;AACC,QAAIA,KAAK,CAACyD,QAAV,EAAoBvG,GAAG,CAAC,CAAD,EAAI8M,IAAI,CAACC,SAAL,CAAerJ,MAAM,CAACZ,KAAD,CAArB,CAAJ,CAAH;AACpB;;AAEDkC,EAAAA,UAAU,CAAC,KAAD,EAAQ;AACjBuG,IAAAA,eAAe,EAAfA,eADiB;AAEjBzE,IAAAA,gBAAgB,EAAhBA,gBAFiB;AAGjB8E,IAAAA,WAAW,EAAXA;AAHiB,GAAR,CAAV;AAKA;;SC1PeoB;AACf,MAAMC,OAAO,GAAG,SAAhB;AACA,MAAMC,GAAG,GAAG,KAAZ;AACA,MAAMC,MAAM,GAAG,QAAf;;AAEA,WAASzF,gBAAT,CACC5E,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;AAMC,YAAQ3H,KAAK,CAACC,KAAd;AACC;;AAAA;AACA;;AAAA;AACA;;AAAA;AACC,eAAOsK,2BAA2B,CACjCvK,KADiC,EAEjCsK,QAFiC,EAGjC5C,OAHiC,EAIjCC,cAJiC,CAAlC;;AAMD;;AAAA;AACA;;AAAA;AACC,eAAO6C,oBAAoB,CAACxK,KAAD,EAAQsK,QAAR,EAAkB5C,OAAlB,EAA2BC,cAA3B,CAA3B;;AACD;;AAAA;AACC,eAAO8C,kBAAkB,CACvBzK,KADuB,EAExBsK,QAFwB,EAGxB5C,OAHwB,EAIxBC,cAJwB,CAAzB;AAdF;AAqBA;;AAED,WAAS6C,oBAAT,CACCxK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;QAMM9I,QAAoBmB,MAApBnB;QAAOoG,YAAajF,MAAbiF;AACZ,QAAIpE,KAAK,GAAGb,KAAK,CAACa,KAAlB;;AAGA,QAAIA,KAAK,CAACM,MAAN,GAAetC,KAAK,CAACsC,MAAzB,EAAiC;AAChC,AADgC,iBAEd,CAACN,KAAD,EAAQhC,KAAR,CAFc;AAE9BA,MAAAA,KAF8B;AAEvBgC,MAAAA,KAFuB;AAAA,kBAGH,CAAC8G,cAAD,EAAiBD,OAAjB,CAHG;AAG9BA,MAAAA,OAH8B;AAGrBC,MAAAA,cAHqB;AAIhC;;;AAGD,SAAK,IAAIzG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrC,KAAK,CAACsC,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACtC,UAAI+D,SAAS,CAAC/D,CAAD,CAAT,IAAgBL,KAAK,CAACK,CAAD,CAAL,KAAarC,KAAK,CAACqC,CAAD,CAAtC,EAA2C;AAC1C,YAAMpE,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;AACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;AACZ3L,UAAAA,EAAE,EAAEoN,OADQ;AAEZrN,UAAAA,IAAI,EAAJA,IAFY;AAGZ;AACA;AACAY,UAAAA,KAAK,EAAEgN,uBAAuB,CAAC7J,KAAK,CAACK,CAAD,CAAN;AALlB,SAAb;AAOAyG,QAAAA,cAAc,CAACe,IAAf,CAAoB;AACnB3L,UAAAA,EAAE,EAAEoN,OADe;AAEnBrN,UAAAA,IAAI,EAAJA,IAFmB;AAGnBY,UAAAA,KAAK,EAAEgN,uBAAuB,CAAC7L,KAAK,CAACqC,CAAD,CAAN;AAHX,SAApB;AAKA;AACD;;;AAGD,SAAK,IAAIA,EAAC,GAAGrC,KAAK,CAACsC,MAAnB,EAA2BD,EAAC,GAAGL,KAAK,CAACM,MAArC,EAA6CD,EAAC,EAA9C,EAAkD;AACjD,UAAMpE,KAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,EAAD,CAAhB,CAAb;;AACAwG,MAAAA,OAAO,CAACgB,IAAR,CAAa;AACZ3L,QAAAA,EAAE,EAAEqN,GADQ;AAEZtN,QAAAA,IAAI,EAAJA,KAFY;AAGZ;AACA;AACAY,QAAAA,KAAK,EAAEgN,uBAAuB,CAAC7J,KAAK,CAACK,EAAD,CAAN;AALlB,OAAb;AAOA;;AACD,QAAIrC,KAAK,CAACsC,MAAN,GAAeN,KAAK,CAACM,MAAzB,EAAiC;AAChCwG,MAAAA,cAAc,CAACe,IAAf,CAAoB;AACnB3L,QAAAA,EAAE,EAAEoN,OADe;AAEnBrN,QAAAA,IAAI,EAAEwN,QAAQ,CAACpL,MAAT,CAAgB,CAAC,QAAD,CAAhB,CAFa;AAGnBxB,QAAAA,KAAK,EAAEmB,KAAK,CAACsC;AAHM,OAApB;AAKA;AACD;;;AAGD,WAASoJ,2BAAT,CACCvK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;QAMQ9I,QAAgBmB,MAAhBnB;QAAOgC,QAASb,MAATa;AACdpB,IAAAA,IAAI,CAACO,KAAK,CAACiF,SAAP,EAAmB,UAAC1F,GAAD,EAAMoL,aAAN;AACtB,UAAMC,SAAS,GAAGxK,GAAG,CAACvB,KAAD,EAAQU,GAAR,CAArB;AACA,UAAM7B,KAAK,GAAG0C,GAAG,CAACS,KAAD,EAAStB,GAAT,CAAjB;AACA,UAAMxC,EAAE,GAAG,CAAC4N,aAAD,GAAiBN,MAAjB,GAA0BnK,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAAH,GAAkB4K,OAAlB,GAA4BC,GAAjE;AACA,UAAIQ,SAAS,KAAKlN,KAAd,IAAuBX,EAAE,KAAKoN,OAAlC,EAA2C;AAC3C,UAAMrN,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgBK,GAAhB,CAAb;AACAmI,MAAAA,OAAO,CAACgB,IAAR,CAAa3L,EAAE,KAAKsN,MAAP,GAAgB;AAACtN,QAAAA,EAAE,EAAFA,EAAD;AAAKD,QAAAA,IAAI,EAAJA;AAAL,OAAhB,GAA6B;AAACC,QAAAA,EAAE,EAAFA,EAAD;AAAKD,QAAAA,IAAI,EAAJA,IAAL;AAAWY,QAAAA,KAAK,EAALA;AAAX,OAA1C;AACAiK,MAAAA,cAAc,CAACe,IAAf,CACC3L,EAAE,KAAKqN,GAAP,GACG;AAACrN,QAAAA,EAAE,EAAEsN,MAAL;AAAavN,QAAAA,IAAI,EAAJA;AAAb,OADH,GAEGC,EAAE,KAAKsN,MAAP,GACA;AAACtN,QAAAA,EAAE,EAAEqN,GAAL;AAAUtN,QAAAA,IAAI,EAAJA,IAAV;AAAgBY,QAAAA,KAAK,EAAEgN,uBAAuB,CAACE,SAAD;AAA9C,OADA,GAEA;AAAC7N,QAAAA,EAAE,EAAEoN,OAAL;AAAcrN,QAAAA,IAAI,EAAJA,IAAd;AAAoBY,QAAAA,KAAK,EAAEgN,uBAAuB,CAACE,SAAD;AAAlD,OALJ;AAOA,KAdG,CAAJ;AAeA;;AAED,WAASH,kBAAT,CACCzK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;QAMM9I,QAAgBmB,MAAhBnB;QAAOgC,QAASb,MAATa;AAEZ,QAAIK,CAAC,GAAG,CAAR;AACArC,IAAAA,KAAK,CAACS,OAAN,CAAc,UAAC5B,KAAD;AACb,UAAI,CAACmD,KAAM,CAACX,GAAP,CAAWxC,KAAX,CAAL,EAAwB;AACvB,YAAMZ,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;AACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;AACZ3L,UAAAA,EAAE,EAAEsN,MADQ;AAEZvN,UAAAA,IAAI,EAAJA,IAFY;AAGZY,UAAAA,KAAK,EAALA;AAHY,SAAb;AAKAiK,QAAAA,cAAc,CAACkD,OAAf,CAAuB;AACtB9N,UAAAA,EAAE,EAAEqN,GADkB;AAEtBtN,UAAAA,IAAI,EAAJA,IAFsB;AAGtBY,UAAAA,KAAK,EAALA;AAHsB,SAAvB;AAKA;;AACDwD,MAAAA,CAAC;AACD,KAfD;AAgBAA,IAAAA,CAAC,GAAG,CAAJ;AACAL,IAAAA,KAAM,CAACvB,OAAP,CAAe,UAAC5B,KAAD;AACd,UAAI,CAACmB,KAAK,CAACqB,GAAN,CAAUxC,KAAV,CAAL,EAAuB;AACtB,YAAMZ,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;AACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;AACZ3L,UAAAA,EAAE,EAAEqN,GADQ;AAEZtN,UAAAA,IAAI,EAAJA,IAFY;AAGZY,UAAAA,KAAK,EAALA;AAHY,SAAb;AAKAiK,QAAAA,cAAc,CAACkD,OAAf,CAAuB;AACtB9N,UAAAA,EAAE,EAAEsN,MADkB;AAEtBvN,UAAAA,IAAI,EAAJA,IAFsB;AAGtBY,UAAAA,KAAK,EAALA;AAHsB,SAAvB;AAKA;;AACDwD,MAAAA,CAAC;AACD,KAfD;AAgBA;;AAED,WAASkD,2BAAT,CACCyF,SADD,EAECiB,WAFD,EAGCpD,OAHD,EAICC,cAJD;AAMCD,IAAAA,OAAO,CAACgB,IAAR,CAAa;AACZ3L,MAAAA,EAAE,EAAEoN,OADQ;AAEZrN,MAAAA,IAAI,EAAE,EAFM;AAGZY,MAAAA,KAAK,EAAEoN,WAAW,KAAKxO,OAAhB,GAA0BwH,SAA1B,GAAsCgH;AAHjC,KAAb;AAKAnD,IAAAA,cAAc,CAACe,IAAf,CAAoB;AACnB3L,MAAAA,EAAE,EAAEoN,OADe;AAEnBrN,MAAAA,IAAI,EAAE,EAFa;AAGnBY,MAAAA,KAAK,EAAEmM;AAHY,KAApB;AAKA;;AAED,WAASvB,aAAT,CAA0B/E,KAA1B,EAAoCmE,OAApC;AACCA,IAAAA,OAAO,CAACpI,OAAR,CAAgB,UAAA8I,KAAK;UACbtL,OAAYsL,MAAZtL;UAAMC,KAAMqL,MAANrL;AAEb,UAAIgE,IAAI,GAAQwC,KAAhB;;AACA,WAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpE,IAAI,CAACqE,MAAL,GAAc,CAAlC,EAAqCD,CAAC,EAAtC,EAA0C;AACzC,YAAM6J,UAAU,GAAGnL,WAAW,CAACmB,IAAD,CAA9B;AACA,YAAIwG,CAAC,GAAGzK,IAAI,CAACoE,CAAD,CAAZ;;AACA,YAAI,OAAOqG,CAAP,KAAa,QAAb,IAAyB,OAAOA,CAAP,KAAa,QAA1C,EAAoD;AACnDA,UAAAA,CAAC,GAAG,KAAKA,CAAT;AACA,SALwC;;;AAQzC,YACC,CAACwD,UAAU;;AAAV,WAAkCA,UAAU;;AAA7C,cACCxD,CAAC,KAAK,WAAN,IAAqBA,CAAC,KAAK,aAD5B,CADD,EAICrK,GAAG,CAAC,EAAD,CAAH;AACD,YAAI,OAAO6D,IAAP,KAAgB,UAAhB,IAA8BwG,CAAC,KAAK,WAAxC,EAAqDrK,GAAG,CAAC,EAAD,CAAH;AACrD6D,QAAAA,IAAI,GAAGX,GAAG,CAACW,IAAD,EAAOwG,CAAP,CAAV;AACA,YAAI,OAAOxG,IAAP,KAAgB,QAApB,EAA8B7D,GAAG,CAAC,EAAD,EAAKJ,IAAI,CAACkO,IAAL,CAAU,GAAV,CAAL,CAAH;AAC9B;;AAED,UAAMC,IAAI,GAAGrL,WAAW,CAACmB,IAAD,CAAxB;AACA,UAAMrD,KAAK,GAAGwN,mBAAmB,CAAC9C,KAAK,CAAC1K,KAAP,CAAjC;;AACA,UAAM6B,GAAG,GAAGzC,IAAI,CAACA,IAAI,CAACqE,MAAL,GAAc,CAAf,CAAhB;;AACA,cAAQpE,EAAR;AACC,aAAKoN,OAAL;AACC,kBAAQc,IAAR;AACC;;AAAA;AACC,qBAAOlK,IAAI,CAACV,GAAL,CAASd,GAAT,EAAc7B,KAAd,CAAP;;AACD;;AACA;;AAAA;AACCR,cAAAA,GAAG,CAAC,EAAD,CAAH;;AACD;AACC;AACA;AACA;AACA;AACA,qBAAQ6D,IAAI,CAACxB,GAAD,CAAJ,GAAY7B,KAApB;AAXF;;AAaD,aAAK0M,GAAL;AACC,kBAAQa,IAAR;AACC;;AAAA;AACC,qBAAO1L,GAAG,KAAK,GAAR,GACJwB,IAAI,CAAC2H,IAAL,CAAUhL,KAAV,CADI,GAEJqD,IAAI,CAACoK,MAAL,CAAY5L,GAAZ,EAAwB,CAAxB,EAA2B7B,KAA3B,CAFH;;AAGD;;AAAA;AACC,qBAAOqD,IAAI,CAACV,GAAL,CAASd,GAAT,EAAc7B,KAAd,CAAP;;AACD;;AAAA;AACC,qBAAOqD,IAAI,CAACP,GAAL,CAAS9C,KAAT,CAAP;;AACD;AACC,qBAAQqD,IAAI,CAACxB,GAAD,CAAJ,GAAY7B,KAApB;AAVF;;AAYD,aAAK2M,MAAL;AACC,kBAAQY,IAAR;AACC;;AAAA;AACC,qBAAOlK,IAAI,CAACoK,MAAL,CAAY5L,GAAZ,EAAwB,CAAxB,CAAP;;AACD;;AAAA;AACC,qBAAOwB,IAAI,CAACc,MAAL,CAAYtC,GAAZ,CAAP;;AACD;;AAAA;AACC,qBAAOwB,IAAI,CAACc,MAAL,CAAYuG,KAAK,CAAC1K,KAAlB,CAAP;;AACD;AACC,qBAAO,OAAOqD,IAAI,CAACxB,GAAD,CAAlB;AARF;;AAUD;AACCrC,UAAAA,GAAG,CAAC,EAAD,EAAKH,EAAL,CAAH;AAxCF;AA0CA,KAnED;AAqEA,WAAOwG,KAAP;AACA;;AAMD,WAAS2H,mBAAT,CAA6BlM,GAA7B;AACC,QAAI,CAACrB,WAAW,CAACqB,GAAD,CAAhB,EAAuB,OAAOA,GAAP;AACvB,QAAInB,KAAK,CAACC,OAAN,CAAckB,GAAd,CAAJ,EAAwB,OAAOA,GAAG,CAACoM,GAAJ,CAAQF,mBAAR,CAAP;AACxB,QAAIlN,KAAK,CAACgB,GAAD,CAAT,EACC,OAAO,IAAIjD,GAAJ,CACN8B,KAAK,CAACmL,IAAN,CAAWhK,GAAG,CAACqM,OAAJ,EAAX,EAA0BD,GAA1B,CAA8B;AAAA,UAAEE,CAAF;AAAA,UAAKC,CAAL;AAAA,aAAY,CAACD,CAAD,EAAIJ,mBAAmB,CAACK,CAAD,CAAvB,CAAZ;AAAA,KAA9B,CADM,CAAP;AAGD,QAAItN,KAAK,CAACe,GAAD,CAAT,EAAgB,OAAO,IAAI/C,GAAJ,CAAQ4B,KAAK,CAACmL,IAAN,CAAWhK,GAAX,EAAgBoM,GAAhB,CAAoBF,mBAApB,CAAR,CAAP;AAChB,QAAMM,MAAM,GAAGrN,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBS,GAAtB,CAAd,CAAf;;AACA,SAAK,IAAMO,GAAX,IAAkBP,GAAlB;AAAuBwM,MAAAA,MAAM,CAACjM,GAAD,CAAN,GAAc2L,mBAAmB,CAAClM,GAAG,CAACO,GAAD,CAAJ,CAAjC;AAAvB;;AACA,QAAIW,GAAG,CAAClB,GAAD,EAAMyM,SAAN,CAAP,EAAyBD,MAAM,CAACC,SAAD,CAAN,GAAoBzM,GAAG,CAACyM,SAAD,CAAvB;AACzB,WAAOD,MAAP;AACA;;AAED,WAASd,uBAAT,CAAoC1L,GAApC;AACC,QAAIvB,OAAO,CAACuB,GAAD,CAAX,EAAkB;AACjB,aAAOkM,mBAAmB,CAAClM,GAAD,CAA1B;AACA,KAFD,MAEO,OAAOA,GAAP;AACP;;AAEDkD,EAAAA,UAAU,CAAC,SAAD,EAAY;AACrBoG,IAAAA,aAAa,EAAbA,aADqB;AAErB1D,IAAAA,gBAAgB,EAAhBA,gBAFqB;AAGrBR,IAAAA,2BAA2B,EAA3BA;AAHqB,GAAZ,CAAV;AAKA;;AChTD;AACA,SAmBgBsH;AACf;AACA,MAAIC,cAAa,GAAG,uBAASC,CAAT,EAAiBC,CAAjB;AACnBF,IAAAA,cAAa,GACZxN,MAAM,CAACsI,cAAP,IACC;AAACqF,MAAAA,SAAS,EAAE;AAAZ,iBAA2BjO,KAA3B,IACA,UAAS+N,CAAT,EAAYC,CAAZ;AACCD,MAAAA,CAAC,CAACE,SAAF,GAAcD,CAAd;AACA,KAJF,IAKA,UAASD,CAAT,EAAYC,CAAZ;AACC,WAAK,IAAItE,CAAT,IAAcsE,CAAd;AAAiB,YAAIA,CAAC,CAACpN,cAAF,CAAiB8I,CAAjB,CAAJ,EAAyBqE,CAAC,CAACrE,CAAD,CAAD,GAAOsE,CAAC,CAACtE,CAAD,CAAR;AAA1C;AACA,KARF;;AASA,WAAOoE,cAAa,CAACC,CAAD,EAAIC,CAAJ,CAApB;AACA,GAXD;;;AAcA,WAASE,SAAT,CAAmBH,CAAnB,EAA2BC,CAA3B;AACCF,IAAAA,cAAa,CAACC,CAAD,EAAIC,CAAJ,CAAb;;AACA,aAASG,EAAT;AACC,WAAKjO,WAAL,GAAmB6N,CAAnB;AACA;;AACDA,IAAAA,CAAC,CAACxN,SAAF;AAEG4N,IAAAA,EAAE,CAAC5N,SAAH,GAAeyN,CAAC,CAACzN,SAAlB,EAA8B,IAAI4N,EAAJ,EAFhC;AAGA;;AAED,MAAMC,QAAQ,GAAI,UAASC,MAAT;AACjBH,IAAAA,SAAS,CAACE,QAAD,EAAWC,MAAX,CAAT;;;AAEA,aAASD,QAAT,CAA6B7M,MAA7B,EAA6CgG,MAA7C;AACC,WAAK3I,WAAL,IAAoB;AACnBwD,QAAAA,KAAK;;AADc;AAEnBsC,QAAAA,OAAO,EAAE6C,MAFU;AAGnBZ,QAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAH7B;AAInB4B,QAAAA,SAAS,EAAE,KAJQ;AAKnBQ,QAAAA,UAAU,EAAE,KALO;AAMnB5D,QAAAA,KAAK,EAAEiD,SANY;AAOnBmB,QAAAA,SAAS,EAAEnB,SAPQ;AAQnBjF,QAAAA,KAAK,EAAEO,MARY;AASnBsF,QAAAA,MAAM,EAAE,IATW;AAUnBW,QAAAA,SAAS,EAAE,KAVQ;AAWnB5B,QAAAA,QAAQ,EAAE;AAXS,OAApB;AAaA,aAAO,IAAP;AACA;;AACD,QAAM8D,CAAC,GAAG0E,QAAQ,CAAC7N,SAAnB;AAEAD,IAAAA,MAAM,CAACqI,cAAP,CAAsBe,CAAtB,EAAyB,MAAzB,EAAiC;AAChCnH,MAAAA,GAAG,EAAE;AACJ,eAAOQ,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0B0P,IAAjC;AACA,OAH+B;AAKhC;;AALgC,KAAjC;;AAQA5E,IAAAA,CAAC,CAACrH,GAAF,GAAQ,UAASX,GAAT;AACP,aAAOqB,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0ByD,GAA1B,CAA8BX,GAA9B,CAAP;AACA,KAFD;;AAIAgI,IAAAA,CAAC,CAAClH,GAAF,GAAQ,UAASd,GAAT,EAAmB7B,KAAnB;AACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;AACA,UAAI,CAACY,MAAM,CAACZ,KAAD,CAAN,CAAcE,GAAd,CAAkBX,GAAlB,CAAD,IAA2BqB,MAAM,CAACZ,KAAD,CAAN,CAAcI,GAAd,CAAkBb,GAAlB,MAA2B7B,KAA1D,EAAiE;AAChE0O,QAAAA,cAAc,CAACpM,KAAD,CAAd;AACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;AACAA,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,IAA1B;AACAS,QAAAA,KAAK,CAACa,KAAN,CAAaR,GAAb,CAAiBd,GAAjB,EAAsB7B,KAAtB;AACAsC,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,IAA1B;AACA;;AACD,aAAO,IAAP;AACA,KAXD;;AAaAgI,IAAAA,CAAC,CAAC1F,MAAF,GAAW,UAAStC,GAAT;AACV,UAAI,CAAC,KAAKW,GAAL,CAASX,GAAT,CAAL,EAAoB;AACnB,eAAO,KAAP;AACA;;AAED,UAAMS,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;AACAoM,MAAAA,cAAc,CAACpM,KAAD,CAAd;AACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;;AACA,UAAIA,KAAK,CAACnB,KAAN,CAAYqB,GAAZ,CAAgBX,GAAhB,CAAJ,EAA0B;AACzBS,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,KAA1B;AACA,OAFD,MAEO;AACNS,QAAAA,KAAK,CAACiF,SAAN,CAAiBpD,MAAjB,CAAwBtC,GAAxB;AACA;;AACDS,MAAAA,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoBtC,GAApB;AACA,aAAO,IAAP;AACA,KAhBD;;AAkBAgI,IAAAA,CAAC,CAAC3F,KAAF,GAAU;AACT,UAAM5B,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;AACA,UAAIY,MAAM,CAACZ,KAAD,CAAN,CAAcmM,IAAlB,EAAwB;AACvBC,QAAAA,cAAc,CAACpM,KAAD,CAAd;AACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;AACAA,QAAAA,KAAK,CAACiF,SAAN,GAAkB,IAAIlJ,GAAJ,EAAlB;AACA0D,QAAAA,IAAI,CAACO,KAAK,CAACnB,KAAP,EAAc,UAAAU,GAAG;AACpBS,UAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,KAA1B;AACA,SAFG,CAAJ;AAGAS,QAAAA,KAAK,CAACa,KAAN,CAAae,KAAb;AACA;AACD,KAZD;;AAcA2F,IAAAA,CAAC,CAACjI,OAAF,GAAY,UACX+M,EADW,EAEXC,OAFW;;;AAIX,UAAMtM,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACAmE,MAAAA,MAAM,CAACZ,KAAD,CAAN,CAAcV,OAAd,CAAsB,UAACiN,MAAD,EAAchN,GAAd,EAAwBiN,IAAxB;AACrBH,QAAAA,EAAE,CAAC3N,IAAH,CAAQ4N,OAAR,EAAiB,KAAI,CAAClM,GAAL,CAASb,GAAT,CAAjB,EAAgCA,GAAhC,EAAqC,KAArC;AACA,OAFD;AAGA,KARD;;AAUAgI,IAAAA,CAAC,CAACnH,GAAF,GAAQ,UAASb,GAAT;AACP,UAAMS,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;AACA,UAAMtC,KAAK,GAAGkD,MAAM,CAACZ,KAAD,CAAN,CAAcI,GAAd,CAAkBb,GAAlB,CAAd;;AACA,UAAIS,KAAK,CAACyE,UAAN,IAAoB,CAAC9G,WAAW,CAACD,KAAD,CAApC,EAA6C;AAC5C,eAAOA,KAAP;AACA;;AACD,UAAIA,KAAK,KAAKsC,KAAK,CAACnB,KAAN,CAAYuB,GAAZ,CAAgBb,GAAhB,CAAd,EAAoC;AACnC,eAAO7B,KAAP,CADmC;AAEnC;;;AAED,UAAM6F,KAAK,GAAGwC,WAAW,CAAC/F,KAAK,CAACwE,MAAN,CAAahC,MAAd,EAAsB9E,KAAtB,EAA6BsC,KAA7B,CAAzB;AACAoM,MAAAA,cAAc,CAACpM,KAAD,CAAd;AACAA,MAAAA,KAAK,CAACa,KAAN,CAAaR,GAAb,CAAiBd,GAAjB,EAAsBgE,KAAtB;AACA,aAAOA,KAAP;AACA,KAfD;;AAiBAgE,IAAAA,CAAC,CAAC1H,IAAF,GAAS;AACR,aAAOe,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0BoD,IAA1B,EAAP;AACA,KAFD;;AAIA0H,IAAAA,CAAC,CAACkF,MAAF,GAAW;;;;AACV,UAAM9P,QAAQ,GAAG,KAAKkD,IAAL,EAAjB;AACA,6BACEnD,cADF,IACmB;AAAA,eAAM,MAAI,CAAC+P,MAAL,EAAN;AAAA,OADnB,OAECC,IAFD,GAEO;AACL,YAAMC,CAAC,GAAGhQ,QAAQ,CAAC+P,IAAT,EAAV;AACA;;AACA,YAAIC,CAAC,CAACC,IAAN,EAAY,OAAOD,CAAP;;AACZ,YAAMjP,KAAK,GAAG,MAAI,CAAC0C,GAAL,CAASuM,CAAC,CAACjP,KAAX,CAAd;;AACA,eAAO;AACNkP,UAAAA,IAAI,EAAE,KADA;AAENlP,UAAAA,KAAK,EAALA;AAFM,SAAP;AAIA,OAXF;AAaA,KAfD;;AAiBA6J,IAAAA,CAAC,CAAC8D,OAAF,GAAY;;;;AACX,UAAM1O,QAAQ,GAAG,KAAKkD,IAAL,EAAjB;AACA,+BACEnD,cADF,IACmB;AAAA,eAAM,MAAI,CAAC2O,OAAL,EAAN;AAAA,OADnB,QAECqB,IAFD,GAEO;AACL,YAAMC,CAAC,GAAGhQ,QAAQ,CAAC+P,IAAT,EAAV;AACA;;AACA,YAAIC,CAAC,CAACC,IAAN,EAAY,OAAOD,CAAP;;AACZ,YAAMjP,KAAK,GAAG,MAAI,CAAC0C,GAAL,CAASuM,CAAC,CAACjP,KAAX,CAAd;;AACA,eAAO;AACNkP,UAAAA,IAAI,EAAE,KADA;AAENlP,UAAAA,KAAK,EAAE,CAACiP,CAAC,CAACjP,KAAH,EAAUA,KAAV;AAFD,SAAP;AAIA,OAXF;AAaA,KAfD;;AAiBA6J,IAAAA,CAAC,CAAC7K,cAAD,CAAD,GAAoB;AACnB,aAAO,KAAK2O,OAAL,EAAP;AACA,KAFD;;AAIA,WAAOY,QAAP;AACA,GApJgB,CAoJdlQ,GApJc,CAAjB;;AAsJA,WAASwM,SAAT,CAAqCnJ,MAArC,EAAgDgG,MAAhD;AACC;AACA,WAAO,IAAI6G,QAAJ,CAAa7M,MAAb,EAAqBgG,MAArB,CAAP;AACA;;AAED,WAASgH,cAAT,CAAwBpM,KAAxB;AACC,QAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;AACjBb,MAAAA,KAAK,CAACiF,SAAN,GAAkB,IAAIlJ,GAAJ,EAAlB;AACAiE,MAAAA,KAAK,CAACa,KAAN,GAAc,IAAI9E,GAAJ,CAAQiE,KAAK,CAACnB,KAAd,CAAd;AACA;AACD;;AAED,MAAMgO,QAAQ,GAAI,UAASX,MAAT;AACjBH,IAAAA,SAAS,CAACc,QAAD,EAAWX,MAAX,CAAT;;;AAEA,aAASW,QAAT,CAA6BzN,MAA7B,EAA6CgG,MAA7C;AACC,WAAK3I,WAAL,IAAoB;AACnBwD,QAAAA,KAAK;;AADc;AAEnBsC,QAAAA,OAAO,EAAE6C,MAFU;AAGnBZ,QAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAH7B;AAInB4B,QAAAA,SAAS,EAAE,KAJQ;AAKnBQ,QAAAA,UAAU,EAAE,KALO;AAMnB5D,QAAAA,KAAK,EAAEiD,SANY;AAOnBjF,QAAAA,KAAK,EAAEO,MAPY;AAQnBsF,QAAAA,MAAM,EAAE,IARW;AASnBjC,QAAAA,OAAO,EAAE,IAAI1G,GAAJ,EATU;AAUnB0H,QAAAA,QAAQ,EAAE,KAVS;AAWnB4B,QAAAA,SAAS,EAAE;AAXQ,OAApB;AAaA,aAAO,IAAP;AACA;;AACD,QAAMkC,CAAC,GAAGsF,QAAQ,CAACzO,SAAnB;AAEAD,IAAAA,MAAM,CAACqI,cAAP,CAAsBe,CAAtB,EAAyB,MAAzB,EAAiC;AAChCnH,MAAAA,GAAG,EAAE;AACJ,eAAOQ,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0B0P,IAAjC;AACA,OAH+B;;AAAA,KAAjC;;AAOA5E,IAAAA,CAAC,CAACrH,GAAF,GAAQ,UAASxC,KAAT;AACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;AAEA,UAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;AACjB,eAAOb,KAAK,CAACnB,KAAN,CAAYqB,GAAZ,CAAgBxC,KAAhB,CAAP;AACA;;AACD,UAAIsC,KAAK,CAACa,KAAN,CAAYX,GAAZ,CAAgBxC,KAAhB,CAAJ,EAA4B,OAAO,IAAP;AAC5B,UAAIsC,KAAK,CAACyC,OAAN,CAAcvC,GAAd,CAAkBxC,KAAlB,KAA4BsC,KAAK,CAACa,KAAN,CAAYX,GAAZ,CAAgBF,KAAK,CAACyC,OAAN,CAAcrC,GAAd,CAAkB1C,KAAlB,CAAhB,CAAhC,EACC,OAAO,IAAP;AACD,aAAO,KAAP;AACA,KAXD;;AAaA6J,IAAAA,CAAC,CAAC/G,GAAF,GAAQ,UAAS9C,KAAT;AACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;AACA,UAAI,CAAC,KAAKE,GAAL,CAASxC,KAAT,CAAL,EAAsB;AACrBoP,QAAAA,cAAc,CAAC9M,KAAD,CAAd;AACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;AACAA,QAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB9C,KAAjB;AACA;;AACD,aAAO,IAAP;AACA,KATD;;AAWA6J,IAAAA,CAAC,CAAC1F,MAAF,GAAW,UAASnE,KAAT;AACV,UAAI,CAAC,KAAKwC,GAAL,CAASxC,KAAT,CAAL,EAAsB;AACrB,eAAO,KAAP;AACA;;AAED,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;AACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;AACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;AACA,aACCA,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoBnE,KAApB,MACCsC,KAAK,CAACyC,OAAN,CAAcvC,GAAd,CAAkBxC,KAAlB,IACEsC,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoB7B,KAAK,CAACyC,OAAN,CAAcrC,GAAd,CAAkB1C,KAAlB,CAApB,CADF;AAEE;AAA2B,WAH9B,CADD;AAMA,KAfD;;AAiBA6J,IAAAA,CAAC,CAAC3F,KAAF,GAAU;AACT,UAAM5B,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;AACA,UAAIY,MAAM,CAACZ,KAAD,CAAN,CAAcmM,IAAlB,EAAwB;AACvBW,QAAAA,cAAc,CAAC9M,KAAD,CAAd;AACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;AACAA,QAAAA,KAAK,CAACa,KAAN,CAAae,KAAb;AACA;AACD,KARD;;AAUA2F,IAAAA,CAAC,CAACkF,MAAF,GAAW;AACV,UAAMzM,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;AACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;AACA,aAAOA,KAAK,CAACa,KAAN,CAAa4L,MAAb,EAAP;AACA,KALD;;AAOAlF,IAAAA,CAAC,CAAC8D,OAAF,GAAY,SAASA,OAAT;AACX,UAAMrL,KAAK,GAAa,KAAKvD,WAAL,CAAxB;AACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;AACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;AACA,aAAOA,KAAK,CAACa,KAAN,CAAawK,OAAb,EAAP;AACA,KALD;;AAOA9D,IAAAA,CAAC,CAAC1H,IAAF,GAAS;AACR,aAAO,KAAK4M,MAAL,EAAP;AACA,KAFD;;AAIAlF,IAAAA,CAAC,CAAC7K,cAAD,CAAD,GAAoB;AACnB,aAAO,KAAK+P,MAAL,EAAP;AACA,KAFD;;AAIAlF,IAAAA,CAAC,CAACjI,OAAF,GAAY,SAASA,OAAT,CAAiB+M,EAAjB,EAA0BC,OAA1B;AACX,UAAM3P,QAAQ,GAAG,KAAK8P,MAAL,EAAjB;AACA,UAAI9I,MAAM,GAAGhH,QAAQ,CAAC+P,IAAT,EAAb;;AACA,aAAO,CAAC/I,MAAM,CAACiJ,IAAf,EAAqB;AACpBP,QAAAA,EAAE,CAAC3N,IAAH,CAAQ4N,OAAR,EAAiB3I,MAAM,CAACjG,KAAxB,EAA+BiG,MAAM,CAACjG,KAAtC,EAA6C,IAA7C;AACAiG,QAAAA,MAAM,GAAGhH,QAAQ,CAAC+P,IAAT,EAAT;AACA;AACD,KAPD;;AASA,WAAOG,QAAP;AACA,GA/GgB,CA+Gd5Q,GA/Gc,CAAjB;;AAiHA,WAASuM,SAAT,CAAqCpJ,MAArC,EAAgDgG,MAAhD;AACC;AACA,WAAO,IAAIyH,QAAJ,CAAazN,MAAb,EAAqBgG,MAArB,CAAP;AACA;;AAED,WAAS0H,cAAT,CAAwB9M,KAAxB;AACC,QAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;AACjB;AACAb,MAAAA,KAAK,CAACa,KAAN,GAAc,IAAI5E,GAAJ,EAAd;AACA+D,MAAAA,KAAK,CAACnB,KAAN,CAAYS,OAAZ,CAAoB,UAAA5B,KAAK;AACxB,YAAIC,WAAW,CAACD,KAAD,CAAf,EAAwB;AACvB,cAAM6F,KAAK,GAAGwC,WAAW,CAAC/F,KAAK,CAACwE,MAAN,CAAahC,MAAd,EAAsB9E,KAAtB,EAA6BsC,KAA7B,CAAzB;AACAA,UAAAA,KAAK,CAACyC,OAAN,CAAcpC,GAAd,CAAkB3C,KAAlB,EAAyB6F,KAAzB;AACAvD,UAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB+C,KAAjB;AACA,SAJD,MAIO;AACNvD,UAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB9C,KAAjB;AACA;AACD,OARD;AASA;AACD;;AAED,WAAS4L,eAAT,CAAyBtJ;AAAW;AAApC;AACC,QAAIA,KAAK,CAACyD,QAAV,EAAoBvG,GAAG,CAAC,CAAD,EAAI8M,IAAI,CAACC,SAAL,CAAerJ,MAAM,CAACZ,KAAD,CAArB,CAAJ,CAAH;AACpB;;AAEDkC,EAAAA,UAAU,CAAC,QAAD,EAAW;AAACqG,IAAAA,SAAS,EAATA,SAAD;AAAYC,IAAAA,SAAS,EAATA;AAAZ,GAAX,CAAV;AACA;;SCvVeuE;AACf9D,EAAAA,SAAS;AACTyC,EAAAA,YAAY;AACZxB,EAAAA,aAAa;AACb;;ACcD,IAAM5G,KAAK;AAAA;AAAG,IAAIuD,KAAJ,EAAd;AAEA;;;;;;;;;;;;;;;;;;;;AAmBA,IAAaM,OAAO,GAAa7D,KAAK,CAAC6D,OAAhC;AACP,AAEA;;;;;AAIA,IAAaM,kBAAkB;AAAA;AAAwBnE,KAAK,CAACmE,kBAAN,CAAyBuF,IAAzB,CACtD1J,KADsD,CAAhD;AAIP;;;;;;AAKA,IAAa0E,aAAa;AAAA;AAAG1E,KAAK,CAAC0E,aAAN,CAAoBgF,IAApB,CAAyB1J,KAAzB,CAAtB;AAEP;;;;;;;AAMA,IAAawE,aAAa;AAAA;AAAGxE,KAAK,CAACwE,aAAN,CAAoBkF,IAApB,CAAyB1J,KAAzB,CAAtB;AAEP;;;;;;AAKA,IAAa6E,YAAY;AAAA;AAAG7E,KAAK,CAAC6E,YAAN,CAAmB6E,IAAnB,CAAwB1J,KAAxB,CAArB;AAEP;;;;;AAIA,IAAa2E,WAAW;AAAA;AAAG3E,KAAK,CAAC2E,WAAN,CAAkB+E,IAAlB,CAAuB1J,KAAvB,CAApB;AAEP;;;;;;;;;AAQA,IAAa4E,WAAW;AAAA;AAAG5E,KAAK,CAAC4E,WAAN,CAAkB8E,IAAlB,CAAuB1J,KAAvB,CAApB;AAEP;;;;;;;AAMA,SAAgB2J,UAAavP;AAC5B,SAAOA,KAAP;AACA;AAED;;;;;;AAKA,SAAgBwP,cAAiBxP;AAChC,SAAOA,KAAP;AACA;;;;;;;;;;;;;;;;;;;;;;;;;"}