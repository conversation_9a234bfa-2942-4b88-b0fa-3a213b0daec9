# Change Log

## v0.5.1 / 2021-03-22
### Fixed
- encode/devoce empty string

## v0.5.0 / 2018-04-05
### Changed
- Throw error object when invalid UTF8. #6
- Throw error when invalid base32 characters. #8

## v0.4.0 / 2017-08-16
### Fixed
- TypeScript bug. #4

## v0.3.0 / 2017-08-16
### Added
- TypeScript support.

## v0.2.0 / 2017-01-23
### Added
- AMD support.

### Fixed
- ArrayBuffer dosen't work in Webpack.

## v0.1.1 / 2015-03-15
### Added
- decode.asBytes method.
- support byte Array, Uint8Array and ArrayBuffer input.

## v0.1.0 / 2015-03-14
### Added
- create project.
