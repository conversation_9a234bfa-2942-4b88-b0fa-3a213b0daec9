{"version": 3, "file": "index.mjs", "names": ["computeAccessibleDescription", "computeAccessibleName", "default", "getRole"], "sources": ["../sources/index.ts"], "sourcesContent": ["export { computeAccessibleDescription } from \"./accessible-description\";\nexport { computeAccessibleName } from \"./accessible-name\";\nexport { default as getRole } from \"./getRole\";\nexport * from \"./is-inaccessible\";\n"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,8BAA0B;AACvE,SAASC,qBAAqB,QAAQ,uBAAmB;AACzD,SAASC,OAAO,IAAIC,OAAO,QAAQ,eAAW;AAC9C,cAAc,uBAAmB"}