{"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.2", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://ariya.ofilabs.com"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}}