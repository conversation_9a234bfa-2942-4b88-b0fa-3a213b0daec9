"use strict";const e=/^media$/i,r=/\(\s*prefers-color-scheme\s*:\s*(dark|light|no-preference)\s*\)/i,o={dark:48,light:70,"no-preference":22},s=(e,r)=>`(color-index: ${o[r.toLowerCase()]})`,c={dark:48842621,light:70318723,"no-preference":22511989},t=(e,r)=>`(color: ${c[r.toLowerCase()]})`,l=o=>{const c=!("preserve"in Object(o))||o.preserve,l={};return!("mediaQuery"in Object(o))||"color-index"!==o.mediaQuery&&"color"!==o.mediaQuery?(l["color-index"]=!0,l.color=!0):l[o.mediaQuery]=!0,{postcssPlugin:"postcss-prefers-color-scheme",AtRule:o=>{if(!e.test(o.name))return;const{params:n}=o,a=n.replace(r,s),i=n.replace(r,t);let p=!1;n!==a&&l["color-index"]&&(o.cloneBefore({params:a}),p=!0),n!==i&&l.color&&(o.cloneBefore({params:i}),p=!0),!c&&p&&o.remove()}}};l.postcss=!0,module.exports=l;
