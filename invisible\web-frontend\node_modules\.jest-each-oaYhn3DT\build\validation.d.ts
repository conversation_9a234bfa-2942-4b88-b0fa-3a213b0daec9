/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import type { Global } from '@jest/types';
export declare const validateArrayTable: (table: unknown) => void;
export declare const validateTemplateTableArguments: (headings: Array<string>, data: Global.TemplateData) => void;
export declare const extractValidTemplateHeadings: (headings: string) => string;
