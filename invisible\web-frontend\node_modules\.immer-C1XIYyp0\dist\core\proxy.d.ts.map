{"version": 3, "file": "proxy.d.ts", "sourceRoot": "", "sources": ["../src/core/proxy.ts"], "names": [], "mappings": "AAAA,OAAO,EAON,cAAc,EACd,UAAU,EACV,OAAO,EACP,SAAS,EACT,QAAQ,EACR,SAAS,EAKT,SAAS,EACT,MAAM,aAAa,CAAA;AAEpB,UAAU,cAAe,SAAQ,cAAc;IAC9C,SAAS,EAAE;QACV,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAA;KAC3B,CAAA;IACD,OAAO,CAAC,EAAE,UAAU,CAAA;IACpB,OAAO,IAAI,IAAI,CAAA;CACf;AAED,MAAM,WAAW,gBAAiB,SAAQ,cAAc;IACvD,KAAK,EAAE,SAAS,CAAC,WAAW,CAAA;IAC5B,KAAK,EAAE,GAAG,CAAA;IACV,KAAK,EAAE,GAAG,CAAA;IACV,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAA;CAC5C;AAED,MAAM,WAAW,eAAgB,SAAQ,cAAc;IACtD,KAAK,EAAE,SAAS,CAAC,UAAU,CAAA;IAC3B,KAAK,EAAE,QAAQ,CAAA;IACf,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;IACtB,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAA;CAC1C;AAED,aAAK,UAAU,GAAG,gBAAgB,GAAG,eAAe,CAAA;AAEpD;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,SAAS,EACnD,IAAI,EAAE,CAAC,EACP,MAAM,CAAC,EAAE,UAAU,GACjB,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,CA0CxB;AAED;;GAEG;AACH,eAAO,MAAM,WAAW,EAAE,YAAY,CAAC,UAAU,CA8GhD,CAAA;AAyDD,wBAAgB,WAAW,CAAC,KAAK,EAAE,UAAU,QAO5C;AAED,wBAAgB,WAAW,CAAC,KAAK,EAAE;IAAC,KAAK,EAAE,GAAG,CAAC;IAAC,KAAK,EAAE,GAAG,CAAA;CAAC,QAI1D"}