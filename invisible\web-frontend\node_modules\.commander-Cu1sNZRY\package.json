{"name": "commander", "version": "8.3.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "typescript-lint": "eslint typings/*.ts tests/*.ts", "test": "jest && npm run test-typings", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm"}, "main": "./index.js", "files": ["index.js", "lib/*.js", "esm.mjs", "typings/index.d.ts", "package-support.json"], "type": "commonjs", "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.23", "@types/node": "^14.17.3", "@typescript-eslint/eslint-plugin": "^4.27.0", "@typescript-eslint/parser": "^4.27.0", "eslint": "^7.29.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-jest": "^24.3.6", "jest": "^27.0.4", "standard": "^16.0.3", "ts-jest": "^27.0.3", "tsd": "^0.17.0", "typescript": "^4.3.4"}, "types": "typings/index.d.ts", "jest": {"testEnvironment": "node", "collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testPathIgnorePatterns": ["/node_modules/"]}, "engines": {"node": ">= 12"}, "support": true}