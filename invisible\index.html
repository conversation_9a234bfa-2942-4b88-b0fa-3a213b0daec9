<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Invisible Chat</title>

    <!-- 📚 CHATGPT-STYLE FORMATTING LIBRARIES -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 10px;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            -webkit-app-region: drag;
            /* Make the entire window draggable */
        }

        /* Make interactive elements non-draggable */
        input,
        button,
        textarea,
        select,
        #chat-container,
        .mode-switch-buttons {
            -webkit-app-region: no-drag;
        }

        #chat-container {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 10px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
            max-width: 80%;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .user-message {
            background-color: #4a5568;
            align-self: flex-end;
            margin-left: auto;
            border-bottom-right-radius: 0;
        }

        .bot-message {
            background-color: black;
            align-self: flex-start;
            border-bottom-left-radius: 0;
        }

        #input-container {
            display: flex;
            margin-top: 10px;
        }

        #message-input {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        #send-button {
            margin-left: 10px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: #4299e1;
            color: white;
            cursor: pointer;
        }

        #controls {
            margin-top: 10px;
            font-size: 12px;
            color: #a0aec0;
            text-align: center;
        }

        /* Drag area indicator */
        .drag-area {
            -webkit-app-region: drag;
            cursor: move;
        }

        /* Make sure the entire body is draggable */
        body::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            -webkit-app-region: drag;
            pointer-events: none;
            z-index: -1;
        }

        .typing-indicator {
            display: inline-block;
            padding: 8px 12px;
            background-color: #2d3748;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #a0aec0;
            border-radius: 50%;
            margin-right: 5px;
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
            margin-right: 0;
        }

        @keyframes typing {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-5px);
            }
        }

        pre {
            background-color: #1a202c;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre;
            font-family: 'Courier New', Consolas, monospace;
            line-height: 1.4;
        }

        code {
            font-family: monospace;
            color: #90cdf4;
        }

        #settings-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #a0aec0;
            cursor: pointer;
            font-size: 16px;
        }

        #settings-modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #2d3748;
            padding: 20px;
            border-radius: 10px;
            z-index: 100;
            width: 300px;
        }

        #settings-modal h2 {
            margin-top: 0;
        }

        #api-key-input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 5px;
            color: white;
        }

        #save-settings {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            background-color: #4299e1;
            color: white;
            cursor: pointer;
        }

        #modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 99;
        }

        /* Mode Selection Styles */
        #mode-selection {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #2d3748;
            padding: 30px;
            border-radius: 15px;
            z-index: 200;
            text-align: center;
            min-width: 400px;
        }

        #mode-selection h2 {
            margin-top: 0;
            color: #90cdf4;
            margin-bottom: 20px;
        }

        .mode-button {
            display: block;
            width: 100%;
            padding: 15px 20px;
            margin: 10px 0;
            border: none;
            border-radius: 8px;
            background-color: #4299e1;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .mode-button:hover {
            background-color: #3182ce;
        }

        .mode-button.dsa {
            background-color: #38a169;
        }

        .mode-button.dsa:hover {
            background-color: #2f855a;
        }

        .mode-description {
            font-size: 14px;
            color: #a0aec0;
            margin-bottom: 15px;
        }

        #current-mode {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #90cdf4;
        }

        /* Mode Switch Buttons */
        .mode-switch-buttons {
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            gap: 5px;
            z-index: 150;
        }

        .mode-switch-btn {
            background-color: black;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-weight: normal;
        }

        .mode-switch-btn:hover {
            background-color: #333;
        }

        .mode-switch-btn.active {
            background-color: #444;
        }

        /* Language selector styles */
        .lang-btn {
            background-color: #2d3748;
            color: #a0aec0;
            border: 1px solid #4a5568;
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .lang-btn:hover {
            background-color: #4a5568;
            color: white;
        }

        .lang-btn.active {
            background-color: #4299e1;
            color: white;
            border-color: #4299e1;
        }

        /* Copy button styles */
        .copy-btn {
            background-color: black;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            cursor: pointer;
            margin-left: 5px;
            font-weight: normal;
        }

        .copy-btn:hover {
            background-color: #333;
        }

        .copy-btn:active {
            background-color: #555;
        }

        /* 🎨 CHATGPT-STYLE RESPONSE FORMATTING */
        .ai-response {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #e2e8f0;
            font-size: 14px;
        }

        .ai-response h1,
        .ai-response h2,
        .ai-response h3,
        .ai-response h4,
        .ai-response h5,
        .ai-response h6 {
            color: #90cdf4;
            margin: 1.5em 0 0.5em 0;
            font-weight: 600;
        }

        .ai-response h1 {
            font-size: 1.5em;
        }

        .ai-response h2 {
            font-size: 1.3em;
        }

        .ai-response h3 {
            font-size: 1.1em;
        }

        .ai-response p {
            margin: 0.8em 0;
        }

        .ai-response ul,
        .ai-response ol {
            margin: 0.8em 0;
            padding-left: 1.5em;
        }

        .ai-response li {
            margin: 0.3em 0;
        }

        .ai-response blockquote {
            border-left: 4px solid #4299e1;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: rgba(66, 153, 225, 0.1);
            border-radius: 0 4px 4px 0;
        }

        .ai-response strong {
            color: #f7fafc;
            font-weight: 600;
        }

        .ai-response em {
            color: #cbd5e0;
            font-style: italic;
        }

        .ai-response code {
            background-color: #2d3748;
            color: #90cdf4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            font-size: 0.9em;
        }

        .ai-response pre {
            background-color: #1a202c !important;
            border: 1px solid #2d3748;
            border-radius: 8px;
            padding: 16px;
            margin: 1em 0;
            overflow-x: auto;
            position: relative;
        }

        .ai-response pre code {
            background: none;
            color: #e2e8f0;
            padding: 0;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .code-block-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #2d3748;
            padding: 8px 12px;
            border-radius: 6px 6px 0 0;
            border-bottom: 1px solid #4a5568;
            font-size: 12px;
            color: #a0aec0;
        }

        .code-language {
            font-weight: 500;
            text-transform: uppercase;
        }

        .code-copy-btn {
            background-color: #4a5568;
            color: #e2e8f0;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .code-copy-btn:hover {
            background-color: #718096;
        }

        .code-copy-btn:active {
            background-color: #4299e1;
        }

        .ai-response table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            border: 1px solid #4a5568;
            border-radius: 6px;
            overflow: hidden;
        }

        .ai-response th,
        .ai-response td {
            border: 1px solid #4a5568;
            padding: 8px 12px;
            text-align: left;
        }

        .ai-response th {
            background-color: #2d3748;
            color: #90cdf4;
            font-weight: 600;
        }

        .ai-response td {
            background-color: rgba(45, 55, 72, 0.3);
        }

        .ai-response a {
            color: #4299e1;
            text-decoration: none;
        }

        .ai-response a:hover {
            text-decoration: underline;
        }

        /* Syntax highlighting overrides for dark theme */
        .hljs {
            background: #1a202c !important;
            color: #e2e8f0 !important;
        }

        .hljs-keyword {
            color: #f56565 !important;
        }

        .hljs-string {
            color: #68d391 !important;
        }

        .hljs-comment {
            color: #a0aec0 !important;
        }

        .hljs-number {
            color: #fbb6ce !important;
        }

        .hljs-function {
            color: #90cdf4 !important;
        }

        .hljs-variable {
            color: #fbd38d !important;
        }
    </style>
</head>

<body>
    <!-- Mode Switch Buttons -->
    <div class="mode-switch-buttons">
        <button class="mode-switch-btn" id="dsa-btn" onclick="selectMode('dsa')">GowithDSA</button>
        <button class="mode-switch-btn" id="chatbot-btn" onclick="selectMode('chatbot')">Gowithchatbot</button>
        <button class="mode-switch-btn" id="interview-btn" onclick="selectMode('interview')">Interview Copilot</button>
    </div>

    <!-- Language Selector for DSA Mode -->
    <div id="language-selector" style="display: none; position: absolute; top: 50px; left: 10px; z-index: 1000;">
        <div style="background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px; border: 1px solid #333;">
            <span style="color: white; font-size: 12px; margin-right: 10px;">Code Language:</span>
            <button class="lang-btn active" id="cpp-btn" onclick="selectLanguage('cpp')">C++ (1)</button>
            <button class="lang-btn" id="java-btn" onclick="selectLanguage('java')">Java (2)</button>
            <button class="lang-btn" id="python-btn" onclick="selectLanguage('python')">Python (3)</button>
            <button class="lang-btn" id="js-btn" onclick="selectLanguage('javascript')">JS (4)</button>
        </div>
    </div>

    <div id="chat-container"></div>

    <div id="input-container">
        <input type="text" id="message-input" placeholder="Type your message...">
        <button id="send-button">Send</button>
    </div>

    <div id="controls">
        <p id="controls-text">Toggle Visibility: Cmd+B | Move: Cmd+Arrows</p>
    </div>

    <button id="settings-button">⚙️</button>

    <div id="modal-overlay"></div>
    <div id="settings-modal">
        <h2>Settings</h2>
        <div>
            <label for="api-type-select">API Provider:</label>
            <select id="api-type-select">
                <option value="qwen">Alibaba Qwen</option>
                <option value="gemini">Google Gemini</option>
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic Claude</option>
                <option value="deepgram">Deepgram (Speech)</option>
            </select>
        </div>
        <label for="api-key-input">API Key:</label>
        <input type="password" id="api-key-input" placeholder="Enter your API key...">
        <button id="save-settings">Save</button>
    </div>

    <script>
        // Cache buster - force reload
        console.log('Script loaded at:', new Date().toISOString());

        // 🎨 CHATGPT-STYLE RESPONSE FORMATTER
        function formatChatGPTResponse(text) {
            try {
                // Configure marked for better parsing
                marked.setOptions({
                    highlight: function (code, lang) {
                        if (lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {
                                console.warn('Highlight.js error:', err);
                            }
                        }
                        return hljs.highlightAuto(code).value;
                    },
                    breaks: true,
                    gfm: true
                });

                // Parse markdown to HTML
                let html = marked.parse(text);

                // Add copy buttons to code blocks
                html = html.replace(/<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g, (match, lang, code) => {
                    const cleanCode = code.replace(/<[^>]*>/g, ''); // Remove HTML tags for copying
                    return `
                        <div class="code-block-header">
                            <span class="code-language">${lang}</span>
                            <button class="code-copy-btn" onclick="copyCodeToClipboard(\`${cleanCode.replace(/`/g, '\\`')}\`)">Copy</button>
                        </div>
                        <pre><code class="language-${lang}">${code}</code></pre>
                    `;
                });

                // Add copy buttons to inline code blocks without language
                html = html.replace(/<pre><code>([\s\S]*?)<\/code><\/pre>/g, (match, code) => {
                    const cleanCode = code.replace(/<[^>]*>/g, '');
                    return `
                        <div class="code-block-header">
                            <span class="code-language">Code</span>
                            <button class="code-copy-btn" onclick="copyCodeToClipboard(\`${cleanCode.replace(/`/g, '\\`')}\`)">Copy</button>
                        </div>
                        <pre><code>${code}</code></pre>
                    `;
                });

                return html;
            } catch (error) {
                console.error('Error formatting response:', error);
                // Fallback to original text if formatting fails
                return text.replace(/\n/g, '<br>');
            }
        }

        // Copy code to clipboard function
        function copyCodeToClipboard(code) {
            navigator.clipboard.writeText(code).then(() => {
                console.log('✅ Code copied to clipboard');
                // Optional: Show brief success indicator
                const event = new CustomEvent('showToast', { detail: 'Code copied!' });
                document.dispatchEvent(event);
            }).catch(err => {
                console.error('❌ Failed to copy code:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }

        // 📋 ENHANCED COPY FUNCTIONS FOR CHATGPT-STYLE CODE BLOCKS

        // Clean code text by removing HTML entities and extra whitespace
        function cleanCodeText(codeText) {
            if (!codeText) return '';

            // Decode HTML entities
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = codeText;
            let cleanText = tempDiv.textContent || tempDiv.innerText || '';

            // Remove extra whitespace but preserve code formatting
            cleanText = cleanText.replace(/^\s+|\s+$/g, ''); // Trim start/end

            return cleanText;
        }

        // Copy to clipboard with multiple fallback methods
        async function copyToClipboardWithFallback(text) {
            try {
                // Method 1: Modern Clipboard API
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    await navigator.clipboard.writeText(text);
                    return;
                }
            } catch (err) {
                console.log('Clipboard API failed, trying fallback method...');
            }

            try {
                // Method 2: execCommand fallback
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (!successful) {
                    throw new Error('execCommand copy failed');
                }
            } catch (err) {
                // Method 3: Manual selection fallback
                console.error('All copy methods failed:', err);
                throw new Error('Failed to copy to clipboard');
            }
        }

        // Global variables
        let currentMode = null; // 'dsa' or 'chatbot'
        let typingIndicator = null;
        let apiKey = '';
        let apiType = 'gemini';
        let selectedLanguage = 'cpp'; // Default to C++
        let conversationHistory = [
            { role: "system", content: "You are a helpful assistant." }
        ];
        let selectedScreenshot = null;
        let allScreenshots = []; // Store all screenshots for batch analysis

        // Debug Assistant variables
        let debugMode = false;
        let debugSession = {
            screenshots: [],
            attempts: [],
            currentAttempt: 0,
            problemScreenshot: null,
            codeScreenshot: null,
            errorScreenshot: null
        };

        // 🚀 MULTI-API POWERHOUSE SYSTEM
        let apiManager = {
            providers: {
                openai: {
                    name: 'OpenAI GPT-4',
                    available: true,
                    responseTime: 0,
                    successRate: 100,
                    specialty: 'complex-coding',
                    lastUsed: 0,
                    requestCount: 0
                },
                anthropic: {
                    name: 'Anthropic Claude',
                    available: true,
                    responseTime: 0,
                    successRate: 100,
                    specialty: 'debugging-analysis',
                    lastUsed: 0,
                    requestCount: 0
                },
                gemini: {
                    name: 'Google Gemini',
                    available: true,
                    responseTime: 0,
                    successRate: 100,
                    specialty: 'vision-screenshot',
                    lastUsed: 0,
                    requestCount: 0
                },
                qwen: {
                    name: 'Alibaba Qwen',
                    available: true,
                    responseTime: 0,
                    successRate: 100,
                    specialty: 'fast-response',
                    lastUsed: 0,
                    requestCount: 0
                }
            },
            currentProvider: 'gemini', // Default
            autoSelect: true,
            failoverEnabled: true,
            parallelMode: false
        };

        // 🎤 INTERVIEW COPILOT MODE (IPM) VARIABLES
        let interviewMode = {
            active: false,
            isListening: false,
            isMuted: false,
            lastQuestion: '',
            lastResponse: '',
            recognition: null,
            audioContext: null,
            conversationHistory: [],
            transcribedText: '', // For storing speech-to-text result
            // Enhanced features
            deepgramSocket: null,
            useDeepgram: true, // Now using Deepgram by default
            mediaRecorder: null,
            audioStream: null,
            questionBuffer: '',
            confidenceThreshold: 0.8,
            statusIndicator: null,
            responseQuality: 'professional', // professional, conversational, technical
            interviewType: 'general' // general, technical, behavioral, system-design
        };

        // ⚡ DEEPGRAM + GROQ ULTRA-FAST PIPELINE VARIABLES
        let deepgramGroqPipeline = {
            isListening: false,
            deepgramSocket: null,
            audioStream: null,
            audioContext: null,
            mediaRecorder: null,
            currentTranscript: '',
            groqApiKey: null,
            deepgramApiKey: null
        };

        // Helper function to focus input field reliably
        function focusInputField(withVisualFeedback = true) {
            const messageInput = document.getElementById('message-input');
            if (messageInput && currentMode === 'chatbot') {
                console.log('Focusing input field...');
                messageInput.blur(); // Reset focus first
                setTimeout(() => {
                    messageInput.focus();
                    messageInput.select();
                    if (withVisualFeedback) {
                        messageInput.style.border = '2px solid #4299e1';
                        messageInput.style.boxShadow = '0 0 8px #4299e1';
                        messageInput.style.backgroundColor = 'rgba(66, 153, 225, 0.05)';
                        setTimeout(() => {
                            messageInput.style.border = '';
                            messageInput.style.boxShadow = '';
                            messageInput.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                        }, 1200);
                    }
                    console.log('Input field focused successfully');
                }, 30);
                return true;
            }
            return false;
        }

        // Utility functions
        function showTypingIndicator() {
            console.log('showTypingIndicator called, current typingIndicator:', typingIndicator);
            if (typingIndicator) return;

            typingIndicator = document.createElement('div');
            typingIndicator.classList.add('typing-indicator');
            typingIndicator.innerHTML = '<span></span><span></span><span></span>';
            document.getElementById('chat-container').appendChild(typingIndicator);
            document.getElementById('chat-container').scrollTop = document.getElementById('chat-container').scrollHeight;
            console.log('Typing indicator added to DOM');
        }

        function hideTypingIndicator() {
            if (typingIndicator) {
                document.getElementById('chat-container').removeChild(typingIndicator);
                typingIndicator = null;
            }
        }

        function formatCodeForCopy(code) {
            // Simple but effective code formatter
            let formatted = code;

            // Step 1: Add line breaks after key characters
            formatted = formatted
                .replace(/;/g, ';\n')           // After semicolons
                .replace(/{/g, '{\n')           // After opening braces
                .replace(/}/g, '\n}\n')         // Around closing braces
                .replace(/,/g, ',\n');          // After commas

            // Step 2: Split into lines and add proper indentation
            let lines = formatted.split('\n');
            let indentLevel = 0;
            let result = [];

            for (let line of lines) {
                line = line.trim();
                if (!line) continue;

                // Decrease indent for closing braces
                if (line.includes('}')) {
                    indentLevel = Math.max(0, indentLevel - 1);
                }

                // Add indentation
                result.push('    '.repeat(indentLevel) + line);

                // Increase indent for opening braces
                if (line.includes('{')) {
                    indentLevel++;
                }
            }

            return result.join('\n');
        }

        function formatMessage(text) {
            // Format code blocks with copy button
            text = text.replace(/```([\s\S]*?)```/g, function (match, code) {
                const codeId = 'code_' + Math.random().toString(36).substr(2, 9);
                const cleanCode = code.trim();
                // Format the code for proper copying
                const formattedCode = formatCodeForCopy(cleanCode);
                // Store formatted code in data attribute for proper copying
                const encodedCode = encodeURIComponent(formattedCode);
                return `<div style="position: relative;"><pre><code id="${codeId}" data-original-code="${encodedCode}">${cleanCode}</code></pre><button class="copy-btn" onclick="copyCode('${codeId}')" title="Copy code (Ctrl+Shift+C)">COPY</button></div>`;
            });

            // Format inline code
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Convert URLs to links
            text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" style="color: #90cdf4; text-decoration: underline;">$1</a>');

            // Convert line breaks to <br>
            text = text.replace(/\n/g, '<br>');

            return text;
        }

        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                // Get the original code from data attribute (preserves formatting)
                let text = codeElement.getAttribute('data-original-code');

                if (text) {
                    // Decode the stored code
                    text = decodeURIComponent(text);
                } else {
                    // Fallback to textContent if data attribute is not available
                    text = codeElement.textContent || codeElement.innerText;
                }

                navigator.clipboard.writeText(text).then(() => {
                    console.log('Code copied to clipboard with proper formatting');
                    console.log('Copied text preview:', text.substring(0, 100) + '...');
                    // Briefly change button text to show success
                    const button = codeElement.parentElement.querySelector('.copy-btn');
                    const originalText = button.textContent;
                    button.textContent = 'COPIED!';
                    setTimeout(() => {
                        button.textContent = originalText;
                    }, 1000);
                }).catch(err => {
                    console.error('Failed to copy code:', err);
                });
            }
        }

        function addMessage(text, isUser) {
            hideTypingIndicator();

            const messageElement = document.createElement('div');
            messageElement.classList.add('message');
            messageElement.classList.add(isUser ? 'user-message' : 'bot-message');

            // 🎨 CHATGPT-STYLE FORMATTING FOR AI RESPONSES
            if (!isUser) {
                // Add ai-response class for ChatGPT styling
                messageElement.classList.add('ai-response');

                // Use ChatGPT-style formatting with markdown parsing and syntax highlighting
                try {
                    messageElement.innerHTML = formatChatGPTResponse(text);
                } catch (error) {
                    console.error('Error formatting ChatGPT response:', error);
                    // Fallback to basic formatting if ChatGPT formatting fails
                    messageElement.innerHTML = formatMessage(text);
                }
            } else {
                // Keep user messages as simple text (no formatting needed)
                messageElement.textContent = text;
            }

            document.getElementById('chat-container').appendChild(messageElement);
            document.getElementById('chat-container').scrollTop = document.getElementById('chat-container').scrollHeight;
        }

        // Removed focusInputField function - using only existing input field

        // Screenshot analysis function - now analyzes only selected screenshots
        async function analyzeSelectedScreenshots() {
            console.log('analyzeSelectedScreenshots called');
            console.log('Current mode:', currentMode);

            if (currentMode !== 'dsa') {
                addMessage('Screenshot analysis is only available in DSA Mode.', false);
                return;
            }

            // Get selected screenshots and validate they exist
            let selectedScreenshots = await validateSelectedScreenshots();

            // 🚀 SEAMLESS WORKFLOW: Auto-select screenshots if none selected
            if (selectedScreenshots.length === 0) {
                // Check if screenshots exist in DOM
                const allScreenshots = document.querySelectorAll('.message img[src^="file://"]');

                if (allScreenshots.length === 0) {
                    addMessage('❌ No screenshots found. Please take screenshots first using Ctrl+H.', false);
                    return;
                }

                // Auto-select all screenshots
                allScreenshots.forEach((img) => {
                    img.style.border = '2px solid #4299e1';
                    img.classList.add('screenshot-selected');
                });

                // Re-validate after auto-selection
                selectedScreenshots = await validateSelectedScreenshots();

                if (selectedScreenshots.length === 0) {
                    // Ultimate fallback: Use file paths directly
                    if (allScreenshots && allScreenshots.length > 0) {
                        const fallbackPaths = [];
                        allScreenshots.forEach(img => {
                            const path = normalizeFilePath(img.src);
                            fallbackPaths.push(path);
                        });

                        if (fallbackPaths.length > 0) {
                            selectedScreenshots = fallbackPaths;
                        }
                    }

                    if (selectedScreenshots.length === 0) {
                        addMessage('❌ Unable to process screenshots. Please try again.', false);
                        return;
                    }
                }
            }

            console.log(`Analyzing ${selectedScreenshots.length} selected screenshot(s)...`);
            addMessage(`Analyzing ${selectedScreenshots.length} selected screenshot(s)...`, false);

            // Show loading dots
            showTypingIndicator();

            try {
                // Process selected screenshots using Electron API
                const base64Images = [];

                for (let i = 0; i < selectedScreenshots.length; i++) {
                    const screenshotPath = selectedScreenshots[i];
                    console.log(`Processing selected screenshot ${i + 1}/${selectedScreenshots.length}: ${screenshotPath}`);

                    try {
                        // Use Electron API to read the file as base64
                        const base64Result = await window.electronAPI.readFileAsBase64(screenshotPath);
                        if (base64Result.success) {
                            base64Images.push(base64Result.data);
                            console.log(`Successfully read selected screenshot ${i + 1}`);
                        } else {
                            console.error(`Failed to read selected screenshot ${i + 1}:`, base64Result.error);
                            throw new Error(`Failed to read selected screenshot ${i + 1}: ${base64Result.error}`);
                        }
                    } catch (fileError) {
                        console.error(`Error reading file ${screenshotPath}:`, fileError);
                        throw new Error(`Error reading screenshot file: ${fileError.message}`);
                    }
                }

                console.log(`Successfully processed ${base64Images.length} selected screenshots`);

                // Analyze selected screenshots together
                await analyzeDSAProblemsMultiple(base64Images);

            } catch (error) {
                console.error('Error reading selected screenshots:', error);
                hideTypingIndicator();
                addMessage(`Error reading selected screenshots: ${error.message}`, false);
            }
        }

        // Helper function to normalize file path from file:// URL
        function normalizeFilePath(fileUrl) {
            let path = fileUrl.replace('file://', '');

            // Handle Windows paths - remove leading slash if it's a Windows absolute path
            if (path.match(/^\/[A-Za-z]:/)) {
                path = path.substring(1);
            }

            // 🔧 DECODE URL ENCODING (fix %20 spaces issue)
            try {
                path = decodeURIComponent(path);
            } catch (error) {
                console.warn('⚠️ Failed to decode path:', path, error);
            }

            return path;
        }

        // Helper function to get selected screenshots (synchronous version for quick checks)
        function getSelectedScreenshots() {
            const selectedImages = document.querySelectorAll('.screenshot-selected');
            const selectedPaths = [];

            selectedImages.forEach(img => {
                const path = normalizeFilePath(img.src);
                selectedPaths.push(path);
            });

            return selectedPaths;
        }

        // Helper function to validate and filter existing screenshots
        async function validateSelectedScreenshots() {
            const selectedImages = document.querySelectorAll('.screenshot-selected');
            const validPaths = [];

            for (const img of selectedImages) {
                const path = normalizeFilePath(img.src);

                // Check if file exists before adding to list
                try {
                    const result = await window.electronAPI.readFileAsBase64(path);
                    if (result.success) {
                        validPaths.push(path);
                    } else {
                        console.warn(`Screenshot file no longer exists: ${path}`);
                        // Remove the broken screenshot from UI
                        img.parentElement.remove();
                    }
                } catch (error) {
                    console.warn(`Error checking screenshot file: ${path}`, error);
                    // Remove the broken screenshot from UI
                    img.parentElement.remove();
                }
            }

            return validPaths;
        }

        // Screenshot selection functions
        function selectCurrentScreenshot() {
            const screenshots = document.querySelectorAll('.message img[src^="file://"]');
            if (screenshots.length === 0) {
                console.log('No screenshots available to select');
                return;
            }

            // Find currently highlighted screenshot (via arrow navigation)
            let currentIndex = -1;
            screenshots.forEach((img, index) => {
                if (img.classList.contains('screenshot-highlighted')) {
                    currentIndex = index;
                }
            });

            // If no highlighted screenshot, select the first one
            if (currentIndex === -1) {
                currentIndex = 0;
            }

            const targetImg = screenshots[currentIndex];
            targetImg.style.border = '2px solid #4299e1';
            targetImg.classList.add('screenshot-selected');
            selectedScreenshot = normalizeFilePath(targetImg.src);
            console.log(`Screenshot ${currentIndex + 1} selected via keyboard (S)`);
        }

        function deselectCurrentScreenshot() {
            const screenshots = document.querySelectorAll('.message img[src^="file://"]');
            if (screenshots.length === 0) {
                console.log('No screenshots available to deselect');
                return;
            }

            // Find currently highlighted screenshot (via arrow navigation)
            let currentIndex = -1;
            screenshots.forEach((img, index) => {
                if (img.classList.contains('screenshot-highlighted')) {
                    currentIndex = index;
                }
            });

            // If no highlighted screenshot, deselect the first selected one
            if (currentIndex === -1) {
                const selectedImages = document.querySelectorAll('.screenshot-selected');
                if (selectedImages.length > 0) {
                    selectedImages[0].style.border = '2px solid transparent';
                    selectedImages[0].classList.remove('screenshot-selected');
                    console.log('First selected screenshot deselected via keyboard (D)');
                }
                return;
            }

            const targetImg = screenshots[currentIndex];
            targetImg.style.border = '2px solid transparent';
            targetImg.classList.remove('screenshot-selected');
            console.log(`Screenshot ${currentIndex + 1} deselected via keyboard (D)`);
        }

        function selectAllScreenshots() {
            const screenshots = document.querySelectorAll('.message img[src^="file://"]');
            if (screenshots.length === 0) {
                console.log('No screenshots available to select');
                return;
            }

            screenshots.forEach((img, index) => {
                img.style.border = '2px solid #4299e1';
                img.classList.add('screenshot-selected');
            });
            console.log(`All ${screenshots.length} screenshots selected via keyboard (A)`);
        }

        function deselectAllScreenshots() {
            const screenshots = document.querySelectorAll('.screenshot-selected');
            if (screenshots.length === 0) {
                console.log('No screenshots selected to deselect');
                return;
            }

            screenshots.forEach(img => {
                img.style.border = '2px solid transparent';
                img.classList.remove('screenshot-selected');
            });
            console.log('All screenshots deselected via keyboard (X)');
        }

        // 🚀 AUTO-SELECT LATEST SCREENSHOT FOR SEAMLESS ONLINE ASSESSMENT WORKFLOW
        function autoSelectLatestScreenshot() {
            const screenshots = document.querySelectorAll('.message img[src^="file://"]');
            if (screenshots.length === 0) {
                console.log('No screenshots available to auto-select');
                return;
            }

            // Clear all previous selections
            screenshots.forEach(img => {
                img.style.border = '2px solid transparent';
                img.classList.remove('screenshot-selected');
            });

            // Select the latest (last) screenshot
            const latestScreenshot = screenshots[screenshots.length - 1];
            latestScreenshot.style.border = '2px solid #4299e1';
            latestScreenshot.classList.add('screenshot-selected');
            selectedScreenshot = normalizeFilePath(latestScreenshot.src);

            console.log(`🎯 Latest screenshot auto-selected for instant analysis (${screenshots.length} total)`);
        }

        // Function to clean up broken screenshot references
        async function cleanupBrokenScreenshots() {
            const allScreenshotImages = document.querySelectorAll('.message img[src^="file://"]');
            let removedCount = 0;

            for (const img of allScreenshotImages) {
                const path = normalizeFilePath(img.src);

                try {
                    const result = await window.electronAPI.readFileAsBase64(path);
                    if (!result.success) {
                        console.log(`Removing broken screenshot from UI: ${path}`);
                        img.parentElement.remove();
                        removedCount++;
                    }
                } catch (error) {
                    console.log(`Removing broken screenshot from UI: ${path}`);
                    img.parentElement.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                console.log(`Cleaned up ${removedCount} broken screenshot(s) from UI`);
                // Update allScreenshots array to remove broken paths
                const validPaths = [];
                for (const path of allScreenshots) {
                    try {
                        const result = await window.electronAPI.readFileAsBase64(path);
                        if (result.success) {
                            validPaths.push(path);
                        }
                    } catch (error) {
                        // Skip broken paths
                    }
                }
                allScreenshots = validPaths;
            }
        }

        // 🚀 MULTI-API POWERHOUSE FUNCTIONS

        // Smart API selection based on task type
        function selectBestAPI(taskType, forceProvider = null) {
            if (forceProvider && apiManager.providers[forceProvider]?.available) {
                return forceProvider;
            }

            if (!apiManager.autoSelect) {
                return apiManager.currentProvider;
            }

            // Task-based API selection
            switch (taskType) {
                case 'screenshot-analysis':
                case 'vision':
                    return findBestAvailableAPI(['gemini', 'openai', 'anthropic', 'qwen']);

                case 'debugging':
                case 'error-analysis':
                    return findBestAvailableAPI(['anthropic', 'openai', 'gemini', 'qwen']);

                case 'complex-coding':
                case 'algorithm':
                    return findBestAvailableAPI(['openai', 'anthropic', 'gemini', 'qwen']);

                case 'fast-response':
                case 'quick-query':
                    return findBestAvailableAPI(['qwen', 'gemini', 'openai', 'anthropic']);

                default:
                    return findBestAvailableAPI(['gemini', 'openai', 'anthropic', 'qwen']);
            }
        }

        // Find best available API from priority list
        function findBestAvailableAPI(priorityList) {
            for (const provider of priorityList) {
                if (apiManager.providers[provider]?.available) {
                    return provider;
                }
            }
            return 'gemini'; // Fallback
        }

        // Update API performance metrics
        function updateAPIMetrics(provider, responseTime, success) {
            const api = apiManager.providers[provider];
            if (!api) return;

            api.lastUsed = Date.now();
            api.requestCount++;

            if (success) {
                api.responseTime = (api.responseTime + responseTime) / 2; // Moving average
                api.successRate = Math.min(100, api.successRate + 1);
            } else {
                api.successRate = Math.max(0, api.successRate - 5);
                api.available = api.successRate > 20; // Mark unavailable if too many failures
            }

            console.log(`📊 API Metrics - ${api.name}: ${api.responseTime}ms, ${api.successRate}% success`);
        }

        // Enhanced API call with smart routing and failover
        async function smartAPICall(base64Images, prompt, taskType = 'general', forceProvider = null) {
            const selectedAPI = selectBestAPI(taskType, forceProvider);
            const startTime = Date.now();

            console.log(`🚀 Smart API Call - Selected: ${apiManager.providers[selectedAPI].name} for ${taskType}`);
            addMessage(`🚀 **${apiManager.providers[selectedAPI].name}** selected for optimal performance...`, false);

            try {
                let result;

                // Call the appropriate API
                switch (selectedAPI) {
                    case 'openai':
                        result = await makeOpenAIAPICall(base64Images, prompt);
                        break;
                    case 'anthropic':
                        result = await makeAnthropicAPICall(base64Images, prompt);
                        break;
                    case 'gemini':
                        result = await makeGeminiAPICall(base64Images, prompt);
                        break;
                    case 'qwen':
                        result = await makeQwenAPICall(base64Images, prompt);
                        break;
                    default:
                        result = await makeGeminiAPICall(base64Images, prompt);
                }

                const responseTime = Date.now() - startTime;
                updateAPIMetrics(selectedAPI, responseTime, true);

                return result;

            } catch (error) {
                const responseTime = Date.now() - startTime;
                updateAPIMetrics(selectedAPI, responseTime, false);

                console.error(`❌ ${apiManager.providers[selectedAPI].name} failed:`, error);

                // Failover to next best API
                if (apiManager.failoverEnabled && !forceProvider) {
                    console.log('🔄 Attempting failover to backup API...');
                    addMessage(`🔄 **Failover activated** - Trying backup API...`, false);

                    const backupAPIs = ['gemini', 'openai', 'anthropic', 'qwen'].filter(api =>
                        api !== selectedAPI && apiManager.providers[api].available
                    );

                    if (backupAPIs.length > 0) {
                        return await smartAPICall(base64Images, prompt, taskType, backupAPIs[0]);
                    }
                }

                throw error;
            }
        }

        // 🤖 INDIVIDUAL API CALL FUNCTIONS

        // OpenAI API call
        async function makeOpenAIAPICall(base64Images, prompt) {
            const apiKey = await loadApiKey('openai');
            if (!apiKey) {
                throw new Error('OpenAI API key not found');
            }

            const messages = [
                {
                    role: "user",
                    content: [
                        { type: "text", text: prompt },
                        ...base64Images.map(base64 => ({
                            type: "image_url",
                            image_url: { url: `data:image/png;base64,${base64}` }
                        }))
                    ]
                }
            ];

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: "gpt-4o",
                    messages: messages,
                    max_tokens: 2000
                })
            });

            if (!response.ok) {
                throw new Error(`OpenAI API error: ${response.status}`);
            }

            const data = await response.json();
            const content = data.choices[0].message.content;

            hideTypingIndicator();
            addMessage(content, false);

            return content;
        }

        // Anthropic API call
        async function makeAnthropicAPICall(base64Images, prompt) {
            const apiKey = await loadApiKey('anthropic');
            if (!apiKey) {
                throw new Error('Anthropic API key not found');
            }

            const messages = [
                {
                    role: "user",
                    content: [
                        { type: "text", text: prompt },
                        ...base64Images.map(base64 => ({
                            type: "image",
                            source: {
                                type: "base64",
                                media_type: "image/png",
                                data: base64
                            }
                        }))
                    ]
                }
            ];

            const response = await fetch('https://api.anthropic.com/v1/messages', {
                method: 'POST',
                headers: {
                    'x-api-key': apiKey,
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                },
                body: JSON.stringify({
                    model: "claude-3-5-sonnet-20241022",
                    max_tokens: 2000,
                    messages: messages
                })
            });

            if (!response.ok) {
                throw new Error(`Anthropic API error: ${response.status}`);
            }

            const data = await response.json();
            const content = data.content[0].text;

            hideTypingIndicator();
            addMessage(content, false);

            return content;
        }

        // Enhanced Qwen API call
        async function makeQwenAPICall(base64Images, prompt) {
            const apiKey = await loadApiKey('qwen');
            if (!apiKey) {
                throw new Error('Qwen API key not found');
            }

            const messages = [
                {
                    role: "user",
                    content: [
                        { type: "text", text: prompt },
                        ...base64Images.map(base64 => ({
                            type: "image_url",
                            image_url: { url: `data:image/png;base64,${base64}` }
                        }))
                    ]
                }
            ];

            const response = await fetch('https://api.openrouter.ai/api/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: "qwen/qwen-2-vl-72b-instruct",
                    messages: messages,
                    max_tokens: 2000
                })
            });

            if (!response.ok) {
                throw new Error(`Qwen API error: ${response.status}`);
            }

            const data = await response.json();
            const content = data.choices[0].message.content;

            hideTypingIndicator();
            addMessage(content, false);

            return content;
        }

        // 🎤 INTERVIEW COPILOT MODE (IPM) FUNCTIONS

        // Enhanced speech recognition with Deepgram support
        async function initializeSpeechRecognition() {
            console.log('🎤 Initializing speech recognition...');

            // Try Deepgram first for better accuracy
            if (await initializeDeepgram()) {
                interviewMode.useDeepgram = true;
                addMessage('🚀 **Deepgram initialized** - Premium speech recognition active!', false);
                return true;
            }

            // Fallback to Web Speech API
            console.log('🎤 Checking Web Speech API support...');
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                console.log('🔇 Speech recognition not supported in this browser');
                // Only show error if interview mode is still active
                if (interviewMode.active) {
                    addMessage('❌ **Speech recognition not supported** in this browser. Please use Chrome or Edge.', false);
                }
                return false;
            }

            console.log('🎤 Web Speech API supported, initializing...');

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            interviewMode.recognition = new SpeechRecognition();

            // Configure speech recognition
            interviewMode.recognition.continuous = true;
            interviewMode.recognition.interimResults = true;
            interviewMode.recognition.lang = 'en-US';
            interviewMode.recognition.maxAlternatives = 1;

            // Event handlers
            interviewMode.recognition.onstart = () => {
                console.log('🎤 Speech recognition started');
                interviewMode.isListening = true;
                updateInterviewStatus();
            };

            interviewMode.recognition.onend = () => {
                console.log('🎤 Speech recognition ended');
                interviewMode.isListening = false;
                updateInterviewStatus();

                // Auto-restart if still in interview mode and not muted
                if (interviewMode.active && !interviewMode.isMuted) {
                    setTimeout(() => {
                        if (interviewMode.active && !interviewMode.isMuted) {
                            startListening();
                        }
                    }, 1000);
                }
            };

            interviewMode.recognition.onresult = (event) => {
                let finalTranscript = '';
                let interimTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    const confidence = event.results[i][0].confidence;

                    if (event.results[i].isFinal) {
                        if (confidence >= interviewMode.confidenceThreshold) {
                            finalTranscript += transcript;
                        }
                    } else {
                        interimTranscript += transcript;
                    }
                }

                if (finalTranscript) {
                    console.log('🎤 Final transcript:', finalTranscript);
                    processInterviewQuestion(finalTranscript);
                }
            };

            interviewMode.recognition.onerror = (event) => {
                // Only log errors if interview mode is still active AND it's a critical error
                if (interviewMode.active && event.error === 'not-allowed') {
                    console.log('🔇 Microphone access denied');
                    addMessage('❌ **Microphone access denied**. Please allow microphone access and try again.', false);
                } else {
                    // Suppress all other errors (network, aborted, etc.) - they're just noise
                    console.log('🔇 Speech error suppressed:', event.error);
                }
            };

            addMessage('🎤 **Web Speech API initialized** - Standard speech recognition active', false);
            return true;
        }

        // Initialize Deepgram with Stereo Mix
        async function initializeDeepgram() {
            try {
                // Get Deepgram API key
                const envResult = await window.electronAPI.getApiKey('deepgram');
                if (!envResult.success || !envResult.key) {
                    addMessage('❌ **Deepgram API key not found** - Check .env file', false);
                    return false;
                }

                // Validate API key format
                const apiKey = envResult.key.trim();
                console.log('🔍 Deepgram API key length:', apiKey.length);
                console.log('🔍 Deepgram API key starts with:', apiKey.substring(0, 10) + '...');

                if (apiKey.length < 20) {
                    addMessage('❌ **Deepgram API key too short** - Get proper key from console.deepgram.com', false);
                    return false;
                }

                // Initialize audio context
                interviewMode.audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // Setup Deepgram WebSocket with proper authentication
                const deepgramUrl = `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&smart_format=true&interim_results=true&endpointing=300`;

                // Create WebSocket with Authorization header using subprotocol method
                interviewMode.deepgramSocket = new WebSocket(deepgramUrl, ['token', envResult.key]);

                interviewMode.deepgramSocket.onopen = () => {
                    addMessage('🚀 **Deepgram ready** - Use Ctrl+X for Stereo Mix', false);

                    // Send keep-alive message to prevent immediate closure
                    const keepAliveMessage = JSON.stringify({
                        type: "KeepAlive"
                    });
                    interviewMode.deepgramSocket.send(keepAliveMessage);
                };

                interviewMode.deepgramSocket.onclose = (event) => {
                    if (event.code === 1000) {
                        addMessage('✅ **Deepgram session ended normally** - Press Ctrl+X to start audio capture', false);
                    } else if (event.code === 1011) {
                        addMessage('❌ **Authentication failed** - Invalid Deepgram API key in .env file', false);
                    } else if (event.code === 1006) {
                        addMessage('❌ **Connection failed** - Check internet connection', false);
                    } else {
                        addMessage(`❌ **Deepgram disconnected** - Code: ${event.code}`, false);
                    }
                };

                interviewMode.deepgramSocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);

                        // Initialize Deepgram connection status
                        if (!interviewMode.deepgramConnected) {
                            interviewMode.deepgramConnected = true;
                        }

                        // Only process Results with actual transcripts
                        if (data.type === 'Results' && data.channel && data.channel.alternatives && data.channel.alternatives[0]) {
                            const transcript = data.channel.alternatives[0].transcript;

                            if (transcript && transcript.trim().length > 0) {
                                // Capture transcript for button workflow
                                if (data.is_final) {
                                    // Add final transcript to current transcript buffer
                                    interviewMode.currentTranscript += transcript + ' ';

                                    // Only auto-process if not using button workflow
                                    const startBtn = document.getElementById('start-listen-btn');
                                    if (!startBtn || !startBtn.disabled) {
                                        // Old workflow - auto process
                                        processInterviewQuestion(transcript);
                                    }
                                }
                            }
                        }
                    } catch (parseError) {
                        // Silent error handling
                    }
                };

                interviewMode.deepgramSocket.onerror = (error) => {
                    console.error('🔥 Deepgram WebSocket error:', error);
                    addMessage('❌ **Deepgram connection failed** - Check API key and internet', false);

                    // Try fallback to Web Speech API
                    console.log('🔄 Trying Web Speech API fallback due to Deepgram error...');
                    setTimeout(() => {
                        setupWebSpeechFallback();
                    }, 1000);
                };

                return true;

            } catch (error) {
                addMessage('❌ **Deepgram initialization failed**', false);
                return false;
            }
        }

        // Setup audio streaming for Deepgram
        function setupAudioStreaming() {
            if (!interviewMode.audioStream) {
                addMessage('❌ **No audio stream** - Press Ctrl+X first', false);
                return;
            }

            const source = interviewMode.audioContext.createMediaStreamSource(interviewMode.audioStream);
            const processor = interviewMode.audioContext.createScriptProcessor(4096, 1, 1);

            let audioSent = false;
            let audioChunks = 0;

            processor.onaudioprocess = (event) => {
                if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                    const audioData = event.inputBuffer.getChannelData(0);

                    // Check if there's actual audio
                    let hasAudio = false;
                    let maxLevel = 0;
                    for (let i = 0; i < audioData.length; i++) {
                        const level = Math.abs(audioData[i]);
                        if (level > 0.001) hasAudio = true;
                        maxLevel = Math.max(maxLevel, level);
                    }

                    if (hasAudio) {
                        const int16Array = new Int16Array(audioData.length);
                        for (let i = 0; i < audioData.length; i++) {
                            const sample = Math.max(-1, Math.min(1, audioData[i]));
                            int16Array[i] = sample * 32767;
                        }

                        interviewMode.deepgramSocket.send(int16Array.buffer);
                        audioChunks++;

                        // Show first audio detection
                        if (!audioSent) {
                            audioSent = true;
                            addMessage(`🎵 **Audio detected** - Level: ${(maxLevel * 100).toFixed(1)}% - Sending to Deepgram...`, false);
                        }
                    }
                } else {
                    if (audioChunks === 0) {
                        addMessage('❌ **Deepgram WebSocket not ready** - Check connection', false);
                    }
                }
            };

            source.connect(processor);
            processor.connect(interviewMode.audioContext.destination);
            interviewMode.audioProcessor = processor;

            addMessage('🎵 **Audio streaming active** - Play audio to test detection', false);
        }

        // Web Speech API fallback when Deepgram fails
        function setupWebSpeechFallback() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                addMessage('❌ **Web Speech API not supported** - Please get a valid Deepgram API key', false);
                return;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            interviewMode.speechRecognition = new SpeechRecognition();

            interviewMode.speechRecognition.continuous = true;
            interviewMode.speechRecognition.interimResults = true;
            interviewMode.speechRecognition.lang = 'en-US';

            interviewMode.speechRecognition.onresult = (event) => {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    transcript += event.results[i][0].transcript;
                }

                if (transcript.trim()) {
                    addMessage(`🎤 **Transcribed:** ${transcript}`, false);
                    interviewMode.currentTranscript = (interviewMode.currentTranscript || '') + ' ' + transcript;
                }
            };

            interviewMode.speechRecognition.onerror = (event) => {
                console.log('🔄 Speech recognition error:', event.error);

                // Detailed error handling
                let errorMessage = `⚠️ **Speech error:** ${event.error}`;

                switch (event.error) {
                    case 'not-allowed':
                        errorMessage += '\n\n**Solution:** Allow microphone access in browser settings';
                        break;
                    case 'no-speech':
                        errorMessage += '\n\n**Solution:** Speak louder or check microphone';
                        break;
                    case 'audio-capture':
                        errorMessage += '\n\n**Solution:** Check microphone connection';
                        break;
                    case 'network':
                        errorMessage += '\n\n**Solution:** Check internet connection';
                        break;
                    case 'service-not-allowed':
                        errorMessage += '\n\n**Solution:** Speech service blocked - try different browser';
                        break;
                    default:
                        errorMessage += '\n\n**Solution:** Try restarting the app';
                }

                addMessage(errorMessage, false);
            };

            interviewMode.speechRecognition.onend = () => {
                if (interviewMode.isListening) {
                    // Restart if still listening
                    setTimeout(() => {
                        try {
                            interviewMode.speechRecognition.start();
                        } catch (e) {
                            console.log('🔄 Restarting speech recognition...');
                        }
                    }, 100);
                }
            };

            try {
                interviewMode.speechRecognition.start();
                interviewMode.isListening = true;
                addMessage('🎤 **Web Speech API started** - Speak into microphone!', false);
                addMessage('💡 **Note:** Web Speech API uses microphone, not system audio', false);
            } catch (e) {
                addMessage('❌ **Speech API failed** - Check microphone permissions', false);
            }
        }

        // Test speech recognition directly
        async function testSpeechRecognition() {
            addMessage('🔧 **Testing Speech Recognition...**', false);

            // Test 1: Check browser support
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                addMessage('❌ **Browser not supported** - Use Chrome or Edge', false);
                return;
            }

            addMessage('✅ **Browser supports Web Speech API**', false);

            // Test 2: Check microphone permissions
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                addMessage('✅ **Microphone access granted**', false);
                stream.getTracks().forEach(track => track.stop());
            } catch (error) {
                addMessage(`❌ **Microphone error:** ${error.name} - ${error.message}`, false);
                return;
            }

            // Test 3: Try speech recognition
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onstart = () => {
                addMessage('🎤 **Speech test started** - Say "Hello test" now!', false);
            };

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                addMessage(`✅ **Speech test SUCCESS!** Heard: "${transcript}"`, false);
            };

            recognition.onerror = (event) => {
                addMessage(`❌ **Speech test FAILED:** ${event.error}`, false);
            };

            recognition.onend = () => {
                addMessage('🔧 **Speech test completed**', false);
            };

            try {
                recognition.start();
            } catch (error) {
                addMessage(`❌ **Could not start speech test:** ${error.message}`, false);
            }
        }

        // Test Groq API with detailed debugging
        async function testGroqAPI() {
            addMessage('⚡ **Testing Groq API with detailed debugging...**', false);

            try {
                // Get Groq API key
                const envResult = await window.electronAPI.getApiKey('groq');
                if (!envResult.success || !envResult.key) {
                    addMessage('❌ **Groq API key not found in .env file**', false);
                    addMessage('**Solution:** Add GROQ_API_KEY=your_key_here to .env file', false);
                    return;
                }

                const apiKey = envResult.key.trim();
                addMessage('✅ **Groq API key found in .env file**', false);
                addMessage(`🔍 **Key length:** ${apiKey.length} characters`, false);
                addMessage(`🔍 **Key starts with:** ${apiKey.substring(0, 10)}...`, false);

                if (!apiKey.startsWith('gsk_')) {
                    addMessage('⚠️ **Warning:** Groq API keys should start with "gsk_"', false);
                }

                addMessage('🌐 **Testing Groq API connection...**', false);

                // Test API with detailed error handling
                const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'llama3-70b-8192',  // Fixed model name
                        messages: [
                            {
                                role: 'user',
                                content: 'Say "Groq API test successful!" if you can read this.'
                            }
                        ],
                        max_tokens: 50,
                        temperature: 0.1
                    })
                });

                addMessage(`📡 **HTTP Status:** ${response.status} ${response.statusText}`, false);

                if (response.ok) {
                    const data = await response.json();
                    const testResponse = data.choices[0].message.content;

                    addMessage('✅ **GROQ API TEST SUCCESSFUL!** 🎉', false);
                    addMessage(`🤖 **Groq response:** ${testResponse}`, false);
                    addMessage('🚀 **Ultra-fast pipeline ready to use!**', false);
                } else {
                    const errorText = await response.text();
                    addMessage(`❌ **GROQ API TEST FAILED**`, false);
                    addMessage(`**HTTP Status:** ${response.status}`, false);
                    addMessage(`**Error Response:** ${errorText}`, false);

                    if (response.status === 401) {
                        addMessage('**🔑 401 = Invalid API Key**', false);
                        addMessage('**Solutions:**', false);
                        addMessage('1. Get fresh key from: https://console.groq.com/keys', false);
                        addMessage('2. Make sure key starts with "gsk_"', false);
                        addMessage('3. Check for extra spaces in .env file', false);
                        addMessage('4. Restart app after updating key', false);
                    } else if (response.status === 429) {
                        addMessage('**⏰ 429 = Rate Limit**', false);
                        addMessage('**Solution:** Wait a moment and try again', false);
                    } else if (response.status === 403) {
                        addMessage('**🚫 403 = Forbidden**', false);
                        addMessage('**Solution:** Check if your Groq account has access', false);
                    }
                }

            } catch (error) {
                addMessage(`❌ **Network/Connection Error:** ${error.message}`, false);
                addMessage('**Possible causes:**', false);
                addMessage('1. No internet connection', false);
                addMessage('2. Firewall blocking request', false);
                addMessage('3. VPN issues', false);
                addMessage('4. DNS problems', false);
                console.error('Groq API test error:', error);
            }
        }

        // Quick manual Groq test (bypasses .env file)
        async function testGroqManual() {
            const userKey = prompt('Enter your Groq API key (starts with gsk_):');
            if (!userKey) {
                addMessage('❌ **Test cancelled** - No API key provided', false);
                return;
            }

            addMessage('⚡ **Testing Groq API manually...**', false);
            addMessage(`🔍 **Using key:** ${userKey.substring(0, 10)}...`, false);

            try {
                const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${userKey.trim()}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'llama3-70b-8192',
                        messages: [
                            {
                                role: 'user',
                                content: 'Say "Manual Groq test successful!" if you can read this.'
                            }
                        ],
                        max_tokens: 50,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const testResponse = data.choices[0].message.content;

                    addMessage('✅ **MANUAL GROQ TEST SUCCESSFUL!** 🎉', false);
                    addMessage(`🤖 **Response:** ${testResponse}`, false);
                    addMessage('💡 **Your key works! Update your .env file with this key.**', false);
                } else {
                    const errorText = await response.text();
                    addMessage(`❌ **Manual test failed:** ${response.status}`, false);
                    addMessage(`**Error:** ${errorText}`, false);

                    if (response.status === 401) {
                        addMessage('🔑 **Invalid API key** - Get fresh key from https://console.groq.com/keys', false);
                    }
                }

            } catch (error) {
                addMessage(`❌ **Network error:** ${error.message}`, false);
            }
        }

        // Test Deepgram API directly
        async function testDeepgramAPI() {
            addMessage('🌊 **Testing Deepgram API...**', false);

            try {
                // Get Deepgram API key
                const envResult = await window.electronAPI.getApiKey('deepgram');
                if (!envResult.success || !envResult.key) {
                    addMessage('❌ **Deepgram API key not found in .env file**', false);
                    addMessage('**Solution:** Add DEEPGRAM_API_KEY=your_key_here to .env file', false);
                    addMessage('**Get key from:** https://console.deepgram.com/', false);
                    return;
                }

                const apiKey = envResult.key.trim();
                addMessage('✅ **Deepgram API key found in .env file**', false);
                addMessage(`🔍 **Key length:** ${apiKey.length} characters`, false);
                addMessage(`🔍 **Key starts with:** ${apiKey.substring(0, 10)}...`, false);

                addMessage('🌐 **Testing Deepgram API connection...**', false);

                // Test API with a simple request
                const response = await fetch('https://api.deepgram.com/v1/projects', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Token ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                addMessage(`📡 **HTTP Status:** ${response.status} ${response.statusText}`, false);

                if (response.ok) {
                    const data = await response.json();

                    addMessage('✅ **DEEPGRAM API TEST SUCCESSFUL!** 🎉', false);
                    addMessage(`🏢 **Projects found:** ${data.projects ? data.projects.length : 'N/A'}`, false);
                    addMessage('🚀 **Deepgram ready for real-time transcription!**', false);
                } else {
                    const errorText = await response.text();
                    addMessage(`❌ **DEEPGRAM API TEST FAILED**`, false);
                    addMessage(`**HTTP Status:** ${response.status}`, false);
                    addMessage(`**Error Response:** ${errorText}`, false);

                    if (response.status === 401) {
                        addMessage('**🔑 401 = Invalid API Key**', false);
                        addMessage('**Solutions:**', false);
                        addMessage('1. Get fresh key from: https://console.deepgram.com/', false);
                        addMessage('2. Make sure key format is correct', false);
                        addMessage('3. Check for extra spaces in .env file', false);
                        addMessage('4. Restart app after updating key', false);
                    } else if (response.status === 403) {
                        addMessage('**🚫 403 = Forbidden**', false);
                        addMessage('**Solution:** Check if your Deepgram account has access', false);
                    }
                }

            } catch (error) {
                addMessage(`❌ **Network/Connection Error:** ${error.message}`, false);
                addMessage('**Possible causes:**', false);
                addMessage('1. No internet connection', false);
                addMessage('2. Firewall blocking request', false);
                addMessage('3. VPN issues', false);
                addMessage('4. DNS problems', false);
                console.error('Deepgram API test error:', error);
            }
        }

        // Test microphone access with detailed diagnostics
        async function testMicrophoneAccess() {
            console.log('🔍 Running detailed microphone diagnostics...');

            try {
                // Check if mediaDevices is available
                if (!navigator.mediaDevices) {
                    throw new Error('navigator.mediaDevices not available');
                }

                // Check if getUserMedia is available
                if (!navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not available');
                }

                console.log('✅ MediaDevices API available');

                // Try to enumerate devices first
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioInputs = devices.filter(device => device.kind === 'audioinput');
                    console.log(`🎤 Found ${audioInputs.length} audio input devices:`, audioInputs);

                    if (audioInputs.length === 0) {
                        throw new Error('No audio input devices found');
                    }
                } catch (enumError) {
                    console.warn('⚠️ Could not enumerate devices:', enumError);
                }

                // Test system audio access first (for interviewer's voice)
                let stream = null;
                let audioSource = 'unknown';

                try {
                    console.log('🔊 Requesting system audio access (for interviewer voice)...');
                    stream = await navigator.mediaDevices.getDisplayMedia({
                        video: false,
                        audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false,
                            systemAudio: 'include'
                        }
                    });
                    audioSource = 'system';
                    console.log('✅ System audio access granted!');
                } catch (systemError) {
                    console.log('⚠️ System audio failed, trying microphone...');

                    // Fallback to microphone
                    stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });
                    audioSource = 'microphone';
                    console.log('✅ Microphone access granted (fallback)');
                }

                console.log(`✅ Audio access granted via ${audioSource}!`);
                console.log('🎤 Stream details:', {
                    active: stream.active,
                    tracks: stream.getTracks().length,
                    audioTracks: stream.getAudioTracks().length,
                    source: audioSource
                });

                // Test audio track
                const audioTracks = stream.getAudioTracks();
                if (audioTracks.length > 0) {
                    const track = audioTracks[0];
                    console.log('🎤 Audio track details:', {
                        enabled: track.enabled,
                        muted: track.muted,
                        readyState: track.readyState,
                        label: track.label
                    });
                }

                // Stop the test stream
                stream.getTracks().forEach(track => track.stop());

                return { success: true, stream: stream, audioSource: audioSource };

            } catch (error) {
                console.error('❌ Microphone test failed:', error);
                return { success: false, error: error };
            }
        }

        // Start listening for interviewer questions
        async function startListening() {
            console.log('🎤 startListening() called');

            // Run detailed microphone test
            const micTest = await testMicrophoneAccess();

            if (!micTest.success) {
                console.error('🎤 Microphone access denied:', micTest.error);

                let errorMessage = '❌ **Microphone access denied**\n\n';

                if (micTest.error.name === 'NotAllowedError') {
                    errorMessage += '**Permission denied by user or system**\n\n';
                    errorMessage += '**Solutions:**\n';
                    errorMessage += '1. **Windows Settings** → Privacy & Security → Microphone\n';
                    errorMessage += '2. Turn ON "Allow apps to access microphone"\n';
                    errorMessage += '3. Turn ON "Allow desktop apps to access microphone"\n';
                    errorMessage += '4. Restart the app after changing settings\n';
                } else if (micTest.error.name === 'NotFoundError') {
                    errorMessage += '**No microphone found**\n\n';
                    errorMessage += '**Solutions:**\n';
                    errorMessage += '1. Check if microphone is connected\n';
                    errorMessage += '2. Check Windows Sound Settings\n';
                    errorMessage += '3. Set correct default microphone\n';
                } else if (micTest.error.name === 'NotReadableError') {
                    errorMessage += '**Microphone in use by another app**\n\n';
                    errorMessage += '**Solutions:**\n';
                    errorMessage += '1. Close other apps using microphone\n';
                    errorMessage += '2. Check for running video calls\n';
                    errorMessage += '3. Restart the app\n';
                } else {
                    errorMessage += `**Error:** ${micTest.error.message}\n\n`;
                    errorMessage += '**General Solutions:**\n';
                    errorMessage += '1. Check Windows microphone permissions\n';
                    errorMessage += '2. Try refreshing the app (Ctrl+R)\n';
                    errorMessage += '3. Restart the app\n';
                }

                addMessage(errorMessage, false);
                return;
            }

            console.log('✅ Audio test passed, proceeding with voice recognition...');

            if (micTest.audioSource === 'system') {
                addMessage('✅ **System audio capture enabled** - Can transcribe interviewer questions from speakers!', false);
            } else {
                addMessage('✅ **Microphone access granted** - Using microphone input', false);
                addMessage('💡 **Tip:** For interviews, try Ctrl+Shift+Space to capture system audio (interviewer voice)', false);
            }

            // Debug: Check interview mode state
            console.log('🔍 Interview mode state:', {
                active: interviewMode.active,
                useDeepgram: interviewMode.useDeepgram,
                isListening: interviewMode.isListening,
                isMuted: interviewMode.isMuted
            });

            if (interviewMode.useDeepgram) {
                console.log('🔍 Deepgram socket state:', {
                    exists: !!interviewMode.deepgramSocket,
                    readyState: interviewMode.deepgramSocket?.readyState,
                    CONNECTING: WebSocket.CONNECTING,
                    OPEN: WebSocket.OPEN,
                    CLOSING: WebSocket.CLOSING,
                    CLOSED: WebSocket.CLOSED
                });

                if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                    interviewMode.isListening = true;
                    updateInterviewStatus();
                    console.log('🎤 Started Deepgram listening');
                    addMessage('🎤 **Deepgram listening started** - Speak your question!', false);
                } else {
                    console.log('🔄 Deepgram not ready, reinitializing...');
                    addMessage('🔄 **Reconnecting to Deepgram...**', false);
                    const initResult = await initializeDeepgram();
                    console.log('🔍 Deepgram initialization result:', initResult);

                    if (initResult) {
                        // Try starting again after initialization
                        setTimeout(() => {
                            console.log('🔄 Retrying to start listening after Deepgram init...');
                            startListening();
                        }, 2000);
                    }
                }
            } else {
                if (!interviewMode.recognition) {
                    console.log('🎤 Recognition not initialized, initializing now...');
                    if (!await initializeSpeechRecognition()) {
                        return;
                    }
                }

                if (!interviewMode.isListening && !interviewMode.isMuted) {
                    try {
                        console.log('🎤 Starting Web Speech API...');
                        interviewMode.recognition.start();
                        console.log('🎤 Web Speech API started successfully');
                        addMessage('🎤 **Web Speech API listening** - Speak your question!', false);
                    } catch (error) {
                        // Suppress speech recognition startup errors during mode switching
                        console.log('🔇 Speech recognition startup error suppressed:', error.message);
                        // Only show error if interview mode is still active
                        if (interviewMode.active) {
                            addMessage(`❌ **Speech recognition error**: ${error.message}\n` +
                                'Try:\n' +
                                '1. Refresh the app (Ctrl+R)\n' +
                                '2. Check microphone permissions\n' +
                                '3. Use Chrome or Edge browser', false);
                        }
                    }
                }
            }
        }

        // Stop listening
        function stopListening() {
            console.log('🛑 stopListening() called');

            if (interviewMode.useDeepgram) {
                interviewMode.isListening = false;
                updateInterviewStatus();
                console.log('🎤 Stopped Deepgram listening');
                addMessage('⏸️ **Deepgram listening stopped**', false);
            } else {
                if (interviewMode.recognition && interviewMode.isListening) {
                    interviewMode.recognition.stop();
                    console.log('🎤 Stopped Web Speech API listening');
                    addMessage('⏸️ **Web Speech API stopped**', false);
                }
            }
        }

        // Cleanup interview mode resources
        function cleanupInterviewMode() {
            console.log('🧹 Cleaning up interview mode resources...');

            // FIRST: Set interview mode as inactive to suppress error logs
            interviewMode.active = false;
            interviewMode.isListening = false;
            interviewMode.useDeepgram = false;

            // Stop listening (this will now suppress errors due to active = false)
            try {
                stopListening();
            } catch (error) {
                console.log('🔇 Suppressed stop listening error during cleanup');
            }

            // Stop speech recognition if active
            if (interviewMode.recognition) {
                try {
                    interviewMode.recognition.stop();
                    interviewMode.recognition = null;
                } catch (error) {
                    console.log('🔇 Suppressed speech recognition error during cleanup');
                }
            }

            // Close Deepgram connection
            if (interviewMode.deepgramSocket) {
                try {
                    interviewMode.deepgramSocket.close();
                    interviewMode.deepgramSocket = null;
                } catch (error) {
                    console.log('🔇 Suppressed Deepgram close error during cleanup');
                }
            }

            // Stop audio stream
            if (interviewMode.audioStream) {
                try {
                    interviewMode.audioStream.getTracks().forEach(track => track.stop());
                    interviewMode.audioStream = null;
                } catch (error) {
                    console.log('🔇 Suppressed audio stream error during cleanup');
                }
            }

            // Close audio context
            if (interviewMode.audioContext) {
                try {
                    interviewMode.audioContext.close();
                    interviewMode.audioContext = null;
                } catch (error) {
                    console.log('🔇 Suppressed audio context error during cleanup');
                }
            }

            // Disconnect audio processor
            if (interviewMode.audioProcessor) {
                try {
                    interviewMode.audioProcessor.disconnect();
                    interviewMode.audioProcessor = null;
                } catch (error) {
                    console.log('🔇 Suppressed audio processor error during cleanup');
                }
            }

            // Stop media recorder if exists
            if (interviewMode.mediaRecorder && interviewMode.mediaRecorder.state !== 'inactive') {
                try {
                    interviewMode.mediaRecorder.stop();
                    interviewMode.mediaRecorder = null;
                } catch (error) {
                    console.log('🔇 Suppressed media recorder error during cleanup');
                }
            }

            // Clear transcribed text
            interviewMode.transcribedText = '';

            console.log('✅ Interview mode cleanup complete - All errors suppressed');
        }

        // Dedicated Stereo Mix capture function
        async function captureStereoMix() {
            console.log('🔊 Attempting to capture Stereo Mix directly...');

            try {
                // Get all audio input devices
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');

                console.log('🎤 All available audio devices:');
                audioInputs.forEach((device, index) => {
                    console.log(`${index + 1}. ${device.label} (ID: ${device.deviceId})`);
                });

                // Look for Stereo Mix device (multiple possible names)
                const stereoMixDevice = audioInputs.find(device => {
                    const label = device.label.toLowerCase();
                    return label.includes('stereo mix') ||
                        label.includes('stereomix') ||
                        label.includes('what u hear') ||
                        label.includes('wave out mix') ||
                        label.includes('speakers') ||
                        label.includes('loopback') ||
                        label.includes('monitor');
                });

                if (!stereoMixDevice) {
                    throw new Error('Stereo Mix device not found in available devices');
                }

                console.log('🎯 Found Stereo Mix device:', stereoMixDevice.label);
                addMessage(`🎯 **Found Stereo Mix:** ${stereoMixDevice.label}`, false);

                // Stop current audio stream if exists
                if (interviewMode.audioStream) {
                    interviewMode.audioStream.getTracks().forEach(track => track.stop());
                }

                // Capture from Stereo Mix device specifically
                interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        deviceId: { exact: stereoMixDevice.deviceId },
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: false, // Don't cancel system audio
                        noiseSuppression: false, // Keep all system audio
                        autoGainControl: false   // Don't adjust system audio
                    }
                });

                addMessage(`✅ **Stereo Mix connected:** ${stereoMixDevice.label}`, false);

                // Setup audio streaming with Deepgram
                if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                    setupAudioStreaming();
                    addMessage('🎤 **Ready to transcribe** - Play audio to test!', false);
                } else {
                    addMessage('⚠️ **Waiting for Deepgram connection** - Try again in a moment', false);
                    // Retry after a short delay
                    setTimeout(() => {
                        if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                            setupAudioStreaming();
                            addMessage('🎤 **Ready to transcribe** - Play audio to test!', false);
                        }
                    }, 2000);
                }

                // Auto-start listening
                interviewMode.isListening = true;

                return true;

            } catch (error) {
                console.error('❌ Stereo Mix capture failed:', error);
                addMessage(`❌ **Stereo Mix capture failed**: ${error.message}`, false);
                return false;
            }
        }

        // Force system audio capture (for capturing interviewer's voice from speakers)
        async function forceSystemAudioCapture() {
            console.log('🔊 Forcing system audio capture...');

            // Check browser compatibility
            const userAgent = navigator.userAgent;
            console.log('🌐 Browser:', userAgent);

            if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
                addMessage('❌ **System audio not supported** - Browser doesn\'t support getDisplayMedia API\n\n' +
                    '**Solutions:**\n' +
                    '1. Use Chrome or Edge browser\n' +
                    '2. Update your browser to latest version\n' +
                    '3. Use Ctrl+Space for microphone instead', false);
                return;
            }

            try {
                // Stop current listening if active
                if (interviewMode.isListening) {
                    stopListening();
                }

                // Clean up existing streams
                if (interviewMode.audioStream) {
                    interviewMode.audioStream.getTracks().forEach(track => track.stop());
                }

                addMessage('🔊 **Requesting system audio access** - Select "Share system audio" when prompted!', false);

                // Try multiple approaches for system audio capture
                let stream = null;

                // Approach 1: Audio-only with systemAudio
                try {
                    console.log('🔊 Trying audio-only with systemAudio...');
                    stream = await navigator.mediaDevices.getDisplayMedia({
                        video: false,
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false,
                            systemAudio: 'include'
                        }
                    });
                    console.log('✅ Audio-only approach worked!');
                } catch (audioOnlyError) {
                    console.log('⚠️ Audio-only failed, trying with video...');

                    // Approach 2: With video (some browsers require video for system audio)
                    try {
                        stream = await navigator.mediaDevices.getDisplayMedia({
                            video: {
                                width: 1,
                                height: 1,
                                frameRate: 1
                            },
                            audio: {
                                sampleRate: 16000,
                                channelCount: 1,
                                echoCancellation: false,
                                noiseSuppression: false,
                                autoGainControl: false
                            }
                        });
                        console.log('✅ Video+audio approach worked!');

                        // Stop video track if we got one
                        const videoTracks = stream.getVideoTracks();
                        videoTracks.forEach(track => track.stop());

                    } catch (videoError) {
                        console.log('⚠️ Video+audio failed, trying basic approach...');

                        // Approach 3: Basic getDisplayMedia
                        stream = await navigator.mediaDevices.getDisplayMedia({
                            audio: true,
                            video: false
                        });
                        console.log('✅ Basic approach worked!');
                    }
                }

                interviewMode.audioStream = stream;

                console.log('✅ System audio capture granted!');
                addMessage('✅ **System audio capture enabled** - Now listening to speakers/interviewer!', false);

                // Reinitialize Deepgram with system audio
                if (interviewMode.useDeepgram && interviewMode.deepgramSocket) {
                    setupAudioStreaming();
                    addMessage('🚀 **Deepgram reconnected with system audio** - Ready to transcribe interviewer!', false);
                }

                // Auto-start listening
                setTimeout(() => {
                    startListening();
                }, 1000);

            } catch (error) {
                console.error('❌ System audio capture failed:', error);

                let errorMessage = '❌ **System audio capture failed**\n\n';

                if (error.name === 'NotAllowedError') {
                    errorMessage += '**User denied system audio access**\n\n';
                    errorMessage += '**To enable system audio:**\n';
                    errorMessage += '1. Press Ctrl+Shift+Space again\n';
                    errorMessage += '2. Click "Share" when prompted\n';
                    errorMessage += '3. Make sure "Share system audio" is checked\n';
                    errorMessage += '4. This will capture interviewer\'s voice from speakers\n';
                } else {
                    errorMessage += `**Error:** ${error.message}\n\n`;
                    errorMessage += '**Fallback:** Using microphone instead\n';
                }

                addMessage(errorMessage, false);

                // Try alternative: Stereo Mix capture
                addMessage('🔄 **Trying alternative method** - Stereo Mix capture...', false);

                try {
                    // Try to capture with stereo mix or "What U Hear" device
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioInputs = devices.filter(device => device.kind === 'audioinput');
                    console.log('🎤 Available audio devices:', audioInputs);

                    // Look for stereo mix or system audio devices
                    const stereoMixDevice = audioInputs.find(device =>
                        device.label.toLowerCase().includes('stereo mix') ||
                        device.label.toLowerCase().includes('what u hear') ||
                        device.label.toLowerCase().includes('wave out mix') ||
                        device.label.toLowerCase().includes('speakers')
                    );

                    if (stereoMixDevice) {
                        console.log('🔊 Found stereo mix device:', stereoMixDevice.label);
                        interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                            audio: {
                                deviceId: stereoMixDevice.deviceId,
                                sampleRate: 16000,
                                channelCount: 1,
                                echoCancellation: false,
                                noiseSuppression: false,
                                autoGainControl: false
                            }
                        });
                        addMessage('✅ **Stereo Mix capture enabled** - Can hear system audio!', false);

                        // Auto-start listening
                        setTimeout(() => {
                            startListening();
                        }, 1000);
                        return;
                    }
                } catch (stereoMixError) {
                    console.log('⚠️ Stereo Mix failed:', stereoMixError);
                }

                // Final fallback to microphone
                try {
                    interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });
                    addMessage('⚠️ **Using microphone fallback** - For system audio, enable Stereo Mix in Windows', false);
                } catch (fallbackError) {
                    addMessage('❌ **All audio capture methods failed** - Check permissions and audio devices', false);
                }
            }
        }

        // Toggle mute/unmute
        function toggleInterviewMute() {
            interviewMode.isMuted = !interviewMode.isMuted;

            if (interviewMode.isMuted) {
                stopListening();
                addMessage('🔇 **Interview mode MUTED** - Not listening for questions', false);
            } else {
                startListening();
                addMessage('🎤 **Interview mode UNMUTED** - Listening for questions', false);
            }

            updateInterviewStatus();
        }

        // Enhanced question processing with context awareness
        async function processInterviewQuestion(question) {
            // Advanced question filtering
            if (question.length < 8 || isNoiseOrFiller(question)) {
                return;
            }

            // Detect question type for better responses
            const questionType = detectQuestionType(question);
            interviewMode.interviewType = questionType;

            interviewMode.lastQuestion = question;
            console.log('🎯 Processing interview question:', question, 'Type:', questionType);

            // Show question being processed with type indicator
            addMessage(`🎤 **${questionType.toUpperCase()} Question detected:** "${question}"`, false);
            addMessage('🧠 **Generating professional response...**', false);

            showTypingIndicator();

            try {
                // Create enhanced interview-specific prompt
                const interviewPrompt = createInterviewPrompt(question, questionType);

                // Use smart API call for interview responses
                const response = await smartAPICall([], interviewPrompt, 'complex-coding');

                interviewMode.lastResponse = response;
                interviewMode.conversationHistory.push({
                    question: question,
                    response: response,
                    type: questionType,
                    timestamp: Date.now()
                });

                console.log('✅ Interview response generated');

            } catch (error) {
                console.error('Error generating interview response:', error);
                hideTypingIndicator();
                addMessage(`❌ Error generating response: ${error.message}`, false);
            }
        }

        // Detect question type for tailored responses
        function detectQuestionType(question) {
            const lowerQuestion = question.toLowerCase();

            // Technical questions
            if (lowerQuestion.includes('algorithm') || lowerQuestion.includes('data structure') ||
                lowerQuestion.includes('complexity') || lowerQuestion.includes('code') ||
                lowerQuestion.includes('programming') || lowerQuestion.includes('technical')) {
                return 'technical';
            }

            // Behavioral questions
            if (lowerQuestion.includes('tell me about') || lowerQuestion.includes('describe a time') ||
                lowerQuestion.includes('how do you handle') || lowerQuestion.includes('experience')) {
                return 'behavioral';
            }

            // System design questions
            if (lowerQuestion.includes('design') || lowerQuestion.includes('architecture') ||
                lowerQuestion.includes('scale') || lowerQuestion.includes('system')) {
                return 'system-design';
            }

            return 'general';
        }

        // Create enhanced interview prompt based on question type
        function createInterviewPrompt(question, questionType) {
            const basePrompt = `You are an expert interview coach helping a candidate answer interview questions professionally.

Question from interviewer: "${question}"
Question Type: ${questionType}

Provide a professional, confident, and detailed answer that sounds natural when spoken aloud. The response should:

1. Be conversational and natural (not robotic)
2. Include specific examples and practical knowledge
3. Show deep understanding of the topic
4. Be appropriate length for verbal response (45-90 seconds when spoken)
5. Sound confident and knowledgeable
6. Include relevant technical details if it's a technical question
7. Use STAR method for behavioral questions (Situation, Task, Action, Result)
8. Be structured and easy to follow when reading aloud

`;

            // Add type-specific instructions
            switch (questionType) {
                case 'technical':
                    return basePrompt + `
TECHNICAL FOCUS:
- Explain concepts clearly with examples
- Mention time/space complexity if relevant
- Include practical applications
- Show problem-solving approach
- Demonstrate deep technical knowledge

Make the candidate sound like a senior engineer who knows their stuff.`;

                case 'behavioral':
                    return basePrompt + `
BEHAVIORAL FOCUS:
- Use STAR method (Situation, Task, Action, Result)
- Include specific metrics and outcomes
- Show leadership and problem-solving skills
- Demonstrate growth mindset
- Include lessons learned

Make the candidate sound experienced and reflective.`;

                case 'system-design':
                    return basePrompt + `
SYSTEM DESIGN FOCUS:
- Start with requirements gathering
- Discuss scalability considerations
- Mention specific technologies
- Include trade-offs and alternatives
- Show architectural thinking

Make the candidate sound like a system architect.`;

                default:
                    return basePrompt + `Make the candidate sound knowledgeable and professional.`;
            }
        }

        // Check if text is noise or filler
        function isNoiseOrFiller(text) {
            const fillerWords = ['um', 'uh', 'hmm', 'okay', 'alright', 'so', 'well', 'you know'];
            const words = text.toLowerCase().split(' ').filter(word => word.length > 0);

            // If mostly filler words, ignore
            const fillerCount = words.filter(word => fillerWords.includes(word)).length;
            return fillerCount / words.length > 0.7;
        }

        // Repeat last response with enhancement
        function repeatLastResponse() {
            if (interviewMode.lastResponse) {
                addMessage('🔄 **Repeating last response:**', false);
                addMessage(interviewMode.lastResponse, false);

                // Add helpful tip
                addMessage('💡 **Tip:** Read this naturally as if you\'re explaining from your own experience!', false);
            } else {
                addMessage('❌ No previous response to repeat', false);
            }
        }

        // Enhanced status display with visual indicators
        function updateInterviewStatus() {
            const statusIcon = interviewMode.isMuted ? '🔇' : (interviewMode.isListening ? '🎤' : '⏸️');
            const status = interviewMode.isMuted ? 'MUTED' : (interviewMode.isListening ? 'LISTENING' : 'PAUSED');
            const provider = interviewMode.useDeepgram ? 'Deepgram' : 'Web Speech API';

            // Update in console for debugging
            console.log(`🎤 Interview Status: ${status} (${provider})`);

            // Update UI status if needed
            if (interviewMode.statusIndicator) {
                interviewMode.statusIndicator.textContent = `${statusIcon} ${status}`;
            }

            // Update button status display
            const statusDiv = document.getElementById('interview-status');
            if (statusDiv) {
                if (interviewMode.isListening) {
                    statusDiv.innerHTML = '<span style="color: #4CAF50;">🎤 LISTENING - Capturing interviewer voice...</span>';
                } else {
                    statusDiv.innerHTML = '<span style="color: #888;">Ready to capture interviewer questions</span>';
                }
            }
        }

        // START LISTEN Button Function - Capture ALL laptop audio
        async function startInterviewListening() {
            const startBtn = document.getElementById('start-listen-btn');
            const stopBtn = document.getElementById('stop-listen-btn');
            const statusDiv = document.getElementById('interview-status');

            // Update button states
            startBtn.disabled = true;
            stopBtn.disabled = false;
            statusDiv.innerHTML = '<span style="color: #FF9800;">🔄 Initializing audio capture...</span>';

            // Reset transcript
            interviewMode.currentTranscript = '';

            try {
                // Capture ALL laptop audio (system + microphone)
                const audioSuccess = await captureAllLaptopAudio();
                if (audioSuccess) {
                    // Show what audio source was captured
                    const audioTracks = interviewMode.audioStream?.getAudioTracks();
                    const audioSource = audioTracks?.[0]?.label || 'Unknown audio source';
                    statusDiv.innerHTML = '<span style="color: #4CAF50;">🎤 LISTENING - Capturing all laptop audio...</span>';
                    addMessage(`🎤 **Audio capture started** - Source: ${audioSource}`, false);
                    interviewMode.isListening = true;
                } else {
                    throw new Error('Failed to capture laptop audio');
                }

            } catch (error) {
                statusDiv.innerHTML = '<span style="color: #f44336;">❌ Audio capture failed - Check permissions</span>';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        // STOP LISTEN Button Function - Analyze and extract keywords
        async function stopInterviewListening() {
            const startBtn = document.getElementById('start-listen-btn');
            const stopBtn = document.getElementById('stop-listen-btn');
            const statusDiv = document.getElementById('interview-status');

            // Update button states
            startBtn.disabled = false;
            stopBtn.disabled = true;
            statusDiv.innerHTML = '<span style="color: #FF9800;">🧠 Analyzing captured audio...</span>';

            // Stop listening
            interviewMode.isListening = false;
            stopListening();

            // Analyze captured audio for questions
            if (interviewMode.currentTranscript && interviewMode.currentTranscript.trim()) {
                statusDiv.innerHTML = '<span style="color: #FF9800;">🔍 Extracting keywords and detecting questions...</span>';

                // Show captured transcript length for debugging
                const transcriptLength = interviewMode.currentTranscript.trim().length;
                addMessage(`📝 **Captured ${transcriptLength} characters of audio transcript**`, false);

                try {
                    // Smart analysis to extract keywords and detect questions
                    const analysisPrompt = `Analyze this captured audio transcript and extract key information:

"${interviewMode.currentTranscript.trim()}"

Tasks:
1. Extract important keywords and topics
2. Identify if there are any interview questions present
3. If questions found, provide professional answers
4. If no questions, summarize key points mentioned

Respond in this format:
KEYWORDS: [list key terms]
QUESTIONS DETECTED: [yes/no]
RESPONSE: [answer if question found, or summary if no questions]`;

                    // Use Anthropic for fast analysis
                    const originalProvider = apiManager.currentProvider;
                    apiManager.currentProvider = 'anthropic';

                    const analysis = await smartAPICall([], analysisPrompt, 'audio-analysis');

                    // Restore original provider
                    apiManager.currentProvider = originalProvider;

                    addMessage(analysis, false);
                    statusDiv.innerHTML = '<span style="color: #4CAF50;">✅ Analysis complete - Press START LISTEN to continue</span>';

                } catch (error) {
                    statusDiv.innerHTML = '<span style="color: #f44336;">❌ Analysis failed - Ready to try again</span>';
                }
            } else {
                statusDiv.innerHTML = '<span style="color: #FF9800;">⚠️ No audio captured - Press START LISTEN to try again</span>';
            }
        }

        // Capture ALL laptop audio (system + microphone)
        async function captureAllLaptopAudio() {
            try {
                // Initialize audio context if not exists
                if (!interviewMode.audioContext) {
                    interviewMode.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                // Try system audio first (captures everything from speakers)
                try {
                    interviewMode.audioStream = await navigator.mediaDevices.getDisplayMedia({
                        video: false,
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false,
                            systemAudio: 'include'
                        }
                    });

                    // Setup Deepgram streaming or fallback
                    if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                        setupAudioStreaming();
                    } else {
                        // Fallback to Web Speech API
                        console.log('🔄 Deepgram unavailable, using Web Speech API fallback');
                        setupWebSpeechFallback();
                    }

                    return true;

                } catch (systemError) {
                    // Fallback to Stereo Mix
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioInputs = devices.filter(device => device.kind === 'audioinput');

                    const stereoMixDevice = audioInputs.find(device => {
                        const label = device.label.toLowerCase();
                        return label.includes('stereo mix') ||
                            label.includes('what u hear') ||
                            label.includes('speakers') ||
                            label.includes('loopback');
                    });

                    if (stereoMixDevice) {
                        interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                            audio: {
                                deviceId: { exact: stereoMixDevice.deviceId },
                                sampleRate: 16000,
                                channelCount: 1,
                                echoCancellation: false,
                                noiseSuppression: false,
                                autoGainControl: false
                            }
                        });

                        // Setup Deepgram streaming or fallback
                        if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                            setupAudioStreaming();
                        } else {
                            // Fallback to Web Speech API
                            console.log('🔄 Deepgram unavailable, using Web Speech API fallback');
                            setupWebSpeechFallback();
                        }

                        return true;
                    }

                    // Final fallback to microphone
                    interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });

                    // Setup Deepgram streaming or fallback
                    if (interviewMode.deepgramSocket && interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                        setupAudioStreaming();
                    } else {
                        // Fallback to Web Speech API
                        console.log('🔄 Deepgram unavailable, using Web Speech API fallback');
                        setupWebSpeechFallback();
                    }

                    return true;
                }

            } catch (error) {
                return false;
            }
        }

        // ⚡ DEEPGRAM + GROQ ULTRA-FAST PIPELINE FUNCTIONS

        // Initialize Deepgram + Groq pipeline
        async function initializeDeepgramGroqPipeline() {
            try {
                // Get API keys
                const deepgramResult = await window.electronAPI.getApiKey('deepgram');
                const groqResult = await window.electronAPI.getApiKey('groq');

                if (!deepgramResult.success || !deepgramResult.key) {
                    addMessage('❌ **Deepgram API key not found** - Check .env file', false);
                    return false;
                }

                if (!groqResult.success || !groqResult.key) {
                    addMessage('❌ **Groq API key not found** - Check .env file', false);
                    return false;
                }

                deepgramGroqPipeline.deepgramApiKey = deepgramResult.key.trim();
                deepgramGroqPipeline.groqApiKey = groqResult.key.trim();

                addMessage('✅ **Deepgram + Groq pipeline initialized** - Ready for ultra-fast transcription!', false);
                return true;

            } catch (error) {
                addMessage('❌ **Pipeline initialization failed** - ' + error.message, false);
                return false;
            }
        }

        // Start Deepgram + Groq listening
        async function startDeepgramGroqListening() {
            const startBtn = document.getElementById('deepgram-start-btn');
            const stopBtn = document.getElementById('deepgram-stop-btn');
            const statusDiv = document.getElementById('deepgram-status');

            // Update button states
            startBtn.disabled = true;
            stopBtn.disabled = false;
            statusDiv.innerHTML = '<span style="color: #00BCD4;">🔄 Initializing ultra-fast pipeline...</span>';

            try {
                // Initialize pipeline if not already done
                if (!deepgramGroqPipeline.deepgramApiKey || !deepgramGroqPipeline.groqApiKey) {
                    const initSuccess = await initializeDeepgramGroqPipeline();
                    if (!initSuccess) {
                        throw new Error('Pipeline initialization failed');
                    }
                }

                // Get microphone access
                deepgramGroqPipeline.audioStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // Initialize Deepgram WebSocket
                const deepgramUrl = `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&smart_format=true&interim_results=false&endpointing=300`;
                deepgramGroqPipeline.deepgramSocket = new WebSocket(deepgramUrl, ['token', deepgramGroqPipeline.deepgramApiKey]);

                deepgramGroqPipeline.deepgramSocket.onopen = () => {
                    statusDiv.innerHTML = '<span style="color: #4CAF50;">🎤 LISTENING - Speak your question now!</span>';
                    deepgramGroqPipeline.isListening = true;

                    // Start audio streaming
                    setupDeepgramAudioStreaming();
                };

                deepgramGroqPipeline.deepgramSocket.onmessage = async (event) => {
                    try {
                        const data = JSON.parse(event.data);

                        if (data.type === 'Results' && data.channel && data.channel.alternatives && data.channel.alternatives[0]) {
                            const transcript = data.channel.alternatives[0].transcript;

                            if (transcript && transcript.trim().length > 0 && data.is_final) {
                                deepgramGroqPipeline.currentTranscript = transcript;

                                // Display transcribed question immediately
                                addMessage(transcript, true);

                                // Send to Groq for ultra-fast response
                                statusDiv.innerHTML = '<span style="color: #FF9800;">🧠 Generating response with Groq...</span>';
                                await processWithGroq(transcript);
                            }
                        }
                    } catch (parseError) {
                        console.error('Error parsing Deepgram response:', parseError);
                    }
                };

                deepgramGroqPipeline.deepgramSocket.onerror = (error) => {
                    console.error('Deepgram WebSocket error:', error);
                    statusDiv.innerHTML = '<span style="color: #f44336;">❌ Deepgram connection failed - Check API key</span>';
                    addMessage('❌ **Deepgram connection failed** - Invalid API key or network issue', false);
                    addMessage('**Solutions:**', false);
                    addMessage('1. Get fresh Deepgram API key from: https://console.deepgram.com/', false);
                    addMessage('2. Click "🌊 TEST DEEPGRAM" to verify your key', false);
                    addMessage('3. Check internet connection', false);
                };

                deepgramGroqPipeline.deepgramSocket.onclose = () => {
                    statusDiv.innerHTML = '<span style="color: #888;">Ready for ultra-fast interview transcription and response</span>';
                    deepgramGroqPipeline.isListening = false;
                };

            } catch (error) {
                console.error('Error starting Deepgram + Groq pipeline:', error);
                statusDiv.innerHTML = '<span style="color: #f44336;">❌ Failed to start pipeline - ' + error.message + '</span>';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        // Stop Deepgram + Groq listening
        async function stopDeepgramGroqListening() {
            const startBtn = document.getElementById('deepgram-start-btn');
            const stopBtn = document.getElementById('deepgram-stop-btn');
            const statusDiv = document.getElementById('deepgram-status');

            // Update button states
            startBtn.disabled = false;
            stopBtn.disabled = true;
            statusDiv.innerHTML = '<span style="color: #888;">Ready for ultra-fast interview transcription and response</span>';

            // Stop listening
            deepgramGroqPipeline.isListening = false;

            // Close Deepgram connection
            if (deepgramGroqPipeline.deepgramSocket) {
                deepgramGroqPipeline.deepgramSocket.close();
                deepgramGroqPipeline.deepgramSocket = null;
            }

            // Stop audio stream
            if (deepgramGroqPipeline.audioStream) {
                deepgramGroqPipeline.audioStream.getTracks().forEach(track => track.stop());
                deepgramGroqPipeline.audioStream = null;
            }

            // Stop media recorder
            if (deepgramGroqPipeline.mediaRecorder && deepgramGroqPipeline.mediaRecorder.state !== 'inactive') {
                deepgramGroqPipeline.mediaRecorder.stop();
            }

            addMessage('⏸️ **Ultra-fast pipeline stopped** - Ready for next question', false);
        }

        // Setup audio streaming for Deepgram
        function setupDeepgramAudioStreaming() {
            if (!deepgramGroqPipeline.audioStream || !deepgramGroqPipeline.deepgramSocket) {
                return;
            }

            // Create MediaRecorder for streaming to Deepgram
            const options = {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 16000
            };

            try {
                deepgramGroqPipeline.mediaRecorder = new MediaRecorder(deepgramGroqPipeline.audioStream, options);

                deepgramGroqPipeline.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0 && deepgramGroqPipeline.deepgramSocket &&
                        deepgramGroqPipeline.deepgramSocket.readyState === WebSocket.OPEN) {
                        deepgramGroqPipeline.deepgramSocket.send(event.data);
                    }
                };

                // Start recording in small chunks for real-time streaming
                deepgramGroqPipeline.mediaRecorder.start(100); // Send data every 100ms

            } catch (error) {
                console.error('Error setting up audio streaming:', error);
            }
        }

        // Process transcribed text with Groq for ultra-fast response
        async function processWithGroq(question) {
            try {
                const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${deepgramGroqPipeline.groqApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'llama3-70b-8192', // Ultra-fast Groq model
                        messages: [
                            {
                                role: 'system',
                                content: 'You are an expert interview coach. Provide concise, professional answers to interview questions. Be direct, confident, and helpful. Focus on practical advice and clear explanations.'
                            },
                            {
                                role: 'user',
                                content: question
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.7,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Groq API error: ${response.status}`);
                }

                const data = await response.json();
                const aiResponse = data.choices[0].message.content;

                // Display AI response immediately
                addMessage(aiResponse, false);

                // Update status
                const statusDiv = document.getElementById('deepgram-status');
                statusDiv.innerHTML = '<span style="color: #4CAF50;">✅ Response generated - Ready for next question</span>';

            } catch (error) {
                console.error('Error processing with Groq:', error);

                // Try OpenAI fallback if Groq fails
                const statusDiv = document.getElementById('deepgram-status');
                statusDiv.innerHTML = '<span style="color: #FF9800;">🔄 Groq failed, trying OpenAI fallback...</span>';

                try {
                    // Get OpenAI API key
                    const envResult = await window.electronAPI.getApiKey('openai');
                    if (!envResult.success || !envResult.key) {
                        throw new Error('OpenAI API key not found');
                    }

                    const response = await fetch('https://api.openai.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${envResult.key}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: 'gpt-3.5-turbo',
                            messages: [
                                {
                                    role: 'system',
                                    content: 'You are an expert interview coach. Provide concise, professional answers to interview questions. Be direct, confident, and helpful. Focus on practical advice and clear explanations.'
                                },
                                {
                                    role: 'user',
                                    content: question
                                }
                            ],
                            max_tokens: 500,
                            temperature: 0.7
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        const aiResponse = data.choices[0].message.content;

                        addMessage(aiResponse, false);
                        statusDiv.innerHTML = '<span style="color: #4CAF50;">✅ OpenAI fallback response generated - Ready for next question</span>';
                    } else {
                        throw new Error(`OpenAI API error: ${response.status}`);
                    }

                } catch (fallbackError) {
                    addMessage('❌ **Both Groq and OpenAI failed:** ' + fallbackError.message, false);
                    statusDiv.innerHTML = '<span style="color: #f44336;">❌ All response generation failed</span>';
                }
            }
        }

        // 🔧 DEBUG ASSISTANT FUNCTIONS

        // Screenshot categorization using AI vision
        function categorizeScreenshot(base64Image) {
            // Simple heuristic categorization - can be enhanced with AI
            // For now, we'll let user manually categorize or auto-detect based on content
            return 'unknown';
        }

        // Add screenshot to debug session
        function addToDebugSession(screenshotPath, category = 'unknown') {
            debugSession.screenshots.push({
                path: screenshotPath,
                category: category,
                timestamp: Date.now()
            });

            console.log(`📸 Added screenshot to debug session: ${category}`);
            addMessage(`📸 **Screenshot added to debug session** (${category}). Total: ${debugSession.screenshots.length}`, false);
        }

        // Clear debug session
        function clearDebugSession() {
            debugSession = {
                screenshots: [],
                attempts: [],
                currentAttempt: 0,
                problemScreenshot: null,
                codeScreenshot: null,
                errorScreenshot: null
            };
            debugMode = false;
            console.log('🧹 Debug session cleared');
            addMessage('🧹 **Debug session cleared** - Ready for new debugging session.', false);
        }

        // Start debug mode
        function startDebugMode() {
            debugMode = true;
            console.log('🔧 Debug mode activated');
            addMessage('🔧 **DEBUG MODE ACTIVATED**\n\n📸 Take screenshots of:\n1. Problem statement\n2. Your current code\n3. Failed test cases/errors\n\nThen press Ctrl+Shift+D to analyze!', false);
        }

        // Smart debug mode activation
        function checkSmartDebugActivation() {
            // Auto-activate debug mode if user takes 3+ screenshots quickly (within 2 minutes)
            const recentScreenshots = debugSession.screenshots.filter(s =>
                Date.now() - s.timestamp < 120000 // 2 minutes
            );

            if (!debugMode && allScreenshots.length >= 3 && recentScreenshots.length === 0) {
                console.log('🤖 Smart debug mode activation - detected multiple screenshots');
                addMessage('🤖 **Smart Debug Mode** detected! You\'ve taken multiple screenshots. Debug mode auto-activated.\n\nPress Ctrl+Shift+D to analyze all screenshots for debugging!', false);
                debugMode = true;

                // Add recent screenshots to debug session
                const recentPaths = allScreenshots.slice(-3); // Last 3 screenshots
                recentPaths.forEach(path => {
                    addToDebugSession(path, 'smart-auto');
                });
            }
        }

        // Analyze debug session
        async function analyzeDebugSession() {
            if (debugSession.screenshots.length === 0) {
                addMessage('❌ No screenshots in debug session. Take screenshots first using Ctrl+H, then try again.', false);
                return;
            }

            console.log(`🔍 Analyzing debug session with ${debugSession.screenshots.length} screenshots`);
            addMessage(`🔍 **Analyzing debug session** with ${debugSession.screenshots.length} screenshots...`, false);

            showTypingIndicator();

            try {
                const base64Images = [];

                // Process all screenshots in debug session
                for (const screenshot of debugSession.screenshots) {
                    const base64Result = await window.electronAPI.readFileAsBase64(screenshot.path);
                    if (base64Result.success) {
                        base64Images.push(base64Result.data);
                    }
                }

                // Create debug-specific prompt
                const debugPrompt = `🔧 DEBUG ASSISTANT MODE:

You are analyzing a coding problem debugging session. The user has provided screenshots that may include:
1. Problem statement
2. Current code implementation
3. Failed test cases or error messages

Your task:
1. **Identify the problem** from the screenshots
2. **Analyze the current code** and find bugs/issues
3. **Compare with failed test cases** to understand what's wrong
4. **Provide specific fixes** - don't rewrite everything, just fix the bugs
5. **Explain why the code failed** and how the fix addresses it

Focus on:
- Logic errors
- Edge cases not handled
- Algorithm efficiency issues
- Syntax or runtime errors
- Off-by-one errors
- Boundary conditions

Provide targeted debugging advice and specific code fixes in ${getLanguageName(selectedLanguage)}.`;

                await smartAPICall(base64Images, debugPrompt, 'debugging');

                // Record this debugging attempt
                debugSession.attempts.push({
                    timestamp: Date.now(),
                    screenshotCount: base64Images.length,
                    attempt: debugSession.currentAttempt + 1
                });
                debugSession.currentAttempt++;

            } catch (error) {
                console.error('Error in debug analysis:', error);
                hideTypingIndicator();
                addMessage(`❌ Error in debug analysis: ${error.message}`, false);
            }
        }


        // Fast coding questions processing (Ctrl+R) - now uses selected screenshots
        async function processCodingQuestions() {
            console.log('processCodingQuestions called - fast mode');

            let selectedScreenshots = await validateSelectedScreenshots();

            // 🚀 SEAMLESS WORKFLOW: If no screenshots selected, auto-select latest for instant analysis
            if (selectedScreenshots.length === 0) {
                console.log('🎯 No screenshots selected - auto-selecting latest for fast processing');
                autoSelectLatestScreenshot();
                selectedScreenshots = await validateSelectedScreenshots();

                if (selectedScreenshots.length === 0) {
                    addMessage('❌ No screenshots available. Please take screenshots first using Ctrl+H.', false);
                    return;
                }

                console.log('✅ Auto-selected latest screenshot for fast processing');
            }

            addMessage(`Fast processing ${selectedScreenshots.length} selected screenshot(s) for coding questions...`, false);
            showTypingIndicator();

            try {
                const base64Images = [];
                for (let i = 0; i < selectedScreenshots.length; i++) {
                    const base64Result = await window.electronAPI.readFileAsBase64(selectedScreenshots[i]);
                    if (base64Result.success) {
                        base64Images.push(base64Result.data);
                    }
                }

                const fastPrompt = `FAST MODE: Analyze these coding question screenshots quickly. For each problem:
1. Extract the problem statement
2. Provide a concise solution approach
3. Give clean, working code in ${getLanguageName(selectedLanguage)}
4. Mention time/space complexity

Be direct and efficient - focus on getting the solution fast for interview scenarios.
IMPORTANT: Provide code solutions ONLY in ${getLanguageName(selectedLanguage)} programming language.`;

                await smartAPICall(base64Images, fastPrompt, 'fast-response');
            } catch (error) {
                console.error('Error in fast coding processing:', error);
                hideTypingIndicator();
                addMessage(`Error in fast processing: ${error.message}`, false);
            }
        }

        // MCQ/Guess-Output fast mode (Ctrl+Shift+S) - now uses selected screenshots
        async function processMCQFastMode() {
            console.log('processMCQFastMode called - super fast mode');

            let selectedScreenshots = await validateSelectedScreenshots();

            // 🚀 SEAMLESS WORKFLOW: If no screenshots selected, auto-select latest for instant analysis
            if (selectedScreenshots.length === 0) {
                console.log('🎯 No screenshots selected - auto-selecting latest for MCQ fast mode');
                autoSelectLatestScreenshot();
                selectedScreenshots = await validateSelectedScreenshots();

                if (selectedScreenshots.length === 0) {
                    addMessage('❌ No screenshots available. Please take screenshots first using Ctrl+H.', false);
                    return;
                }

                console.log('✅ Auto-selected latest screenshot for MCQ fast mode');
            }

            addMessage(`Super fast MCQ/Guess-Output processing for ${selectedScreenshots.length} selected screenshot(s)...`, false);
            showTypingIndicator();

            try {
                const base64Images = [];
                for (let i = 0; i < selectedScreenshots.length; i++) {
                    const base64Result = await window.electronAPI.readFileAsBase64(selectedScreenshots[i]);
                    if (base64Result.success) {
                        base64Images.push(base64Result.data);
                    }
                }

                const mcqPrompt = `SUPER FAST MODE: These are MCQ/Guess-Output questions. For each:
1. Identify the question type (MCQ/Output prediction)
2. Give the direct answer
3. Brief 1-line explanation

Be extremely concise - just the answer and minimal explanation for speed.`;

                await smartAPICall(base64Images, mcqPrompt, 'fast-response');
            } catch (error) {
                console.error('Error in MCQ fast processing:', error);
                hideTypingIndicator();
                addMessage(`❌ Error in MCQ processing: ${error.message}`, false);
            }
        }

        // Common Gemini API call function
        async function makeGeminiAPICall(base64Images, prompt) {
            if (!apiKey) {
                hideTypingIndicator();
                addMessage('Please set your API key in the settings (⚙️) to use this functionality.', false);
                return;
            }

            if (apiType !== 'gemini') {
                hideTypingIndicator();
                addMessage('Fast processing modes require Gemini API. Please switch to Gemini in settings.', false);
                return;
            }

            const parts = [{ text: prompt }];
            base64Images.forEach((base64Image) => {
                parts.push({
                    inline_data: {
                        mime_type: "image/png",
                        data: base64Image
                    }
                });
            });

            const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    contents: [{
                        parts: parts
                    }]
                })
            });

            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error?.message || `API error: ${response.status}`);
            }

            const reply = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response available.";
            hideTypingIndicator();
            addMessage(reply, false);
        }

        // Removed duplicate functions - keeping the ones below

        // Common Gemini API call function
        async function makeGeminiAPICall(base64Images, prompt) {
            if (!apiKey) {
                hideTypingIndicator();
                addMessage('Please set your API key in the settings (⚙️) to use this functionality.', false);
                return;
            }

            if (apiType !== 'gemini') {
                hideTypingIndicator();
                addMessage('Fast processing modes require Gemini API. Please switch to Gemini in settings.', false);
                return;
            }

            const parts = [{ text: prompt }];
            base64Images.forEach((base64Image) => {
                parts.push({
                    inline_data: {
                        mime_type: "image/png",
                        data: base64Image
                    }
                });
            });

            const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    contents: [{
                        parts: parts
                    }]
                })
            });

            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error?.message || `API error: ${response.status}`);
            }

            const reply = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response available.";
            hideTypingIndicator();
            addMessage(reply, false);
        }

        // DSA analysis function - moved to global scope
        async function analyzeDSAProblemsMultiple(base64Images) {
            try {
                const dsaPrompt = `You are an expert in Data Structures and Algorithms (DSA). I'm providing you with ${base64Images.length} screenshot(s) that may contain coding problems, algorithm questions, or DSA challenges.

Please analyze ALL the screenshots and:

1. **Identify** all coding problems, algorithm questions, or DSA challenges visible in the images
2. **Extract** problem statements, constraints, and examples from each screenshot
3. **Provide** complete solutions for each problem with:
   - Problem analysis and approach
   - Time and space complexity
   - Clean, well-commented code in ${getLanguageName(selectedLanguage)}
   - Step-by-step explanation of the solution
   - Alternative approaches if applicable

If multiple problems are found, clearly separate each solution with headers like "Problem 1:", "Problem 2:", etc.

If no DSA problems are found, explain what you see in the images and suggest how they might relate to programming or algorithms.

IMPORTANT: Provide ALL code solutions ONLY in ${getLanguageName(selectedLanguage)} programming language.
Please be thorough and educational in your response.`;

                // Use smart API call with screenshot analysis task type
                await smartAPICall(base64Images, dsaPrompt, 'screenshot-analysis');

            } catch (error) {
                console.error("Error in DSA analysis:", error);
                hideTypingIndicator();
                addMessage(`Error analyzing DSA problems: ${error.message}`, false);
            }
        }

        // Language selection function
        function selectLanguage(language) {
            selectedLanguage = language;
            console.log('Language selected:', language);

            // Update button states
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`${language === 'javascript' ? 'js' : language}-btn`).classList.add('active');

            // No message - silent language change
        }

        // Get language display name
        function getLanguageName(lang) {
            const names = {
                'cpp': 'C++',
                'java': 'Java',
                'python': 'Python',
                'javascript': 'JavaScript'
            };
            return names[lang] || lang;
        }

        // Settings functions
        function openSettings() {
            const settingsModal = document.getElementById('settings-modal');
            const modalOverlay = document.getElementById('modal-overlay');
            settingsModal.style.display = 'block';
            modalOverlay.style.display = 'block';

            // Focus the API type selector first for keyboard navigation
            setTimeout(() => {
                document.getElementById('api-type-select').focus();
                console.log('🎯 SETTINGS OPENED - KEYBOARD NAVIGATION:');
                console.log('• Tab/Shift+Tab: Navigate between fields');
                console.log('• ↑/↓ Arrow Keys: Change API provider');
                console.log('• Ctrl+W/G/O: Quick switch (Qwen/Gemini/OpenAI)');
                console.log('• Ctrl+Enter: Save settings');
                console.log('• Escape: Close settings');

                // Show helpful message in chat
                addMessage('⚙️ **SETTINGS OPENED - KEYBOARD NAVIGATION:**\n\n' +
                    '🔧 **API Selection:**\n' +
                    '• **↑/↓ Arrow Keys** → Change API provider\n' +
                    '• **Ctrl+W** → Quick switch to Qwen\n' +
                    '• **Ctrl+G** → Quick switch to Gemini\n' +
                    '• **Ctrl+O** → Quick switch to OpenAI\n\n' +
                    '📝 **Navigation:**\n' +
                    '• **Tab** → Move to next field\n' +
                    '• **Shift+Tab** → Move to previous field\n' +
                    '• **Ctrl+V** → Paste API key\n' +
                    '• **Ctrl+Enter** → Save settings\n' +
                    '• **Escape** → Close settings\n\n' +
                    '🎯 **Current Focus:** API Provider dropdown', false);
            }, 100);
        }

        function closeSettings() {
            const settingsModal = document.getElementById('settings-modal');
            const modalOverlay = document.getElementById('modal-overlay');
            settingsModal.style.display = 'none';
            modalOverlay.style.display = 'none';

            console.log('⚙️ Settings closed');
            addMessage('⚙️ **Settings closed** - Ready for analysis! Use Ctrl+S to reopen settings anytime.', false);
        }

        // Clear chat function
        function clearChat() {
            const chatContainer = document.getElementById('chat-container');
            chatContainer.innerHTML = '';
            console.log('Chat cleared');
        }

        // Show keyboard help function
        function showKeyboardHelp() {
            const helpText = `
🎯 **COMPLETE KEYBOARD SHORTCUTS - NO MOUSE NEEDED!**

**🚀 CORE NAVIGATION:**
• Ctrl+B → Toggle window visibility
• Ctrl+D → Switch to DSA mode
• Ctrl+C → Switch to Chatbot mode (auto-focuses input)
• Ctrl+Q → Quit app

**📸 DSA MODE (Ctrl+D):**
• Ctrl+H → Take screenshot
• Ctrl+Enter → Analyze selected screenshots (detailed)
• Ctrl+R → Fast coding questions processing (selected)
• Ctrl+Shift+S → MCQ/Guess-Output super fast mode (selected)
• 1/2/3/4 → Select language (C++/Java/Python/JS)
• ←/→ Arrow Keys → Navigate between screenshots
• S → Select current/first screenshot
• D → Deselect current/first screenshot
• A → Select all screenshots
• X → Deselect all screenshots

**🔧 DEBUG ASSISTANT:**
• Ctrl+Shift+D → Analyze debug session (Problem + Code + Errors)
• Ctrl+Shift+A → Add latest screenshot to debug session
• Ctrl+Shift+X → Clear debug session

**🎤 INTERVIEW COPILOT MODE:**
• Ctrl+N → Switch to Interview Copilot Mode
• Ctrl+Space → Start/Stop audio capture
• Ctrl+F → Mute/Unmute (emergency)
• Ctrl+Shift+R → Repeat last response

**💬 CHATBOT MODE (Ctrl+C):**
• Ctrl+I → Focus input field (with visual feedback)
• Ctrl+Enter → Send message from input field
• Enter → Send message (regular Enter key)

**📋 CODE COPYING (Both modes):**
• Ctrl+Shift+C → Copy last code block
• Ctrl+Shift+1 → Copy 1st code block
• Ctrl+Shift+2 → Copy 2nd code block
• Ctrl+Shift+3 → Copy 3rd code block
• (and so on up to Ctrl+Shift+9)

**⚙️ SETTINGS & API CONTROL:**
• Ctrl+S → Open settings modal
• Ctrl+W/G/O → Quick switch API (Qwen/Gemini/OpenAI)
• Ctrl+Alt+C → Switch to Anthropic Claude
• Ctrl+Alt+A → Toggle auto-select mode
• Ctrl+Alt+F → Toggle failover mode
• Ctrl+Alt+S → Show API status & performance
• Tab → Navigate forward (API Provider → API Key → Save Button)
• Shift+Tab → Navigate backward
• ↑/↓ Arrow Keys → Change API provider in dropdown
• Ctrl+V → Paste API key in input field
• Ctrl+Enter → Save settings
• Escape → Close settings

**📜 SIDEBAR NAVIGATION:**
• Ctrl+E → Focus sidebar for scrolling
• Ctrl++ → Scroll sidebar up
• Ctrl+- → Scroll sidebar down
• Escape → Return focus to main window



**🪟 WINDOW MANAGEMENT:**
• Ctrl+[ → Decrease opacity
• Ctrl+] → Increase opacity
• Ctrl+Arrow Keys → Move window (Left/Right/Up/Down)
• Ctrl+M → Shrink window (stealth mode)
• Ctrl+Shift+M → Reset window to original size
• Ctrl+B → Toggle visibility (auto-focused)
• Ctrl+Q → Quit app

**🛠️ UTILITY:**
• Ctrl+L → Clear chat
• Ctrl+? → Show this help

**✅ 100% KEYBOARD CONTROL - PERFECT FOR TESTS!**
            `;
            addMessage(helpText, false);
        }

        // Enhanced API provider switching function
        function switchAPIProvider(provider) {
            const apiTypeSelect = document.getElementById('api-type-select');
            apiTypeSelect.value = provider;
            apiType = provider;
            apiManager.currentProvider = provider;
            loadApiKey(provider);
            console.log('API provider switched to:', provider);
            addMessage(`🔄 **API switched to:** ${apiManager.providers[provider].name}`, false);
        }

        // Show API status and performance metrics
        function showAPIStatus() {
            let statusMessage = '📊 **API STATUS & PERFORMANCE:**\n\n';

            Object.entries(apiManager.providers).forEach(([key, api]) => {
                const status = api.available ? '✅' : '❌';
                const current = key === apiManager.currentProvider ? ' 🎯' : '';
                statusMessage += `${status} **${api.name}**${current}\n`;
                statusMessage += `   • Response Time: ${api.responseTime.toFixed(0)}ms\n`;
                statusMessage += `   • Success Rate: ${api.successRate}%\n`;
                statusMessage += `   • Requests: ${api.requestCount}\n`;
                statusMessage += `   • Specialty: ${api.specialty}\n\n`;
            });

            statusMessage += `🤖 **Auto-select:** ${apiManager.autoSelect ? 'ON' : 'OFF'}\n`;
            statusMessage += `🔄 **Failover:** ${apiManager.failoverEnabled ? 'ON' : 'OFF'}\n\n`;
            statusMessage += `**Quick Switch:**\n`;
            statusMessage += `• Ctrl+W → Qwen | Ctrl+G → Gemini\n`;
            statusMessage += `• Ctrl+O → OpenAI | Ctrl+Alt+C → Claude\n`;
            statusMessage += `• Ctrl+Alt+A → Toggle Auto-select\n`;
            statusMessage += `• Ctrl+Alt+F → Toggle Failover`;

            addMessage(statusMessage, false);
        }

        // Sidebar navigation functions
        let sidebarFocused = false;

        function focusSidebar() {
            const chatContainer = document.getElementById('chat-container');
            if (chatContainer) {
                chatContainer.focus();
                chatContainer.tabIndex = 0; // Make it focusable
                sidebarFocused = true;
                console.log('🎯 Sidebar focused - use Ctrl+./Ctrl+, to scroll, Escape to return');
                addMessage('🎯 **Sidebar focused** - Use Ctrl+. (up) / Ctrl+, (down) to scroll. Press Escape to return focus.', false);
            }
        }

        function scrollSidebar(direction) {
            const chatContainer = document.getElementById('chat-container');
            if (chatContainer && sidebarFocused) {
                const scrollAmount = 100; // pixels to scroll
                if (direction === 'up') {
                    chatContainer.scrollTop -= scrollAmount;
                    console.log('📜 Scrolling sidebar up');
                } else if (direction === 'down') {
                    chatContainer.scrollTop += scrollAmount;
                    console.log('📜 Scrolling sidebar down');
                }
            } else if (!sidebarFocused) {
                console.log('⚠️ Sidebar not focused - press Ctrl+E first');
                addMessage('⚠️ **Sidebar not focused** - Press Ctrl+E to focus sidebar first, then use Ctrl+./Ctrl+, to scroll.', false);
            }
        }

        // Enhanced API key loading function
        async function loadApiKey(type) {
            try {
                const result = await window.electronAPI.getApiKey(type);
                if (result.success && result.key) {
                    if (type === apiType) {
                        // Only update UI if this is the current API type
                        apiKey = result.key;
                        document.getElementById('api-key-input').value = apiKey;
                    }
                    console.log(`${type.toUpperCase()} API key loaded`);
                    return result.key;
                } else {
                    if (type === apiType) {
                        apiKey = '';
                        document.getElementById('api-key-input').value = '';
                    }
                    console.log(`No ${type.toUpperCase()} API key found`);
                    return null;
                }
            } catch (error) {
                console.error(`Error loading ${type} API key:`, error);
                return null;
            }
        }

        // Mode selection function
        function selectMode(mode) {
            // Cleanup previous mode if switching from interview mode
            if (currentMode === 'interview' && mode !== 'interview') {
                console.log('🔄 Switching from interview mode, cleaning up...');
                cleanupInterviewMode();
            }

            currentMode = mode;
            const controlsText = document.getElementById('controls-text');
            const dsaBtn = document.getElementById('dsa-btn');
            const chatbotBtn = document.getElementById('chatbot-btn');
            const interviewBtn = document.getElementById('interview-btn');
            const languageSelector = document.getElementById('language-selector');

            // Update button states
            dsaBtn.classList.remove('active');
            chatbotBtn.classList.remove('active');
            if (interviewBtn) interviewBtn.classList.remove('active');

            if (mode === 'dsa') {
                dsaBtn.classList.add('active');
                languageSelector.style.display = 'block'; // Show language selector
                controlsText.textContent = 'DSA Mode: Screenshot: Ctrl+H | Analyze Selected: Ctrl+Enter | Fast: Ctrl+R | MCQ: Ctrl+Shift+S | Debug: Ctrl+Shift+D/A/X | Lang: 1/2/3/4 | Navigate: ←/→ | Select: S/A | Deselect: D/X | Copy: Ctrl+Shift+C/1-9 | Chat: Ctrl+C | Clear: Ctrl+L | Settings: Ctrl+S | API: Ctrl+Alt+1/2/3 | Help: Ctrl+? | Visibility: Ctrl+B | Opacity: Ctrl+[/] | Move: Ctrl+Arrows | Quit: Ctrl+Q';

                // Clear chat when switching to DSA mode
                const chatContainer = document.getElementById('chat-container');
                chatContainer.innerHTML = '';

                // Clear screenshots array when switching to DSA mode
                allScreenshots = [];
                selectedScreenshot = null;
                console.log('Screenshots cleared for new DSA session');

                // Clean up any broken screenshot references after a short delay
                setTimeout(() => {
                    cleanupBrokenScreenshots();
                }, 1000);

                // Update conversation history for DSA mode
                conversationHistory = [
                    { role: "system", content: "You are an expert DSA (Data Structures and Algorithms) tutor. Analyze coding problems and provide detailed solutions with explanations, complexity analysis, and clean code." }
                ];
            } else if (mode === 'interview') {
                if (interviewBtn) interviewBtn.classList.add('active');
                languageSelector.style.display = 'none'; // Hide language selector
                controlsText.textContent = 'Interview Copilot Mode (Deepgram + Groq): Use START/STOP LISTEN buttons | DSA: Ctrl+D | Chat: Ctrl+C | Interview: Ctrl+N | Clear: Ctrl+L | Settings: Ctrl+S | API: Ctrl+Alt+C/W/G/O | Help: Ctrl+? | Visibility: Ctrl+B | Quit: Ctrl+Q';

                // Clear chat when switching to interview mode
                const chatContainer = document.getElementById('chat-container');
                chatContainer.innerHTML = '';

                // Clean Interview Copilot Mode - ONLY Essential Buttons
                const interviewButtonsHTML = `
                    <div style="padding: 20px; border-bottom: 2px solid #333; margin-bottom: 20px;">
                        <h3 style="color: #00BCD4; margin-bottom: 20px;">🎤 Interview Copilot Mode</h3>

                        <!-- Essential Listen Buttons Only -->
                        <div style="padding: 25px; background: #0a0a0a; border-radius: 10px; border: 2px solid #00BCD4; text-align: center;">
                            <div style="margin-bottom: 20px;">
                                <button id="start-listen-btn" onclick="startSimpleListening()"
                                        style="background: #4CAF50; color: white; border: none; padding: 20px 40px;
                                               border-radius: 10px; font-size: 18px; font-weight: bold; margin-right: 20px; cursor: pointer;">
                                    🎤 START LISTEN
                                </button>
                                <button id="stop-listen-btn" onclick="stopSimpleListening()"
                                        style="background: #FF5722; color: white; border: none; padding: 20px 40px;
                                               border-radius: 10px; font-size: 18px; font-weight: bold; cursor: pointer;" disabled>
                                    ⏹️ STOP LISTEN
                                </button>
                            </div>
                            <div id="listen-status" style="color: #888; font-size: 16px;">
                                Ready to listen for your question
                            </div>
                        </div>
                    </div>
                `;
                chatContainer.innerHTML = interviewButtonsHTML;

                // Initialize interview mode
                interviewMode.active = true;
                interviewMode.useDeepgram = true;
                interviewMode.currentTranscript = '';

                // Initialize Deepgram immediately
                initializeDeepgram();

                // Update conversation history for interview mode
                conversationHistory = [
                    { role: "system", content: "You are an expert interview coach helping a candidate answer interview questions professionally and confidently." }
                ];
            } else {
                chatbotBtn.classList.add('active');
                languageSelector.style.display = 'none'; // Hide language selector
                controlsText.textContent = 'Chatbot Mode: Focus: Ctrl+I | Send: Ctrl+Enter/Enter | DSA: Ctrl+D | Interview: Ctrl+N | Copy: Ctrl+Shift+C/1-9 | Clear: Ctrl+L | Settings: Ctrl+S (Tab/Shift+Tab) | API: Ctrl+Alt+1/2/3 | Help: Ctrl+? | Visibility: Ctrl+B | Opacity: Ctrl+[/] | Move: Ctrl+Arrows | Quit: Ctrl+Q';

                // Clear chat when switching to chatbot mode
                const chatContainer = document.getElementById('chat-container');
                chatContainer.innerHTML = '';

                // Update conversation history for chatbot mode
                conversationHistory = [
                    { role: "system", content: "You are a helpful AI assistant. Provide clear, helpful responses to user questions." }
                ];

                // Auto-focus input field when switching to chatbot mode
                setTimeout(() => {
                    if (focusInputField(true)) {
                        console.log('Auto-focused input field in chatbot mode');
                    } else {
                        console.error('Failed to auto-focus input field');
                    }
                }, 200); // Increased delay to ensure DOM is ready
            }
        }

        // 🚀 BULLETPROOF FALLBACK SHORTCUT SYSTEM
        let fallbackShortcutsEnabled = false;
        let shortcutStats = { failed: 0, total: 0 };

        // Listen for fallback shortcut registration from main process
        if (window.electronAPI && window.electronAPI.onRegisterFallbackShortcuts) {
            window.electronAPI.onRegisterFallbackShortcuts((data) => {
                console.log('🔧 Enabling fallback shortcut system', data);
                fallbackShortcutsEnabled = true;
                shortcutStats = data || { failed: 0, total: 0 };

                if (shortcutStats.failed > 0) {
                    addMessage(`🔧 **Fallback mode enabled** - ${shortcutStats.failed}/${shortcutStats.total} global shortcuts failed. Using enhanced fallback system.`, false);
                    addMessage('💡 **Tip:** Run as Administrator for full global shortcut support', false);
                } else {
                    addMessage('🔧 **Enhanced shortcut system** - All shortcuts available', false);
                }
            });
        }

        // 🚀 BULLETPROOF KEYBOARD EVENT HANDLER WITH COMPLETE FALLBACK SYSTEM
        document.addEventListener('keydown', (e) => {
            // Only log important shortcuts, not every key press
            const isImportantShortcut = e.ctrlKey && ['h', 'b', 'q', 'd', 'c', 'n', 'Enter', 'r', 'm', 'l'].includes(e.key);
            if (isImportantShortcut) {
                console.log(`🎯 Shortcut: ${e.ctrlKey ? 'Ctrl+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.key}`);
            }

            // 🎯 ESSENTIAL SHORTCUTS (HIGHEST PRIORITY)

            // 📸 CTRL+H - SCREENSHOT (CRITICAL) - BULLETPROOF FALLBACK
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                console.log('🔥 Renderer Ctrl+H - Taking screenshot (fallback)');

                if (window.electronAPI && window.electronAPI.takeScreenshot) {
                    window.electronAPI.takeScreenshot().then(result => {
                        if (result && result.success) {
                            console.log('✅ Screenshot taken via renderer fallback');
                            addMessage('📸 **Screenshot captured!** - Ready for analysis', false);
                        } else {
                            console.log('❌ Screenshot failed:', result);
                            addMessage('❌ **Screenshot failed** - Try running as Administrator or check permissions', false);
                        }
                    }).catch(error => {
                        console.error('❌ Screenshot error:', error);
                        addMessage('❌ **Screenshot error** - Try running as Administrator or restart app', false);
                    });
                } else {
                    console.error('❌ electronAPI.takeScreenshot not available');
                    addMessage('❌ **Screenshot API not available** - Try restarting the application', false);
                }
                return;
            }

            // 🎮 CTRL+B - TOGGLE WINDOW (CRITICAL)
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                if (window.electronAPI && window.electronAPI.toggleWindow) {
                    window.electronAPI.toggleWindow();
                }
                return;
            }

            // 🚪 CTRL+Q - QUIT APP (CRITICAL)
            if (e.ctrlKey && e.key === 'q') {
                e.preventDefault();
                if (window.electronAPI && window.electronAPI.quitApp) {
                    window.electronAPI.quitApp();
                }
                return;
            }

            // 🎮 MODE SWITCHING SHORTCUTS
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                selectMode('dsa');
                return;
            }

            if (e.ctrlKey && e.key === 'c') {
                e.preventDefault();
                selectMode('chatbot');
                return;
            }

            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                selectMode('interview');
                return;
            }

            // 🏃‍♂️ FAST PROCESSING SHORTCUTS
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                console.log('🔥 Ctrl+Enter detected in renderer');
                if (currentMode === 'dsa') {
                    analyzeSelectedScreenshots();
                } else {
                    // Handle in other modes if needed
                    console.log('Ctrl+Enter in non-DSA mode');
                }
                return;
            }

            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                console.log('🔥 Ctrl+R detected in renderer - Fast coding');
                if (currentMode === 'dsa') {
                    processCodingQuestions();
                }
                return;
            }

            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                console.log('🔥 Ctrl+Shift+S detected in renderer - MCQ fast mode');
                if (currentMode === 'dsa') {
                    processMCQFastMode();
                }
                return;
            }

            // 🎯 WINDOW CONTROL SHORTCUTS
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                console.log('🔥 Ctrl+M detected in renderer - Shrink window');
                if (window.electronAPI && window.electronAPI.shrinkWindow) {
                    window.electronAPI.shrinkWindow().then(result => {
                        if (result.success) {
                            if (result.percentage) {
                                addMessage(`🔧 **Window shrunk to ${result.percentage}%** - Press Ctrl+Shift+M to reset`, false);
                            } else {
                                addMessage('⚠️ **Window already at minimum size** - Press Ctrl+Shift+M to reset', false);
                            }
                        }
                    });
                }
                return;
            }

            if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                e.preventDefault();
                console.log('🔥 Ctrl+Shift+M detected in renderer - Reset window');
                if (window.electronAPI && window.electronAPI.resetWindowSize) {
                    window.electronAPI.resetWindowSize().then(result => {
                        if (result.success) {
                            addMessage('🔧 **Window reset to original size** - Ready for normal use!', false);
                        }
                    });
                }
                return;
            }

            if (e.ctrlKey && e.key === '[') {
                e.preventDefault();
                console.log('🔥 Ctrl+[ detected in renderer - Decrease opacity');
                if (window.electronAPI && window.electronAPI.decreaseOpacity) {
                    window.electronAPI.decreaseOpacity();
                }
                return;
            }

            if (e.ctrlKey && e.key === ']') {
                e.preventDefault();
                console.log('🔥 Ctrl+] detected in renderer - Increase opacity');
                if (window.electronAPI && window.electronAPI.increaseOpacity) {
                    window.electronAPI.increaseOpacity();
                }
                return;
            }

            // 🔄 API SWITCHING SHORTCUTS
            if (e.ctrlKey && e.key === 'o') {
                e.preventDefault();
                console.log('🔥 Ctrl+O detected in renderer - OpenAI');
                switchAPIProvider('openai');
                return;
            }

            if (e.ctrlKey && e.key === 'g') {
                e.preventDefault();
                console.log('🔥 Ctrl+G detected in renderer - Gemini');
                switchAPIProvider('gemini');
                return;
            }

            if (e.ctrlKey && e.key === 'w') {
                e.preventDefault();
                console.log('🔥 Ctrl+W detected in renderer - Qwen');
                switchAPIProvider('qwen');
                return;
            }

            if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                console.log('🔥 Ctrl+Shift+A detected in renderer - Anthropic');
                switchAPIProvider('anthropic');
                return;
            }

            // 🎤 INTERVIEW MODE SHORTCUTS
            if (e.ctrlKey && e.shiftKey && e.key === 'L') {
                e.preventDefault();
                console.log('🔥 Ctrl+Shift+L detected in renderer - Toggle listening');
                if (currentMode === 'interview') {
                    toggleListening();
                }
                return;
            }

            // 💬 CTRL+L - CLEAR CHAT (ALL MODES)
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                console.log('🔥 Ctrl+L detected in renderer - Clear chat');
                clearChat();
                return;
            }

            // 📋 CODE COPYING SHORTCUTS
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                console.log('🔥 Ctrl+Shift+C detected in renderer - Copy last code');
                const lastCodeBlock = document.querySelector('code[id^="code_"]:last-of-type');
                if (lastCodeBlock) {
                    copyCode(lastCodeBlock.id);
                } else {
                    console.log('No code block found to copy');
                }
                return;
            }

            // 📋 NUMBERED CODE COPYING (Ctrl+Shift+1-5)
            if (e.ctrlKey && e.shiftKey && /^[1-5]$/.test(e.key)) {
                e.preventDefault();
                console.log(`🔥 Ctrl+Shift+${e.key} detected in renderer - Copy code block ${e.key}`);
                const codeBlocks = document.querySelectorAll('code[id^="code_"]');
                const index = parseInt(e.key) - 1;
                if (codeBlocks[index]) {
                    copyCode(codeBlocks[index].id);
                    console.log(`Copied code block ${e.key}`);
                } else {
                    console.log(`No code block ${e.key} found`);
                }
                return;
            }

            // 🎯 WINDOW MOVEMENT SHORTCUTS
            if (e.ctrlKey && e.key === 'ArrowLeft') {
                e.preventDefault();
                console.log('🔥 Ctrl+Left detected in renderer - Move left');
                if (window.electronAPI && window.electronAPI.moveWindowLeft) {
                    window.electronAPI.moveWindowLeft();
                }
                return;
            }

            if (e.ctrlKey && e.key === 'ArrowRight') {
                e.preventDefault();
                console.log('🔥 Ctrl+Right detected in renderer - Move right');
                if (window.electronAPI && window.electronAPI.moveWindowRight) {
                    window.electronAPI.moveWindowRight();
                }
                return;
            }

            if (e.ctrlKey && e.key === 'ArrowUp') {
                e.preventDefault();
                console.log('🔥 Ctrl+Up detected in renderer - Move up');
                if (window.electronAPI && window.electronAPI.moveWindowUp) {
                    window.electronAPI.moveWindowUp();
                }
                return;
            }

            if (e.ctrlKey && e.key === 'ArrowDown') {
                e.preventDefault();
                console.log('🔥 Ctrl+Down detected in renderer - Move down');
                if (window.electronAPI && window.electronAPI.moveWindowDown) {
                    window.electronAPI.moveWindowDown();
                }
                return;
            }

            // ✅ Mode switching handled above in enhanced system

            // 🎤 INTERVIEW COPILOT MODE SHORTCUTS
            if (currentMode === 'interview') {
                // Ctrl+Space for start/stop listening
                if (e.ctrlKey && e.key === ' ') {
                    e.preventDefault();

                    if (e.shiftKey) {
                        // Ctrl+Shift+Space - Force system audio capture
                        forceSystemAudioCapture();
                    } else {
                        // Ctrl+Space - Normal listening
                        if (interviewMode.isListening) {
                            stopListening();
                        } else {
                            startListening();
                        }
                    }
                }

                // Ctrl+F for mute/unmute
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    toggleInterviewMute();
                }

                // Ctrl+Shift+R for repeat last response
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    repeatLastResponse();
                }

                // Ctrl+X for Stereo Mix capture (only in interview mode)
                if (e.ctrlKey && e.key === 'x') {
                    e.preventDefault();
                    captureStereoMix();
                }
            }

            // Language selection shortcuts (only in DSA mode)
            if (currentMode === 'dsa') {
                if (e.key === '1') {
                    e.preventDefault();
                    selectLanguage('cpp');
                } else if (e.key === '2') {
                    e.preventDefault();
                    selectLanguage('java');
                } else if (e.key === '3') {
                    e.preventDefault();
                    selectLanguage('python');
                } else if (e.key === '4') {
                    e.preventDefault();
                    selectLanguage('javascript');
                }

                // Screenshot selection shortcuts (only in DSA mode)
                if (e.key === 's' || e.key === 'S') {
                    e.preventDefault();
                    selectCurrentScreenshot();
                } else if (e.key === 'd' || e.key === 'D') {
                    e.preventDefault();
                    deselectCurrentScreenshot();
                } else if (e.key === 'a' || e.key === 'A') {
                    e.preventDefault();
                    selectAllScreenshots();
                } else if (e.key === 'x' || e.key === 'X') {
                    e.preventDefault();
                    deselectAllScreenshots();
                }
            }

            // Ctrl+Shift+C for copying the last code block (ChatGPT-style)
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();

                // First try to find ChatGPT-style code blocks
                const chatGPTCodeBlocks = document.querySelectorAll('.ai-response pre code');
                if (chatGPTCodeBlocks.length > 0) {
                    const lastCodeBlock = chatGPTCodeBlocks[chatGPTCodeBlocks.length - 1];
                    let codeText = lastCodeBlock.textContent || lastCodeBlock.innerText;

                    // Clean up the code text
                    codeText = cleanCodeText(codeText);

                    copyToClipboardWithFallback(codeText).then(() => {
                        console.log('✅ ChatGPT-style code copied to clipboard');
                        addMessage('📋 **Code copied to clipboard!**', false);
                    }).catch(err => {
                        console.error('❌ Failed to copy code:', err);
                        addMessage('❌ **Failed to copy code**', false);
                    });
                } else {
                    // Fallback to old-style code blocks
                    const oldCodeBlocks = document.querySelector('code[id^="code_"]:last-of-type');
                    if (oldCodeBlocks) {
                        copyCode(oldCodeBlocks.id);
                    } else {
                        console.log('No code block found to copy');
                        addMessage('❌ **No code block found to copy**', false);
                    }
                }
            }

            // Ctrl+Shift+V for copying the most recent code (alternative shortcut)
            if (e.ctrlKey && e.shiftKey && e.key === 'V') {
                e.preventDefault();

                // First try to find ChatGPT-style code blocks
                const chatGPTCodeBlocks = document.querySelectorAll('.ai-response pre code');
                if (chatGPTCodeBlocks.length > 0) {
                    const lastCodeBlock = chatGPTCodeBlocks[chatGPTCodeBlocks.length - 1];
                    let codeText = lastCodeBlock.textContent || lastCodeBlock.innerText;

                    // Clean up the code text
                    codeText = cleanCodeText(codeText);

                    copyToClipboardWithFallback(codeText).then(() => {
                        console.log('✅ ChatGPT-style code copied to clipboard (Ctrl+Shift+V)');
                        addMessage('📋 **Code copied to clipboard!**', false);
                    }).catch(err => {
                        console.error('❌ Failed to copy code:', err);
                        addMessage('❌ **Failed to copy code**', false);
                    });
                } else {
                    // Fallback to old-style code blocks
                    const oldCodeBlocks = document.querySelector('code[id^="code_"]:last-of-type');
                    if (oldCodeBlocks) {
                        copyCode(oldCodeBlocks.id);
                    } else {
                        console.log('No code block found to copy');
                        addMessage('❌ **No code block found to copy**', false);
                    }
                }
            }

            // Ctrl+Shift+1/2/3... for copying specific code blocks
            if (e.ctrlKey && e.shiftKey && /^[1-9]$/.test(e.key)) {
                e.preventDefault();
                const index = parseInt(e.key) - 1;

                // First try ChatGPT-style code blocks
                const chatGPTCodeBlocks = document.querySelectorAll('.ai-response pre code');
                if (chatGPTCodeBlocks[index]) {
                    let codeText = chatGPTCodeBlocks[index].textContent || chatGPTCodeBlocks[index].innerText;

                    // Clean up the code text
                    codeText = cleanCodeText(codeText);

                    copyToClipboardWithFallback(codeText).then(() => {
                        console.log(`✅ ChatGPT-style code block ${e.key} copied to clipboard`);
                        addMessage(`📋 **Code block ${e.key} copied to clipboard!**`, false);
                    }).catch(err => {
                        console.error('❌ Failed to copy code:', err);
                        addMessage('❌ **Failed to copy code**', false);
                    });
                } else {
                    // Fallback to old-style code blocks
                    const oldCodeBlocks = document.querySelectorAll('code[id^="code_"]');
                    if (oldCodeBlocks[index]) {
                        copyCode(oldCodeBlocks[index].id);
                        console.log(`Copied old-style code block ${e.key}`);
                    } else {
                        console.log(`No code block ${e.key} found`);
                        addMessage(`❌ **No code block ${e.key} found**`, false);
                    }
                }
            }

            // Screenshot navigation in DSA mode (Arrow keys when no input focused)
            if (currentMode === 'dsa' && !document.activeElement.matches('input, textarea, select')) {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault();
                    const screenshots = document.querySelectorAll('.message img[src^="file://"]');
                    if (screenshots.length > 0) {
                        let currentIndex = -1;
                        screenshots.forEach((img, index) => {
                            if (img.classList.contains('screenshot-selected')) {
                                currentIndex = index;
                            }
                        });

                        let newIndex;
                        if (e.key === 'ArrowLeft') {
                            newIndex = currentIndex > 0 ? currentIndex - 1 : screenshots.length - 1;
                        } else {
                            newIndex = currentIndex < screenshots.length - 1 ? currentIndex + 1 : 0;
                        }

                        // Clear previous selection
                        screenshots.forEach(img => {
                            img.style.border = '2px solid transparent';
                            img.classList.remove('screenshot-selected');
                        });

                        // Select new screenshot
                        screenshots[newIndex].style.border = '2px solid #4299e1';
                        screenshots[newIndex].classList.add('screenshot-selected');
                        selectedScreenshot = normalizeFilePath(screenshots[newIndex].src);
                        console.log(`Screenshot ${newIndex + 1} selected via keyboard`);

                        // Scroll to selected screenshot
                        screenshots[newIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }

                // Space or Enter to select first screenshot if none selected
                if ((e.key === ' ' || e.key === 'Enter') && !e.ctrlKey) {
                    e.preventDefault();
                    const screenshots = document.querySelectorAll('.message img[src^="file://"]');
                    if (screenshots.length > 0) {
                        const hasSelected = Array.from(screenshots).some(img => img.classList.contains('screenshot-selected'));
                        if (!hasSelected) {
                            screenshots[0].style.border = '2px solid #4299e1';
                            screenshots[0].classList.add('screenshot-selected');
                            selectedScreenshot = normalizeFilePath(screenshots[0].src);
                            console.log('First screenshot selected via keyboard');
                        }
                    }
                }
            }

            // Ctrl+B for toggling window visibility
            if (e.ctrlKey && e.key === 'b') {
                console.log('Ctrl+B pressed - toggling window visibility');
                e.preventDefault();
                window.electronAPI.toggleWindow();
            }

            // Ctrl+[ for decreasing opacity
            if (e.ctrlKey && e.key === '[') {
                console.log('Ctrl+[ pressed - decreasing opacity');
                e.preventDefault();
                window.electronAPI.decreaseOpacity();
            }

            // Ctrl+] for increasing opacity
            if (e.ctrlKey && e.key === ']') {
                console.log('Ctrl+] pressed - increasing opacity');
                e.preventDefault();
                window.electronAPI.increaseOpacity();
            }

            // Ctrl+R for processing coding questions (fast mode)
            if (e.ctrlKey && e.key === 'r') {
                console.log('Ctrl+R pressed - processing coding questions (fast mode)');
                e.preventDefault();
                if (currentMode === 'dsa') {
                    // Just call the function - it will handle validation internally
                    processCodingQuestions();
                } else {
                    addMessage('❌ Coding question processing is only available in DSA Mode. Press Ctrl+D to switch.', false);
                }
            }

            // Ctrl+Shift+S for MCQ/Guess-Output fast mode
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                console.log('Ctrl+Shift+S pressed - MCQ/Guess-Output fast mode');
                e.preventDefault();
                if (currentMode === 'dsa') {
                    // Just call the function - it will handle validation internally
                    processMCQFastMode();
                } else {
                    addMessage('❌ MCQ processing is only available in DSA Mode. Press Ctrl+D to switch.', false);
                }
            }

            // 🔧 DEBUG ASSISTANT KEYBOARD SHORTCUTS

            // Ctrl+Shift+D for debug mode analysis
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                console.log('Ctrl+Shift+D pressed - Debug Assistant');
                e.preventDefault();
                if (currentMode === 'dsa') {
                    analyzeDebugSession();
                } else {
                    addMessage('❌ Debug Assistant is only available in DSA Mode. Press Ctrl+D to switch.', false);
                }
            }

            // Ctrl+Shift+A for adding screenshot to debug session
            if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                console.log('Ctrl+Shift+A pressed - Add to debug session');
                e.preventDefault();
                if (currentMode === 'dsa') {
                    // Add latest screenshot to debug session
                    const screenshots = document.querySelectorAll('.message img[src^="file://"]');
                    if (screenshots.length > 0) {
                        const latestScreenshot = screenshots[screenshots.length - 1];
                        const path = normalizeFilePath(latestScreenshot.src);
                        addToDebugSession(path, 'manual');
                    } else {
                        addMessage('❌ No screenshots available. Take a screenshot first using Ctrl+H.', false);
                    }
                } else {
                    addMessage('❌ Debug Assistant is only available in DSA Mode. Press Ctrl+D to switch.', false);
                }
            }

            // Ctrl+Shift+X for clearing debug session
            if (e.ctrlKey && e.shiftKey && e.key === 'X') {
                console.log('Ctrl+Shift+X pressed - Clear debug session');
                e.preventDefault();
                if (currentMode === 'dsa') {
                    clearDebugSession();
                } else {
                    addMessage('❌ Debug Assistant is only available in DSA Mode. Press Ctrl+D to switch.', false);
                }
            }

            // Ctrl+Q for quitting app
            if (e.ctrlKey && e.key === 'q') {
                console.log('Ctrl+Q pressed - quitting app');
                e.preventDefault();
                window.electronAPI.quitApp();
            }

            // Ctrl+S for opening settings
            if (e.ctrlKey && e.key === 's') {
                console.log('Ctrl+S pressed - opening settings');
                e.preventDefault();
                openSettings();
            }

            // Escape for closing settings modal
            if (e.key === 'Escape') {
                const settingsModal = document.getElementById('settings-modal');
                if (settingsModal && settingsModal.style.display === 'block') {
                    console.log('Escape pressed - closing settings');
                    e.preventDefault();
                    closeSettings();
                }
            }

            // Ctrl+L for clearing chat
            if (e.ctrlKey && e.key === 'l') {
                console.log('Ctrl+L pressed - clearing chat');
                e.preventDefault();
                clearChat();
            }



            // Quick API switching shortcuts (work in settings and globally)
            if (e.ctrlKey && e.key === 'w') {
                console.log('Ctrl+W pressed - switching to Qwen');
                e.preventDefault();
                switchAPIProvider('qwen');
            }

            if (e.ctrlKey && e.key === 'g') {
                console.log('Ctrl+G pressed - switching to Gemini');
                e.preventDefault();
                switchAPIProvider('gemini');
            }

            if (e.ctrlKey && e.key === 'o') {
                console.log('Ctrl+O pressed - switching to OpenAI');
                e.preventDefault();
                switchAPIProvider('openai');
            }

            // 🚀 ENHANCED API SHORTCUTS

            // Ctrl+Alt+C for Anthropic Claude
            if (e.ctrlKey && e.altKey && e.key === 'c') {
                console.log('Ctrl+Alt+C pressed - switching to Anthropic Claude');
                e.preventDefault();
                switchAPIProvider('anthropic');
            }

            // Ctrl+Alt+A for Auto-select mode toggle
            if (e.ctrlKey && e.altKey && e.key === 'a') {
                console.log('Ctrl+Alt+A pressed - toggling auto-select mode');
                e.preventDefault();
                apiManager.autoSelect = !apiManager.autoSelect;
                addMessage(`🤖 **Auto-select mode:** ${apiManager.autoSelect ? 'ON' : 'OFF'} - ${apiManager.autoSelect ? 'AI will choose best API for each task' : 'Manual API selection only'}`, false);
            }

            // Ctrl+Alt+F for Failover toggle
            if (e.ctrlKey && e.altKey && e.key === 'f') {
                console.log('Ctrl+Alt+F pressed - toggling failover mode');
                e.preventDefault();
                apiManager.failoverEnabled = !apiManager.failoverEnabled;
                addMessage(`🔄 **Failover mode:** ${apiManager.failoverEnabled ? 'ON' : 'OFF'} - ${apiManager.failoverEnabled ? 'Auto-switch to backup API on failure' : 'No automatic failover'}`, false);
            }

            // Ctrl+Alt+S for API status
            if (e.ctrlKey && e.altKey && e.key === 's') {
                console.log('Ctrl+Alt+S pressed - showing API status');
                e.preventDefault();
                showAPIStatus();
            }

            // Sidebar navigation shortcuts
            if (e.ctrlKey && e.key === 'e') {
                console.log('Ctrl+E pressed - focusing sidebar');
                e.preventDefault();
                focusSidebar();
            }

            if (e.ctrlKey && e.key === '.') {
                console.log('Ctrl+. pressed - scrolling sidebar up');
                e.preventDefault();
                scrollSidebar('up');
            }

            if (e.ctrlKey && e.key === ',') {
                console.log('Ctrl+, pressed - scrolling sidebar down');
                e.preventDefault();
                scrollSidebar('down');
            }

            // Escape to return focus from sidebar to main window
            if (e.key === 'Escape' && sidebarFocused) {
                console.log('Escape pressed - returning focus to main window');
                e.preventDefault();
                sidebarFocused = false;
                document.body.focus();
                addMessage('🎯 **Focus returned to main window** - Sidebar navigation disabled.', false);
            }

            // Window resizing shortcuts
            if (e.ctrlKey && e.key === 'm') {
                console.log('🔥 RENDERER Ctrl+M pressed - shrinking window');
                e.preventDefault();
                window.electronAPI.shrinkWindow().then(result => {
                    console.log('Shrink window result:', result);
                    if (result.success) {
                        if (result.percentage) {
                            addMessage(`🔧 **Window shrunk to ${result.percentage}% (${result.size.width}x${result.size.height})** - Press Ctrl+Shift+M to reset`, false);
                        } else {
                            addMessage('⚠️ **Window already at minimum size (150x100)** - Press Ctrl+Shift+M to reset', false);
                        }
                    } else {
                        addMessage('❌ **Failed to shrink window** - ' + (result.message || 'Unknown error'), false);
                    }
                }).catch(error => {
                    console.error('Error shrinking window:', error);
                    addMessage('❌ **Error shrinking window** - ' + error.message, false);
                });
            }

            if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                console.log('🔥 RENDERER Ctrl+Shift+M pressed - resetting window size');
                e.preventDefault();
                window.electronAPI.resetWindowSize().then(result => {
                    console.log('Reset window result:', result);
                    if (result.success) {
                        addMessage('🔧 **Window reset to original size** - Ready for normal use!', false);
                    } else {
                        addMessage('❌ **Failed to reset window** - ' + (result.message || 'Unknown error'), false);
                    }
                }).catch(error => {
                    console.error('Error resetting window:', error);
                    addMessage('❌ **Error resetting window** - ' + error.message, false);
                });
            }

            // Settings modal navigation (when settings is open)
            const settingsModal = document.getElementById('settings-modal');
            if (settingsModal && settingsModal.style.display === 'block') {
                // Tab to cycle through settings fields
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const apiTypeSelect = document.getElementById('api-type-select');
                    const apiKeyInput = document.getElementById('api-key-input');
                    const saveButton = document.getElementById('save-settings');

                    if (document.activeElement === apiTypeSelect) {
                        apiKeyInput.focus();
                        console.log('🎯 Focused API key input - paste your key here!');
                        addMessage('📝 **API Key Input focused** - Paste your API key (Ctrl+V) then press Tab or Ctrl+Enter to save!', false);
                    } else if (document.activeElement === apiKeyInput) {
                        saveButton.focus();
                        console.log('🎯 Focused save button - press Enter or Ctrl+Enter to save!');
                        addMessage('💾 **Save button focused** - Press Enter or Ctrl+Enter to save your API key!', false);
                    } else {
                        apiTypeSelect.focus();
                        console.log('🎯 Focused API type selector - use arrow keys to change!');
                        addMessage('🔧 **API Provider focused** - Use ↑/↓ arrow keys to change provider or Ctrl+W/G/O for quick switch!', false);
                    }
                }

                // Shift+Tab for reverse navigation
                if (e.shiftKey && e.key === 'Tab') {
                    e.preventDefault();
                    const apiTypeSelect = document.getElementById('api-type-select');
                    const apiKeyInput = document.getElementById('api-key-input');
                    const saveButton = document.getElementById('save-settings');

                    if (document.activeElement === saveButton) {
                        apiKeyInput.focus();
                        console.log('🎯 Focused API key input (reverse navigation)');
                        addMessage('📝 **API Key Input focused** - Paste your API key here!', false);
                    } else if (document.activeElement === apiKeyInput) {
                        apiTypeSelect.focus();
                        console.log('🎯 Focused API type selector (reverse navigation)');
                        addMessage('🔧 **API Provider focused** - Use ↑/↓ arrow keys to change!', false);
                    } else {
                        saveButton.focus();
                        console.log('🎯 Focused save button (reverse navigation)');
                        addMessage('💾 **Save button focused** - Press Enter to save!', false);
                    }
                }

                // Enter to activate focused element in settings
                if (e.key === 'Enter' && document.activeElement === document.getElementById('save-settings')) {
                    e.preventDefault();
                    document.getElementById('save-settings').click();
                }
            }

            // Arrow keys for window movement (Ctrl+Arrow)
            if (e.ctrlKey && e.key === 'ArrowLeft') {
                console.log('Ctrl+Left pressed - moving window left');
                e.preventDefault();
                window.electronAPI.moveWindow('left');
            }

            if (e.ctrlKey && e.key === 'ArrowRight') {
                console.log('Ctrl+Right pressed - moving window right');
                e.preventDefault();
                window.electronAPI.moveWindow('right');
            }

            if (e.ctrlKey && e.key === 'ArrowUp') {
                console.log('Ctrl+Up pressed - moving window up');
                e.preventDefault();
                window.electronAPI.moveWindow('up');
            }

            if (e.ctrlKey && e.key === 'ArrowDown') {
                console.log('Ctrl+Down pressed - moving window down');
                e.preventDefault();
                window.electronAPI.moveWindow('down');
            }

            // Ctrl+I for focusing existing input field (ONLY in chatbot mode)
            if (e.ctrlKey && (e.key === 'i' || e.key === 'I')) {
                console.log('Ctrl+I pressed - focusing existing input field');
                console.log('Current mode:', currentMode);
                e.preventDefault();
                if (currentMode === 'chatbot') {
                    if (focusInputField(true)) {
                        console.log('Input field focused successfully with Ctrl+I');
                    } else {
                        console.error('Failed to focus input field with Ctrl+I');
                        addMessage('❌ Input field not found. Please refresh the app.', false);
                    }
                } else {
                    console.log('Ctrl+I only works in chatbot mode. Press Ctrl+C to switch.');
                    addMessage('❌ Ctrl+I only works in chatbot mode. Press Ctrl+C to switch to chatbot mode.', false);
                }
            }

            // Ctrl+? for help (show all keyboard shortcuts)
            if (e.ctrlKey && (e.key === '?' || e.key === '/')) {
                console.log('Ctrl+? pressed - showing help');
                e.preventDefault();
                showKeyboardHelp();
            }
        });

        // Global sendMessage function - removed duplicate

        document.addEventListener('DOMContentLoaded', async () => {
            // Debug: Check for duplicate input fields
            const allInputs = document.querySelectorAll('input[type="text"]');
            console.log('Found input fields:', allInputs.length);
            allInputs.forEach((input, index) => {
                console.log(`Input ${index}:`, input.id, input.placeholder);
            });

            const chatContainer = document.getElementById('chat-container');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const settingsButton = document.getElementById('settings-button');
            const settingsModal = document.getElementById('settings-modal');
            const modalOverlay = document.getElementById('modal-overlay');
            const apiKeyInput = document.getElementById('api-key-input');
            const apiTypeSelect = document.getElementById('api-type-select');
            const saveSettingsButton = document.getElementById('save-settings');

            // Start with chatbot mode by default
            selectMode('chatbot');

            // Add keyboard shortcuts to the EXISTING input field
            messageInput.addEventListener('keydown', (e) => {
                console.log('Key pressed in input field:', e.key, 'Ctrl:', e.ctrlKey);

                // Ctrl+Enter to send message
                if (e.ctrlKey && e.key === 'Enter') {
                    console.log('Ctrl+Enter in input field - sending message');
                    e.preventDefault();
                    if (currentMode === 'chatbot' && messageInput.value.trim()) {
                        sendMessage();
                    }
                }
            });

            // Ctrl+I handler moved to main global event listener to prevent conflicts

            // Load API key
            const loadApiKey = async (type) => {
                const apiKeyResult = await window.electronAPI.getApiKey(type);
                if (apiKeyResult.success && apiKeyResult.key) {
                    apiKey = apiKeyResult.key;
                    apiKeyInput.value = apiKey;
                    console.log(`${type} API key loaded successfully`);
                    return true;
                } else {
                    console.log(`No ${type} API key found or error loading key`);
                    return false;
                }
            };

            // Try to load Gemini API key first since it's working
            if (await loadApiKey('gemini')) {
                apiType = 'gemini';
                apiTypeSelect.value = 'gemini';
            } else if (await loadApiKey('qwen')) {
                apiType = 'qwen';
                apiTypeSelect.value = 'qwen';
            } else if (await loadApiKey('openai')) {
                apiType = 'openai';
                apiTypeSelect.value = 'openai';
            }

            // Settings modal
            settingsButton.addEventListener('click', () => {
                settingsModal.style.display = 'block';
                modalOverlay.style.display = 'block';
            });

            modalOverlay.addEventListener('click', () => {
                settingsModal.style.display = 'none';
                modalOverlay.style.display = 'none';
            });

            apiTypeSelect.addEventListener('change', () => {
                apiType = apiTypeSelect.value;
                loadApiKey(apiType);
            });

            // Add keyboard shortcuts for settings modal
            apiKeyInput.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    console.log('Ctrl+Enter in settings - saving');
                    e.preventDefault();
                    saveSettingsButton.click();
                }
            });

            saveSettingsButton.addEventListener('click', async () => {
                apiKey = apiKeyInput.value.trim();
                const result = await window.electronAPI.saveApiKey(apiKey, apiType);

                if (result.success) {
                    addMessage(`${apiType.toUpperCase()} API key saved successfully. You can now chat with the AI.`, false);
                    console.log(`${apiType} API key saved successfully`);
                } else {
                    addMessage(`Error saving API key: ${result.error}`, false);
                    console.error(`Error saving ${apiType} API key:`, result.error);
                }

                settingsModal.style.display = 'none';
                modalOverlay.style.display = 'none';
            });

            // Create handleChatMessage function for global access
            window.handleChatMessage = async function (message) {
                try {
                    // Notify main process
                    await window.electronAPI.sendMessage(message);

                    // Check if API key is set
                    if (!apiKey) {
                        hideTypingIndicator();
                        addMessage(`Please set your ${apiType.toUpperCase()} API key in the settings (⚙️) to use the AI chat functionality.`, false);
                        return;
                    }

                    console.log(`Making ${apiType} API request with key:`, apiKey.substring(0, 3) + "..." + apiKey.substring(apiKey.length - 3));

                    let reply;

                    if (apiType === 'qwen') {
                        console.log("Making Qwen API request with model: qwen/qwen3-32b:free");

                        // Make Qwen API call with the correct model name
                        const response = await fetch("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${apiKey.trim()}`
                            },
                            body: JSON.stringify({
                                model: "qwen/qwen3-32b:free",
                                input: {
                                    messages: conversationHistory.map(msg => ({
                                        role: msg.role,
                                        content: msg.content
                                    }))
                                }
                            })
                        });

                        const data = await response.json();
                        console.log("Qwen API response status:", response.status);

                        if (!response.ok) {
                            console.error("Qwen API error details:", data);
                            throw new Error(data.message || data.code || `API error: ${response.status}`);
                        }

                        reply = data.output?.text || "No response.";
                    } else if (apiType === 'gemini') {
                        // Make Gemini API call with the correct model name
                        console.log("Making Gemini API request with model: gemini-1.5-flash");
                        console.log("Full URL:", "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey.substring(0, 10) + "...");

                        const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                contents: [
                                    {
                                        parts: [
                                            { text: message }
                                        ]
                                    }
                                ]
                            })
                        });

                        const data = await response.json();
                        console.log("Gemini API response status:", response.status);

                        if (!response.ok) {
                            throw new Error(data.error?.message || `API error: ${response.status}`);
                        }

                        reply = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response.";
                    } else {
                        // Make OpenAI API call
                        const response = await fetch("https://api.openai.com/v1/chat/completions", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${apiKey}`
                            },
                            body: JSON.stringify({
                                model: "gpt-3.5-turbo",
                                messages: conversationHistory
                            })
                        });

                        const data = await response.json();
                        console.log("OpenAI API response status:", response.status);

                        if (!response.ok) {
                            throw new Error(data.error?.message || `API error: ${response.status}`);
                        }

                        reply = data.choices?.[0]?.message?.content || "No response.";
                    }

                    // Add to conversation history
                    conversationHistory.push({ role: "assistant", content: reply });

                    hideTypingIndicator();
                    addMessage(reply, false);
                } catch (error) {
                    console.error("Error in sendMessage:", error);
                    hideTypingIndicator();
                    addMessage(`Error: ${error.message}`, false);
                } finally {
                    hideTypingIndicator();
                }
            };

            async function sendMessage() {
                const message = messageInput.value.trim();
                if (!message) return;

                addMessage(message, true);
                messageInput.value = '';

                // Add to conversation history
                conversationHistory.push({ role: "user", content: message });

                // Show typing indicator
                showTypingIndicator();

                try {
                    // Notify main process
                    await window.electronAPI.sendMessage(message);

                    // Check if API key is set
                    if (!apiKey) {
                        hideTypingIndicator();
                        addMessage(`Please set your ${apiType.toUpperCase()} API key in the settings (⚙️) to use the AI chat functionality.`, false);
                        return;
                    }

                    console.log(`Making ${apiType} API request with key:`, apiKey.substring(0, 3) + "..." + apiKey.substring(apiKey.length - 3));

                    let reply;

                    if (apiType === 'qwen') {
                        console.log("Making Qwen API request with model: qwen/qwen3-32b:free");

                        // Make Qwen API call with the correct model name
                        const response = await fetch("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${apiKey.trim()}`
                            },
                            body: JSON.stringify({
                                model: "qwen/qwen3-32b:free",
                                input: {
                                    messages: conversationHistory.map(msg => ({
                                        role: msg.role,
                                        content: msg.content
                                    }))
                                }
                            })
                        });

                        const data = await response.json();
                        console.log("Qwen API response status:", response.status);

                        if (!response.ok) {
                            console.error("Qwen API error details:", data);
                            throw new Error(data.message || data.code || `API error: ${response.status}`);
                        }

                        reply = data.output?.text || "No response.";
                    } else if (apiType === 'gemini') {
                        // Make Gemini API call with the correct model name
                        console.log("Making Gemini API request with model: gemini-1.5-flash");
                        console.log("Full URL:", "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey.substring(0, 10) + "...");

                        const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                contents: [
                                    {
                                        parts: [
                                            { text: message }
                                        ]
                                    }
                                ]
                            })
                        });

                        const data = await response.json();
                        console.log("Gemini API response status:", response.status);

                        if (!response.ok) {
                            throw new Error(data.error?.message || `API error: ${response.status}`);
                        }

                        reply = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response.";
                    } else {
                        // Make OpenAI API call
                        const response = await fetch("https://api.openai.com/v1/chat/completions", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${apiKey}`
                            },
                            body: JSON.stringify({
                                model: "gpt-3.5-turbo",
                                messages: conversationHistory
                            })
                        });

                        const data = await response.json();
                        console.log("OpenAI API response status:", response.status);

                        if (!response.ok) {
                            throw new Error(data.error?.message || `API error: ${response.status}`);
                        }

                        reply = data.choices?.[0]?.message?.content || "No response.";
                    }

                    console.log("Got reply:", reply.substring(0, 50) + "...");

                    // Add to conversation history
                    conversationHistory.push({ role: "assistant", content: reply });

                    // Keep history to a reasonable size
                    if (conversationHistory.length > 21) { // system + 10 exchanges
                        conversationHistory = [
                            conversationHistory[0], // Keep system message
                            ...conversationHistory.slice(-20) // Keep last 20 messages
                        ];
                    }

                    addMessage(reply, false);
                } catch (error) {
                    console.error("Error in sendMessage:", error);
                    hideTypingIndicator();
                    addMessage(`Error: ${error.message}`, false);
                } finally {
                    hideTypingIndicator();
                }
            }

            // Send message when button is clicked
            sendButton.addEventListener('click', sendMessage);

            // Send message when Enter key is pressed (only in chatbot mode)
            messageInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter' && currentMode === 'chatbot') {
                    sendMessage();
                }
            });

            // Keyboard listeners are now global - removed duplicates

            // Mode switching listener
            window.electronAPI.onSwitchMode((mode) => {
                console.log('Mode switch requested:', mode);
                selectMode(mode);
            });

            // 🚀 GLOBAL SHORTCUTS FOR SEAMLESS WORKFLOW - NO WINDOW FOCUS NEEDED!
            window.electronAPI.onGlobalAnalyze(() => {
                console.log('🎯 Global Ctrl+Enter received - analyzing screenshots');
                if (currentMode === 'dsa') {
                    analyzeSelectedScreenshots();
                } else {
                    console.log('Global analysis only works in DSA mode');
                }
            });

            window.electronAPI.onGlobalMCQFast(() => {
                console.log('🎯 Global MCQ fast mode received');
                if (currentMode === 'dsa') {
                    processMCQFastMode();
                } else {
                    console.log('Global MCQ fast mode only works in DSA mode');
                }
            });

            // Screenshot functionality (using global variables now)

            // analyzeSelectedScreenshots is now a global function

            window.electronAPI.onScreenshotTaken((data) => {
                console.log('Screenshot taken:', data);
                if (currentMode === 'dsa') {
                    // Add screenshot to chat and store in array
                    addScreenshotToChat(data.path, data.timestamp);
                    allScreenshots.push(data.path);
                    selectedScreenshot = data.path; // Keep for backward compatibility
                    console.log('Total screenshots:', allScreenshots.length);

                    // 🔧 AUTO-ADD TO DEBUG SESSION IF DEBUG MODE IS ACTIVE
                    if (debugMode) {
                        addToDebugSession(data.path, 'auto');
                        console.log('🔧 Screenshot automatically added to debug session');
                    }

                    // 🤖 CHECK FOR SMART DEBUG MODE ACTIVATION
                    setTimeout(() => {
                        checkSmartDebugActivation();
                    }, 500); // Small delay to ensure screenshot is processed

                    // 🚀 AUTO-SELECT LATEST SCREENSHOT FOR SEAMLESS WORKFLOW
                    setTimeout(() => {
                        // Get all screenshots
                        const screenshots = document.querySelectorAll('.message img[src^="file://"]');

                        if (screenshots.length > 0) {
                            // Clear all previous selections
                            screenshots.forEach(img => {
                                img.style.border = '2px solid transparent';
                                img.classList.remove('screenshot-selected');
                            });

                            // Select the latest (last) screenshot
                            const latestScreenshot = screenshots[screenshots.length - 1];
                            latestScreenshot.style.border = '2px solid #4299e1';
                            latestScreenshot.classList.add('screenshot-selected');
                            selectedScreenshot = normalizeFilePath(latestScreenshot.src);
                        }
                    }, 300);
                }
            });

            function addScreenshotToChat(imagePath, timestamp) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('message', 'bot-message');

                const img = document.createElement('img');
                img.src = `file://${imagePath}`;
                img.style.maxWidth = '300px';
                img.style.maxHeight = '200px';
                img.style.cursor = 'pointer';
                img.style.border = '2px solid transparent';
                img.style.borderRadius = '5px';
                img.title = 'Click to select, then press Ctrl+Enter to analyze';

                // Click to select screenshot
                img.addEventListener('click', () => {
                    // Remove selection from other screenshots
                    document.querySelectorAll('.screenshot-selected').forEach(el => {
                        el.style.border = '2px solid transparent';
                        el.classList.remove('screenshot-selected');
                    });

                    // Select this screenshot
                    img.style.border = '2px solid #4299e1';
                    img.classList.add('screenshot-selected');
                    selectedScreenshot = imagePath;
                    console.log('Screenshot selected:', imagePath);
                });

                // 🚀 AUTO-SELECT THIS SCREENSHOT IMMEDIATELY FOR SEAMLESS WORKFLOW
                setTimeout(() => {
                    // Clear all previous selections
                    document.querySelectorAll('.screenshot-selected').forEach(el => {
                        el.style.border = '2px solid transparent';
                        el.classList.remove('screenshot-selected');
                    });

                    // Auto-select this new screenshot
                    img.style.border = '2px solid #4299e1';
                    img.classList.add('screenshot-selected');
                    selectedScreenshot = imagePath;
                }, 150);

                const timeText = document.createElement('div');
                timeText.style.fontSize = '12px';
                timeText.style.color = '#a0aec0';
                timeText.style.marginTop = '5px';
                timeText.textContent = `Screenshot taken at ${new Date(timestamp).toLocaleTimeString()}`;

                messageElement.appendChild(img);
                messageElement.appendChild(timeText);

                document.getElementById('chat-container').appendChild(messageElement);
                document.getElementById('chat-container').scrollTop = document.getElementById('chat-container').scrollHeight;
            }

            // analyzeDSAProblemsMultiple is now a global function - removed duplicate

            async function analyzeDSAProblem(base64Image) {
                try {
                    // Don't show typing indicator again since it's already shown

                    if (!apiKey) {
                        hideTypingIndicator();
                        addMessage('Please set your API key in the settings (⚙️) to use the DSA analysis functionality.', false);
                        return;
                    }

                    const dsaPrompt = `You are an expert in Data Structures and Algorithms (DSA). Analyze the screenshot provided and:

1. **Identify** if there are any coding problems, algorithm questions, or DSA challenges visible in the image
2. **Extract** the problem statement, constraints, and examples if present
3. **Provide** a complete solution with:
   - Problem analysis and approach
   - Time and space complexity
   - Clean, well-commented code in ${getLanguageName(selectedLanguage)}
   - Step-by-step explanation of the solution
   - Alternative approaches if applicable

If no DSA problem is found, explain what you see in the image and suggest how it might relate to programming or algorithms.

IMPORTANT: Provide ALL code solutions ONLY in ${getLanguageName(selectedLanguage)} programming language.
Please be thorough and educational in your response.`;

                    let reply;

                    if (apiType === 'gemini') {
                        console.log("Making Gemini API request for DSA analysis");

                        const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" + apiKey, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                contents: [
                                    {
                                        parts: [
                                            { text: dsaPrompt },
                                            {
                                                inline_data: {
                                                    mime_type: "image/png",
                                                    data: base64Image
                                                }
                                            }
                                        ]
                                    }
                                ]
                            })
                        });

                        const data = await response.json();
                        console.log("Gemini API response status:", response.status);

                        if (!response.ok) {
                            throw new Error(data.error?.message || `API error: ${response.status}`);
                        }

                        reply = data.candidates?.[0]?.content?.parts?.[0]?.text || "No analysis available.";
                    } else {
                        // For other APIs, we'll use text-only analysis
                        reply = "DSA analysis with image is currently only supported with Gemini API. Please switch to Gemini in settings or describe the problem in text.";
                    }

                    hideTypingIndicator();
                    addMessage(reply, false);

                } catch (error) {
                    console.error("Error in DSA analysis:", error);
                    hideTypingIndicator();
                    addMessage(`Error analyzing DSA problem: ${error.message}`, false);
                }
            }

            // Welcome messages will be added when mode is selected

            // Start with chatbot mode by default
            selectMode('chatbot');

            // Ensure input field is focused after initial setup
            setTimeout(() => {
                if (currentMode === 'chatbot') {
                    focusInputField(false); // No visual feedback on startup
                    console.log('Initial focus set on app startup');
                }
            }, 500); // Longer delay to ensure everything is loaded
        });
        // 🎤 SIMPLE INTERVIEW COPILOT FUNCTIONS

        // Simple start listening function with Deepgram
        async function startSimpleListening() {
            console.log('🎤 Starting Deepgram listening...');

            // Update UI
            document.getElementById('start-listen-btn').disabled = true;
            document.getElementById('stop-listen-btn').disabled = false;
            document.getElementById('listen-status').textContent = '🌊 Initializing Deepgram...';
            document.getElementById('listen-status').style.color = '#00BCD4';

            // Reset transcript
            interviewMode.transcribedText = '';
            interviewMode.isListening = true;

            try {
                // Get Deepgram API key
                const envResult = await window.electronAPI.getApiKey('deepgram');
                if (!envResult.success || !envResult.key) {
                    throw new Error('Deepgram API key not found in .env file');
                }

                // Get microphone access
                interviewMode.audioStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                // Initialize Deepgram WebSocket
                const deepgramUrl = `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&smart_format=true&interim_results=false&endpointing=300`;
                interviewMode.deepgramSocket = new WebSocket(deepgramUrl, ['token', envResult.key]);

                interviewMode.deepgramSocket.onopen = () => {
                    document.getElementById('listen-status').textContent = '🎤 Listening... Speak your question';
                    document.getElementById('listen-status').style.color = '#4CAF50';

                    // Start audio streaming
                    setupSimpleAudioStreaming();
                };

                interviewMode.deepgramSocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);

                        if (data.type === 'Results' && data.channel && data.channel.alternatives && data.channel.alternatives[0]) {
                            const transcript = data.channel.alternatives[0].transcript;

                            if (transcript && transcript.trim().length > 0 && data.is_final) {
                                interviewMode.transcribedText += transcript + ' ';

                                // Update status with transcript
                                document.getElementById('listen-status').textContent = `🎤 Captured: "${interviewMode.transcribedText.trim()}"`;
                            }
                        }
                    } catch (parseError) {
                        console.log('🔇 Deepgram response parsing error suppressed:', parseError.message);
                    }
                };

                interviewMode.deepgramSocket.onerror = (error) => {
                    // Only log Deepgram errors if interview mode is still active
                    if (interviewMode.active) {
                        console.log('🔇 Deepgram WebSocket error:', error);
                        document.getElementById('listen-status').textContent = '❌ Deepgram connection failed';
                        document.getElementById('listen-status').style.color = '#FF5722';

                        // Reset buttons
                        document.getElementById('start-listen-btn').disabled = false;
                        document.getElementById('stop-listen-btn').disabled = true;
                        interviewMode.isListening = false;
                    } else {
                        console.log('🔇 Deepgram error suppressed during mode switch');
                    }
                };

                interviewMode.deepgramSocket.onclose = () => {
                    console.log('Deepgram WebSocket closed');
                };

            } catch (error) {
                // Only log Deepgram startup errors if interview mode is still active
                if (interviewMode.active) {
                    console.log('🔇 Error starting Deepgram:', error.message);
                    document.getElementById('listen-status').textContent = `❌ Error: ${error.message}`;
                    document.getElementById('listen-status').style.color = '#FF5722';

                    // Reset buttons
                    document.getElementById('start-listen-btn').disabled = false;
                    document.getElementById('stop-listen-btn').disabled = true;
                    interviewMode.isListening = false;
                } else {
                    console.log('🔇 Deepgram startup error suppressed during mode switch');
                }
            }
        }

        // Simple stop listening function with Deepgram
        async function stopSimpleListening() {
            console.log('⏹️ Stopping Deepgram listening...');

            // Update UI
            document.getElementById('start-listen-btn').disabled = false;
            document.getElementById('stop-listen-btn').disabled = true;
            document.getElementById('listen-status').textContent = '⚡ Processing your question...';
            document.getElementById('listen-status').style.color = '#FF9800';

            interviewMode.isListening = false;

            // Stop Deepgram connection
            if (interviewMode.deepgramSocket) {
                interviewMode.deepgramSocket.close();
                interviewMode.deepgramSocket = null;
            }

            // Stop audio stream
            if (interviewMode.audioStream) {
                interviewMode.audioStream.getTracks().forEach(track => track.stop());
                interviewMode.audioStream = null;
            }

            // Stop media recorder
            if (interviewMode.mediaRecorder && interviewMode.mediaRecorder.state !== 'inactive') {
                interviewMode.mediaRecorder.stop();
                interviewMode.mediaRecorder = null;
            }

            // Process the transcribed text
            if (interviewMode.transcribedText.trim()) {
                console.log('📝 Transcribed text:', interviewMode.transcribedText);

                // Add user message to chat
                addMessage(interviewMode.transcribedText.trim(), true);

                // Get AI response using Groq
                await getSimpleGroqResponse(interviewMode.transcribedText.trim());

                // Reset status
                document.getElementById('listen-status').textContent = 'Ready to listen for your question';
                document.getElementById('listen-status').style.color = '#888';

            } else {
                console.log('❌ No speech detected');
                document.getElementById('listen-status').textContent = '❌ No speech detected. Try again.';
                document.getElementById('listen-status').style.color = '#FF5722';

                // Reset status after 3 seconds
                setTimeout(() => {
                    document.getElementById('listen-status').textContent = 'Ready to listen for your question';
                    document.getElementById('listen-status').style.color = '#888';
                }, 3000);
            }
        }

        // Setup audio streaming for simple Deepgram
        function setupSimpleAudioStreaming() {
            if (!interviewMode.audioStream || !interviewMode.deepgramSocket) {
                console.log('🔇 Missing audio stream or Deepgram socket - setup skipped');
                return;
            }

            // Create MediaRecorder for streaming to Deepgram
            const options = {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 16000
            };

            try {
                interviewMode.mediaRecorder = new MediaRecorder(interviewMode.audioStream, options);

                interviewMode.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0 && interviewMode.deepgramSocket &&
                        interviewMode.deepgramSocket.readyState === WebSocket.OPEN) {
                        interviewMode.deepgramSocket.send(event.data);
                    }
                };

                // Start recording in small chunks for real-time streaming
                interviewMode.mediaRecorder.start(100); // Send data every 100ms
                console.log('✅ Audio streaming to Deepgram started');

            } catch (error) {
                console.error('Error setting up audio streaming:', error);
                document.getElementById('listen-status').textContent = '❌ Audio streaming failed';
                document.getElementById('listen-status').style.color = '#FF5722';
            }
        }

        // Get Groq response for interview questions
        async function getSimpleGroqResponse(question) {
            try {
                showTypingIndicator();
                console.log('🚀 Getting Groq response for:', question);

                // Get Groq API key using electronAPI
                const envResult = await window.electronAPI.getApiKey('groq');
                if (!envResult.success || !envResult.key) {
                    throw new Error('Groq API key not found in .env file');
                }

                console.log('🔑 Using Groq API key:', envResult.key.substring(0, 10) + '...');

                const requestBody = {
                    model: 'llama-3.1-8b-instant',
                    messages: [
                        {
                            role: 'system',
                            content: `You are an expert interview coach specializing in ALL types of interview questions. Provide professional, confident, and well-structured answers for:

🔧 TECHNICAL QUESTIONS: Programming, algorithms, system design, databases, etc.
🧠 BEHAVIORAL QUESTIONS: "Tell me about a time when...", leadership, teamwork, conflict resolution
📊 SCENARIO-BASED: Business problems, hypothetical situations, case studies
💼 SITUATIONAL: "What would you do if...", decision-making, prioritization
🎯 COMPANY-SPECIFIC: Culture fit, motivation, career goals, why this company
🚀 PROBLEM-SOLVING: Creative thinking, analytical approach, troubleshooting

RESPONSE STYLE:
- Be confident and professional
- Use STAR method for behavioral questions (Situation, Task, Action, Result)
- Provide specific examples when possible
- Keep responses 30-60 seconds when spoken
- Show enthusiasm and positive attitude
- Demonstrate leadership and growth mindset

Always tailor your response to sound natural and authentic while showcasing relevant skills and experience.`
                        },
                        {
                            role: 'user',
                            content: question
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1000
                };

                console.log('📤 Sending request to Groq:', requestBody);

                const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${envResult.key}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('📥 Groq response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ Groq API error details:', errorText);
                    throw new Error(`Groq API error: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ Groq response data:', data);

                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    throw new Error('Invalid response format from Groq API');
                }

                const aiResponse = data.choices[0].message.content;

                hideTypingIndicator();
                addMessage(aiResponse, false);

                console.log('✅ Groq response generated successfully');

            } catch (error) {
                console.error('❌ Error getting Groq response:', error);
                hideTypingIndicator();
                addMessage(`❌ Error generating response: ${error.message}`, false);
            }
        }

    </script>
</body>

</html>