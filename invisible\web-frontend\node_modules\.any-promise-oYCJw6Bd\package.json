{"name": "any-promise", "version": "1.3.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "typings": "index.d.ts", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "https://github.com/kevinbeaty/any-promise"}, "keywords": ["promise", "es6"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"ava": "^0.14.0", "bluebird": "^3.0.0", "es6-promise": "^3.0.0", "is-promise": "^2.0.0", "lie": "^3.0.0", "mocha": "^2.0.0", "native-promise-only": "^0.8.0", "phantomjs-prebuilt": "^2.0.0", "pinkie": "^2.0.0", "promise": "^7.0.0", "q": "^1.0.0", "rsvp": "^3.0.0", "vow": "^0.4.0", "when": "^3.0.0", "zuul": "^3.0.0"}}