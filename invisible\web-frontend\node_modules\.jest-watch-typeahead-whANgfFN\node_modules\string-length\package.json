{"name": "string-length", "version": "5.0.1", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": "sindresorhus/string-length", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=12.20"}, "type": "module", "exports": "./index.js", "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}