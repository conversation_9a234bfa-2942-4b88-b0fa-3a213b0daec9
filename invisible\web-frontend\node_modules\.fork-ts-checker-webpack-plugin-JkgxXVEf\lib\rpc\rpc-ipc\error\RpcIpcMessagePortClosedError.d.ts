import { RpcMessagePortClosedError } from '../../error/RpcMessagePortClosedError';
declare class RpcIpcMessagePortClosedError extends RpcMessagePortClosedError {
    readonly code?: string | number | null | undefined;
    readonly signal?: string | null | undefined;
    constructor(message: string, code?: string | number | null | undefined, signal?: string | null | undefined);
}
export { RpcIpcMessagePortClosedError };
