{"name": "case-sensitive-paths-webpack-plugin", "version": "2.4.0", "description": "Enforces module path case sensitivity in Webpack", "engines": {"node": ">=4"}, "main": "index.js", "scripts": {"test": "mocha test/", "lint": "eslint index.js", "lintfix": "eslint --fix index.js test/index.js", "version": "auto-changelog -p && git add CHANGELOG.md"}, "repository": {"type": "git", "url": "git+https://github.com/Urthen/case-sensitive-paths-webpack-plugin.git"}, "keywords": ["webpack", "plugin", "case sensitive", "import", "require"], "files": ["index.js"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/Urthen/case-sensitive-paths-webpack-plugin/issues"}, "homepage": "https://github.com/Urthen/case-sensitive-paths-webpack-plugin#readme", "devDependencies": {"auto-changelog": "2.2.1", "eslint": "7.20.0", "eslint-config-airbnb-base": "14.2.1", "eslint-plugin-import": "2.22.1", "fs-extra": "9.1.0", "mocha": "8.3.0", "webpack": "5.23.0"}, "auto-changelog": {"commitLimit": false}}