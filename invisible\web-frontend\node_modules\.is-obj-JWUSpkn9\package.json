{"name": "is-obj", "version": "1.0.1", "description": "Check if a value is an object", "license": "MIT", "repository": "sindresorhus/is-obj", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["obj", "object", "is", "check", "test", "type"], "devDependencies": {"ava": "*", "xo": "*"}}