{"version": 3, "sources": ["../src/getPropertyByPath.ts"], "names": ["getPropertyByPath", "source", "path", "Object", "prototype", "hasOwnProperty", "call", "parsed<PERSON><PERSON>", "split", "reduce", "previous", "key", "undefined"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAT,CACEC,MADF,EAEEC,IAFF,EAGW;AACT,MACE,OAAOA,IAAP,KAAgB,QAAhB,IACAC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCL,MAArC,EAA6CC,IAA7C,CAFF,EAGE;AACA,WAAOD,MAAM,CAACC,IAAD,CAAb;AACD;;AAED,QAAMK,UAAU,GAAG,OAAOL,IAAP,KAAgB,QAAhB,GAA2BA,IAAI,CAACM,KAAL,CAAW,GAAX,CAA3B,GAA6CN,IAAhE,CARS,CAST;;AACA,SAAOK,UAAU,CAACE,MAAX,CAAkB,CAACC,QAAD,EAAgBC,GAAhB,KAAiC;AACxD,QAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1B,aAAOF,QAAP;AACD;;AACD,WAAOA,QAAQ,CAACC,GAAD,CAAf;AACD,GALM,EAKJV,MALI,CAAP;AAMD", "sourcesContent": ["// Resolves property names or property paths defined with period-delimited\n// strings or arrays of strings. Property names that are found on the source\n// object are used directly (even if they include a period).\n// Nested property names that include periods, within a path, are only\n// understood in array paths.\nfunction getPropertyByPath(\n  source: { [key: string]: unknown },\n  path: string | Array<string>,\n): unknown {\n  if (\n    typeof path === 'string' &&\n    Object.prototype.hasOwnProperty.call(source, path)\n  ) {\n    return source[path];\n  }\n\n  const parsedPath = typeof path === 'string' ? path.split('.') : path;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return parsedPath.reduce((previous: any, key): unknown => {\n    if (previous === undefined) {\n      return previous;\n    }\n    return previous[key];\n  }, source);\n}\n\nexport { getPropertyByPath };\n"], "file": "getPropertyByPath.js"}