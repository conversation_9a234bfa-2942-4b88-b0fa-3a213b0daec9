{"name": "invisible-app", "version": "1.0.0", "description": "An application with an invisible window and chatbot", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --win", "build-portable": "electron-builder --win portable", "build-stealth": "electron-builder --win portable", "build-simple": "electron-builder --win nsis", "build-all": "electron-builder --win --x64 --ia32"}, "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"dotenv": "^16.3.1"}, "build": {"appId": "com.invisible.assessment-tool", "productName": "Invisible Assessment Tool", "directories": {"output": "dist"}, "forceCodeSigning": false, "files": ["main.js", "index.html", "preload.js", "package.json", "node_modules/dotenv/**/*", "screenshots/.gitkeep", "temp/.gitkeep", "cache/.gitkeep", "!screenshots/*.png", "!screenshots/*.jpg", "!screenshots/*.jpeg", "!temp/*", "!cache/*", "!dist/**/*", "!.git/**/*"], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Invisible Assessment Tool"}, "portable": {"artifactName": "InvisibleAssessmentTool-Portable-${arch}.exe"}, "extraMetadata": {"name": "System Calculator", "productName": "System Calculator"}}}