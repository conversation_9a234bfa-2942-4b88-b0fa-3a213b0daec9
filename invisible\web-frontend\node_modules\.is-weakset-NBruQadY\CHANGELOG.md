# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.4](https://github.com/inspect-js/is-weakset/compare/v2.0.3...v2.0.4) - 2024-12-16

### Commits

- [types] use shared config [`5fe9848`](https://github.com/inspect-js/is-weakset/commit/5fe98485c31c8269b90fe93b6f0d002259510786)
- [actions] split out node 10-20, and 20+ [`bd400b9`](https://github.com/inspect-js/is-weakset/commit/bd400b94a77eddeea29f940a2e18708f760deaab)
- [<PERSON>] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@types/object-inspect`, `auto-changelog`, `object-inspect`, `tape` [`8b290fc`](https://github.com/inspect-js/is-weakset/commit/8b290fc32cdd37e4464a2f3df192e71bf77a4636)
- [Refactor] use `call-bound` directly [`265971b`](https://github.com/inspect-js/is-weakset/commit/265971b6c1cf9d01b4c48b7182e13b4d46e4825a)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`f39dc78`](https://github.com/inspect-js/is-weakset/commit/f39dc787ab85cd24f62013f1dd416e2f3bf2197a)
- [Dev Deps] update `@arethetypeswrong/cli`, `@types/get-intrinsic`, `object-inspect` [`ce6c6a9`](https://github.com/inspect-js/is-weakset/commit/ce6c6a936bbd40743021b6b96835d328dd924e1b)
- [Deps] update `call-bind`, `get-intrinsic` [`ebd5d82`](https://github.com/inspect-js/is-weakset/commit/ebd5d822ca3b8479ebebc99d8f268a7c2a264f8e)
- [Tests] replace `aud` with `npm audit` [`3eb16c8`](https://github.com/inspect-js/is-weakset/commit/3eb16c8b9e9b368f1e2a342867e10972e81b194b)
- [Dev Deps] update `@arethetypeswrong/cli` [`9fe99f3`](https://github.com/inspect-js/is-weakset/commit/9fe99f3ebc084129576e4f467df8eef834b2f25f)
- [Dev Deps] add missing peer dep [`a2fc30e`](https://github.com/inspect-js/is-weakset/commit/a2fc30ed83a6592c6ac56b425ebadc311dc96102)

## [v2.0.3](https://github.com/inspect-js/is-weakset/compare/v2.0.2...v2.0.3) - 2024-03-08

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`e70d6aa`](https://github.com/inspect-js/is-weakset/commit/e70d6aa49d997930d3d88103090279ca1e480c7d)
- add types [`c9bbc35`](https://github.com/inspect-js/is-weakset/commit/c9bbc35f4d87cfa206281ddf6eb0e595f7994b7e)
- [readme] remove dead badges [`fb443f6`](https://github.com/inspect-js/is-weakset/commit/fb443f66e34a71a4d4ff41e09429f7479c9f4895)
- [actions] remove redundant finisher [`eb292cc`](https://github.com/inspect-js/is-weakset/commit/eb292cc1c056725c1a9c7d5861c3bca03734710e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es5-shim`, `object-inspect`, `tape` [`49d0c35`](https://github.com/inspect-js/is-weakset/commit/49d0c3583793fa2f097b66198a3a33dc2846c659)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `es6-shim`, `npmignore`, `object-inspect`, `tape` [`6ec0a57`](https://github.com/inspect-js/is-weakset/commit/6ec0a5720c92ac3624283580d4af58a1b8846f43)
- [actions] update rebase action to use reusable workflow [`d996166`](https://github.com/inspect-js/is-weakset/commit/d9961664d6beb649e3ea8ee9b80309a0b60252fa)
- [Deps] update `call-bind`, `get-intrinsic` [`e207da3`](https://github.com/inspect-js/is-weakset/commit/e207da3865a658e83c1e9f453edfc5c52e63ccc3)
- [meta] add missing `engines.node` [`4d9dd14`](https://github.com/inspect-js/is-weakset/commit/4d9dd14f6919c969d7e6b8378d3aae2a7ea78a8f)
- [Deps] update `get-intrinsic` [`cf796dd`](https://github.com/inspect-js/is-weakset/commit/cf796dd7e71ea08abb81332f244ae3ffd34bffd5)
- [meta] add `sideEffects` flag [`c88a25d`](https://github.com/inspect-js/is-weakset/commit/c88a25df1f14630d937e730e75fd6b182356fc0b)

## [v2.0.2](https://github.com/inspect-js/is-weakset/compare/v2.0.1...v2.0.2) - 2021-12-12

### Commits

- [actions] reuse common workflows [`a8f7c7f`](https://github.com/inspect-js/is-weakset/commit/a8f7c7fa22088dabbadf82cd52cf962eca646c59)
- [Tests] migrate tests to Github Actions [`f38af72`](https://github.com/inspect-js/is-weakset/commit/f38af729300d425360caad1763e7f904dcd3e393)
- [Refactor] use `call-bind` and `get-intrinsic` to be more robust [`5102e7e`](https://github.com/inspect-js/is-weakset/commit/5102e7ef227f06da1bc8dcee2579af74f1e1a477)
- [meta] do not publish github action workflow files [`6ac6e8e`](https://github.com/inspect-js/is-weakset/commit/6ac6e8e5b15181e73d30f6d37e41955372b07792)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`304af52`](https://github.com/inspect-js/is-weakset/commit/304af52f4c40743b055e252d50c8e804cac4054f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es6-shim`, `object-inspect`, `tape` [`b82fb5f`](https://github.com/inspect-js/is-weakset/commit/b82fb5fafdeea05f93420e0966fe63785362649a)
- [Tests] run `nyc` on all tests; use `tape` runner [`89e2611`](https://github.com/inspect-js/is-weakset/commit/89e26115ab1aa58b37816d6b5e2aad62508bd79c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim`, `object-inspect`, `safe-publish-latest`, `tape` [`42b0bdc`](https://github.com/inspect-js/is-weakset/commit/42b0bdc5a8785ddb589ea16629c933ec01359ca8)
- [actions] update codecov uploader [`112697a`](https://github.com/inspect-js/is-weakset/commit/112697aaf3b6f2e22275575ce4b684059fa5dcaa)
- [actions] add "Allow Edits" workflow [`1af6ffe`](https://github.com/inspect-js/is-weakset/commit/1af6ffe2793a5784ac70048e50850f4d6e650de4)
- [readme] remove travis badge [`dff769b`](https://github.com/inspect-js/is-weakset/commit/dff769b367aa4886ab082dd6330cbc54d0dcf03f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`4494ced`](https://github.com/inspect-js/is-weakset/commit/4494cedea891e3617768b30721e3b5ddee5c41e4)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim`, `tape` [`a2c11c6`](https://github.com/inspect-js/is-weakset/commit/a2c11c6dcef990b7f6fabb26d58837a7fe4f3a3e)
- [Tests] add `core-js` tests [`cd619e9`](https://github.com/inspect-js/is-weakset/commit/cd619e95f64cc02cbec8f4b0b29a806f371eab9e)
- [readme] add actions and codecov badges [`d3cbefe`](https://github.com/inspect-js/is-weakset/commit/d3cbefeb526773a565eb4e501b2e7da7947b215d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`3d54035`](https://github.com/inspect-js/is-weakset/commit/3d54035e2ca66969f6cc779b85902ac3507d7297)
- [Dev Deps] update `auto-changelog`, `eslint` [`a80fb4a`](https://github.com/inspect-js/is-weakset/commit/a80fb4a7c1a90a2929d80f9b2a9adaa56c94d2d2)
- [actions] switch Automatic Rease workflow to `pull_request_target` event [`b3b8aee`](https://github.com/inspect-js/is-weakset/commit/b3b8aeeb3e133d88da897d42530aea4bcc729b23)
- [Dev Deps] update `es5-shim`, `tape` [`5ba5ca8`](https://github.com/inspect-js/is-weakset/commit/5ba5ca84a3d4bb4acacb9fd9265a21476d4f0457)
- [meta] use `prepublishOnly` script for npm 7+ [`b4f7636`](https://github.com/inspect-js/is-weakset/commit/b4f76366574ac4b4d854c330cbad33a8d9ff48ff)
- [Dev Deps] update `auto-changelog`; add `aud` [`2ccd594`](https://github.com/inspect-js/is-weakset/commit/2ccd5944c8fd161fa463620de268bd6f40ff0e59)
- [Fix] when `WeakSet` lacks a `has`, return false [`53a2cbc`](https://github.com/inspect-js/is-weakset/commit/53a2cbce11d2493b4ff82132f3d14e22c909b541)
- [Tests] only audit prod deps [`f74aaf5`](https://github.com/inspect-js/is-weakset/commit/f74aaf5746fc49d424742184025288d0d565639c)
- [meta] normalize line endings [`31f60a6`](https://github.com/inspect-js/is-weakset/commit/31f60a6a70e38851743e602e30bb0907cd3cc6ba)

## [v2.0.1](https://github.com/inspect-js/is-weakset/compare/v2.0.0...v2.0.1) - 2019-12-17

### Fixed

- [Refactor] avoid top-level return, because babel and webpack are broken [`#79`](https://github.com/inspect-js/node-deep-equal/issues/79) [`#78`](https://github.com/inspect-js/node-deep-equal/issues/78) [`#7`](https://github.com/es-shims/Promise.allSettled/issues/7) [`#12`](https://github.com/airbnb/js-shims/issues/12)

### Commits

- [actions] add automatic rebasing / merge commit blocking [`d85eb2c`](https://github.com/inspect-js/is-weakset/commit/d85eb2ca5fe1f1890a04c5504e4c23d68db68447)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`790128b`](https://github.com/inspect-js/is-weakset/commit/790128b8e7c2abe39f70a5c25a303646f8555487)
- [Dev Deps] update `tape` [`e4bda71`](https://github.com/inspect-js/is-weakset/commit/e4bda71a8a6b1233285e91f54a05a08b75cdbd6e)

## [v2.0.0](https://github.com/inspect-js/is-weakset/compare/v1.0.1...v2.0.0) - 2019-11-12

### Commits

- Initial commit [`095ce1f`](https://github.com/inspect-js/is-weakset/commit/095ce1f56c52aa547b57dd326e9b5c2c8a7c2765)
- Tests [`2e8f26d`](https://github.com/inspect-js/is-weakset/commit/2e8f26d1b632fbfe4ded276d046e34276780671b)
- implementation [`acae1ef`](https://github.com/inspect-js/is-weakset/commit/acae1ef8d29a84ff0729135ac4acfe42f18c1328)
- readme [`344db89`](https://github.com/inspect-js/is-weakset/commit/344db8951568a3206847e7b00820622c2364e1ff)
- npm init [`e318679`](https://github.com/inspect-js/is-weakset/commit/e318679acc2c3c168a32fb648ddf3d54ff3e6d5e)
- [meta] add `funding` field; create `FUNDING.yml` [`a1e9277`](https://github.com/inspect-js/is-weakset/commit/a1e927798405e643e570a43d0ee30f5ae16d9d18)
- [meta] add `safe-publish-latest`, `auto-changelog` [`066a08c`](https://github.com/inspect-js/is-weakset/commit/066a08cd939ec1efe433af23688f8c73d3524b5c)
- [Tests] add `npm run lint` [`6af0730`](https://github.com/inspect-js/is-weakset/commit/6af07301fda27f1450184f31b941cf9fbefe261d)
- [Tests] use shared travis-ci configs [`a44f4ec`](https://github.com/inspect-js/is-weakset/commit/a44f4ec03d734274e351acef37698272f3e500c1)
- Only apps should have lockfiles [`11e4115`](https://github.com/inspect-js/is-weakset/commit/11e41153e46eb3ead4be9187770fe8cb47a21e12)
- [Tests] add `npx aud` in `posttest` [`53ceba1`](https://github.com/inspect-js/is-weakset/commit/53ceba16b0a98f968e40439f7bd2ffc98a406de8)

## [v1.0.1](https://github.com/inspect-js/is-weakset/compare/v1.0.0...v1.0.1) - 2015-06-03

### Commits

- Tweaks [`cb3a689`](https://github.com/inspect-js/is-weakset/commit/cb3a68985d734632423ffe81704500bd04e95934)
- Add `related` section to readme [`7c2766b`](https://github.com/inspect-js/is-weakset/commit/7c2766b3e1992b34d5ad933f2cf8901352aa4fcd)

## v1.0.0 - 2015-02-18

### Commits

- init [`579f442`](https://github.com/inspect-js/is-weakset/commit/579f442c42afa4e3880f9f62b3ccea79e0b6edd5)
