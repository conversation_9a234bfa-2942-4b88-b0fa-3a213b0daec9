{"name": "detect-newline", "version": "3.1.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": "sindresorhus/detect-newline", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}