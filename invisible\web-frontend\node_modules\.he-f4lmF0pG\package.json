{"name": "he", "version": "1.2.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": "bin/he", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": "https://github.com/mathiasbynens/he/issues", "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-cli": "^1.3.1", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "regexgen": "^1.3.0", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}}